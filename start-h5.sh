#!/bin/bash

echo "🚀 启动馆拼拼H5前端..."

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Node.js未安装，请先安装Node.js"
    exit 1
fi

# 检查npm是否安装
if ! command -v npm &> /dev/null; then
    echo "❌ npm未安装，请先安装npm"
    exit 1
fi

# 进入H5前端目录
cd frontend

# 检查是否已安装依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装H5前端依赖..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败"
        exit 1
    fi
fi

echo "🔧 启动Vite开发服务器..."
echo ""
echo "📱 H5前端地址: http://localhost:5173"
echo "🌐 网络地址: http://*************:5173"
echo ""
echo "💡 提示："
echo "   - 可以在手机浏览器中访问网络地址查看移动端效果"
echo "   - 按 Ctrl+C 停止服务"
echo ""

# 启动开发服务器
npm run dev
