# 观品品展会平台

一个基于Vue3 + Node.js的展会信息平台，支持微信H5和管理后台。

## 🚀 项目特性

### 前端H5 (Vue3 + Vant)
- 🔐 微信授权登录
- 👤 用户角色管理（设计师/工厂/参展商）
- 🔍 智能搜索（地点→展会→公司级联搜索）
- ❤️ 收藏功能
- 📱 响应式设计，完美适配移动端

### 管理后台 (Vue3 + Element Plus)
- 📊 数据统计面板
- 🏢 展会管理
- 🏭 展馆管理
- 🏷️ 分类管理
- 👥 用户管理
- 🎯 参展商管理

### 后端API (Node.js + Express)
- 🔑 JWT身份认证
- 🗄️ MySQL数据库
- 📝 完整的CRUD接口
- 🔍 高性能搜索
- 📊 数据统计

## 🛠️ 技术栈

### 前端
- **框架**: Vue 3 + Composition API
- **UI组件**: Vant (H5) / Element Plus (管理后台)
- **路由**: Vue Router 4
- **状态管理**: Pinia
- **构建工具**: Vite
- **样式**: SCSS

### 后端
- **运行时**: Node.js
- **框架**: Express.js
- **数据库**: MySQL
- **ORM**: Sequelize
- **认证**: JWT
- **日志**: Winston

## 📦 项目结构

```
guanpinpin/
├── frontend/          # H5前端
├── admin/            # 管理后台
├── backend/          # 后端API
├── database/         # 数据库脚本
├── docs/            # 项目文档
└── README.md
```

## 🚀 快速开始

### 环境要求
- Node.js >= 16
- MySQL >= 8.0
- npm 或 yarn

### 1. 克隆项目
```bash
git clone https://gitee.com/leiyuyh/guanpinpin.git
cd guanpinpin
```

### 2. 数据库配置
```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE guanpinpin;

# 导入初始数据
mysql -u root -p guanpinpin < database/init.sql
mysql -u root -p guanpinpin < database/seed.sql
```

### 3. 后端配置
```bash
cd backend
npm install

# 复制环境配置
cp .env.example .env
# 编辑 .env 文件，配置数据库连接信息

# 启动后端服务
npm start
```

### 4. 前端H5配置
```bash
cd frontend
npm install
npm run dev
```

### 5. 管理后台配置
```bash
cd admin
npm install
npm run dev
```

## 📱 访问地址

- **H5前端**: http://localhost:5173
- **管理后台**: http://localhost:5174
- **后端API**: http://localhost:3001

## 🔧 开发指南

### 数据库迁移
```bash
cd backend
node src/scripts/migrate.js
```

### 数据填充
```bash
cd backend
node src/scripts/seed.js
```

### API文档
后端API遵循RESTful设计，主要接口：

- `POST /api/auth/wechat-login` - 微信登录
- `GET /api/search/recent-exhibitions` - 获取近期展会
- `GET /api/search/design-companies` - 搜索设计公司
- `GET /api/search/factories` - 搜索工厂
- `POST /api/favorite` - 添加收藏

## 🎯 核心功能

### 搜索逻辑
1. **地点选择** → 自动加载该地点的展会
2. **展会选择** → 根据展会ID查找参展公司
3. **关键字搜索** → 在结果中进一步筛选

### 用户角色
- **设计师**: 寻找工厂合作
- **工厂**: 展示产品和服务
- **参展商**: 参与展会推广

## 📄 许可证

MIT License

## 👥 贡献

欢迎提交Issue和Pull Request！

## 📞 联系方式

- 邮箱: <EMAIL>
- 项目地址: https://gitee.com/leiyuyh/guanpinpin
