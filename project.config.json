{"miniprogramRoot": "miniprogram/", "cloudfunctionRoot": "cloudfunctions/", "setting": {"urlCheck": false, "es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": true, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "enableEngineNative": false, "useIsolateContext": true, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "disableUseStrict": false, "minifyWXML": true, "showES6CompileOption": false, "useCompilerPlugins": false, "compileWorklet": false, "localPlugins": false, "condition": false, "swc": false, "disableSWC": true}, "appid": "wxaa87bd18d1cf1d1e", "projectname": "guanpinpin", "libVersion": "2.19.4", "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {}, "cloudbaseRoot": "cloudfunctions/", "cloudfunctionTemplateRoot": "cloudfunctionTemplate/", "compileType": "miniprogram", "packOptions": {"ignore": [], "include": []}, "editorSetting": {}}