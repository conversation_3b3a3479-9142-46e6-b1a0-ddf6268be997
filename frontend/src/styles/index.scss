// 全局样式文件

// CSS变量定义
:root {
  // 主色调
  --primary-color: #1989fa;
  --primary-light: #66b1ff;
  --primary-dark: #0960bd;

  // 辅助色
  --success-color: #07c160;
  --warning-color: #ff976a;
  --danger-color: #ee0a24;
  --info-color: #909399;

  // 中性色
  --text-primary: #323233;
  --text-regular: #646566;
  --text-secondary: #969799;
  --text-placeholder: #c8c9cc;

  // 背景色
  --bg-primary: #ffffff;
  --bg-secondary: #f7f8fa;
  --bg-tertiary: #ebedf0;

  // 边框色
  --border-color: #ebedf0;
  --border-light: #f2f3f5;

  // 间距
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 20px;
  --spacing-xxl: 24px;

  // 阴影
  --shadow-light: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.15);
  --shadow-heavy: 0 8px 16px rgba(0, 0, 0, 0.2);

  // 圆角
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
}

// 重置样式
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--bg-secondary);
}

// 通用类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

// 星级评分样式
.star-rating {
  color: #ffd21e;
  font-size: 14px;
}

// 卡片样式
.card {
  background: var(--bg-primary);
  border-radius: 8px;
  padding: var(--spacing-lg);
  margin: var(--spacing-sm) var(--spacing-lg);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

// 列表项样式
.list-item {
  background: var(--bg-primary);
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  
  &:last-child {
    border-bottom: none;
  }
}

// 安全区域适配
.safe-area-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

// 页面布局
.page {
  min-height: 100vh;
  background-color: var(--bg-secondary);

  &.has-tabbar {
    padding-bottom: 50px;
  }
}

.page-header {
  background: var(--bg-primary);
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);

  .title {
    font-size: 18px;
    font-weight: bold;
    color: var(--text-primary);
    margin: 0;
  }

  .subtitle {
    font-size: 14px;
    color: var(--text-secondary);
    margin: 4px 0 0 0;
  }
}

.page-content {
  padding: var(--spacing-lg);
}

// 搜索栏样式
.search-bar {
  background: var(--bg-primary);
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);

  .van-search {
    padding: 0;
    background: transparent;
  }
}

// 筛选栏样式
.filter-bar {
  background: var(--bg-primary);
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  gap: var(--spacing-md);
  overflow-x: auto;

  .filter-item {
    flex-shrink: 0;
    padding: 6px 12px;
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    font-size: 14px;
    color: var(--text-regular);
    border: 1px solid var(--border-color);
    cursor: pointer;

    &.active {
      background: var(--primary-color);
      color: white;
      border-color: var(--primary-color);
    }
  }
}

// 公司卡片样式
.company-card {
  background: var(--bg-primary);
  margin: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-light);

  .company-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);

    .company-info {
      flex: 1;

      .company-name {
        font-size: 16px;
        font-weight: bold;
        color: var(--text-primary);
        margin-bottom: 4px;
      }

      .company-type {
        font-size: 12px;
        padding: 2px 8px;
        border-radius: var(--radius-sm);
        background: var(--bg-secondary);
        color: var(--text-secondary);
        display: inline-block;
      }
    }

    .company-actions {
      display: flex;
      gap: var(--spacing-sm);
    }
  }

  .company-details {
    .detail-item {
      display: flex;
      align-items: center;
      margin-bottom: 6px;
      font-size: 14px;
      color: var(--text-regular);

      .icon {
        margin-right: 6px;
        color: var(--text-secondary);
      }
    }

    .company-tags {
      margin-top: var(--spacing-md);
      display: flex;
      flex-wrap: wrap;
      gap: 6px;

      .tag {
        font-size: 12px;
        padding: 2px 6px;
        background: var(--bg-tertiary);
        color: var(--text-secondary);
        border-radius: var(--radius-sm);
      }
    }
  }

  .company-footer {
    margin-top: var(--spacing-md);
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--border-light);
    display: flex;
    justify-content: space-between;
    align-items: center;

    .rating {
      display: flex;
      align-items: center;
      gap: 4px;

      .stars {
        color: #ffd21e;
      }

      .level {
        font-size: 14px;
        color: var(--text-secondary);
      }
    }

    .status {
      font-size: 12px;
      padding: 2px 8px;
      border-radius: var(--radius-sm);

      &.available {
        background: #f0f9ff;
        color: var(--success-color);
      }

      &.full {
        background: #fef2f2;
        color: var(--danger-color);
      }
    }
  }
}

// 展会卡片样式
.exhibition-card {
  background: var(--bg-primary);
  margin: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-light);

  .exhibition-header {
    margin-bottom: var(--spacing-md);

    .exhibition-name {
      font-size: 16px;
      font-weight: bold;
      color: var(--text-primary);
      margin-bottom: 4px;
    }

    .exhibition-category {
      font-size: 12px;
      padding: 2px 8px;
      border-radius: var(--radius-sm);
      background: var(--primary-color);
      color: white;
      display: inline-block;
    }
  }

  .exhibition-info {
    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 6px;
      font-size: 14px;
      color: var(--text-regular);

      .icon {
        margin-right: 6px;
        color: var(--text-secondary);
      }
    }
  }
}

// 底部导航栏样式
.custom-tabbar {
  .van-tabbar-item {
    &--active {
      color: var(--primary-color);
    }
  }
}

// 加载更多样式
.load-more {
  padding: var(--spacing-lg);
  text-align: center;
  color: var(--text-secondary);
  font-size: 14px;
}

// 空状态样式
.empty-state {
  padding: 60px var(--spacing-lg);
  text-align: center;

  .empty-icon {
    font-size: 48px;
    color: var(--text-placeholder);
    margin-bottom: var(--spacing-lg);
  }

  .empty-text {
    font-size: 16px;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
  }

  .empty-desc {
    font-size: 14px;
    color: var(--text-placeholder);
  }
}
