import request from './request'

/**
 * 获取我的展会列表
 * @param {Object} params 查询参数
 */
export const getMyExhibitions = (params) => {
  return request({
    url: '/exhibitions/my',
    method: 'get',
    params
  })
}

/**
 * 获取可参加的展会列表
 * @param {Object} params 查询参数
 */
export const getAvailableExhibitions = (params) => {
  return request({
    url: '/exhibitions/available',
    method: 'get',
    params
  })
}

/**
 * 参加展会
 * @param {Object} data 参展数据 { exhibitionId, participantType, isFull }
 */
export const joinExhibition = (data) => {
  return request({
    url: '/exhibitions/join',
    method: 'post',
    data
  })
}

/**
 * 获取展会详情
 * @param {number} id 展会ID
 */
export const getExhibitionDetail = (id) => {
  return request({
    url: `/exhibitions/${id}`,
    method: 'get'
  })
}

/**
 * 退出展会
 * @param {number} exhibitionId 展会ID
 */
export const leaveExhibition = (exhibitionId) => {
  return request({
    url: `/exhibitions/${exhibitionId}/leave`,
    method: 'post'
  })
}

/**
 * 更新参展状态
 * @param {number} participantId 参展记录ID
 * @param {number} isFull 是否满额：0否 1是
 */
export const updateParticipantStatus = (participantId, isFull) => {
  return request({
    url: `/exhibitions/participant/${participantId}/status`,
    method: 'put',
    data: { isFull }
  })
}
