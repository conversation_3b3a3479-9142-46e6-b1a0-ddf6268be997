import request from './request'

/**
 * 微信授权登录
 * @param {string} code 微信授权码
 */
export const login = (code) => {
  return request.post('/auth/wechat-login', { code })
}

/**
 * 获取用户信息
 */
export const getUserProfile = () => {
  return request.get('/user/profile')
}

/**
 * 更新用户信息
 * @param {Object} userInfo 用户信息
 */
export const updateUserProfile = (userInfo) => {
  return request.put('/user/profile', userInfo)
}

/**
 * 完善用户信息
 * @param {Object} profileData 用户资料
 */
export const completeProfile = (profileData) => {
  return request.post('/user/complete-profile', profileData)
}
