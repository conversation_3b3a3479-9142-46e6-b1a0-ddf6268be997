import request from './request'

/**
 * 搜索工厂
 * @param {Object} params 搜索参数
 */
export const searchFactories = (params) => {
  return request.get('/search/factories', { params })
}

/**
 * 搜索设计公司
 * @param {Object} params 搜索参数
 */
export const searchDesignCompanies = (params) => {
  return request.get('/search/design-companies', { params })
}

/**
 * 获取近期展会
 * @param {number} limit 限制数量
 * @param {string} city 城市过滤
 */
export const getRecentExhibitions = (limit = 10, city = null) => {
  const params = { limit }
  if (city) {
    params.city = city
  }
  return request.get('/search/recent-exhibitions', { params })
}

/**
 * 获取展馆列表
 */
export const getVenues = () => {
  return request.get('/search/venues')
}

/**
 * 获取展会详情
 * @param {number} id 展会ID
 */
export const getExhibitionDetail = (id) => {
  return request.get(`/exhibition/${id}`)
}

/**
 * 获取工厂详情
 * @param {number} id 工厂ID
 */
export const getFactoryDetail = (id) => {
  return request.get(`/factory/${id}`)
}

/**
 * 获取设计公司详情
 * @param {number} id 设计公司ID
 */
export const getDesignCompanyDetail = (id) => {
  return request.get(`/design-company/${id}`)
}
