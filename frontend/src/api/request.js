import axios from 'axios'
import { showToast } from 'vant'
import { useUserStore } from '@/stores/user'

// 获取API基础URL
const getBaseURL = () => {
  // 调试信息
  console.log('环境信息:', {
    MODE: import.meta.env.MODE,
    PROD: import.meta.env.PROD,
    DEV: import.meta.env.DEV,
    VITE_API_BASE_URL: import.meta.env.VITE_API_BASE_URL
  })

  // 生产环境使用自定义域名
  if (import.meta.env.PROD) {
    const apiUrl = import.meta.env.VITE_API_BASE_URL + '/api' || 'https://api.leiyumail.xyz/api'
    console.log('生产环境API地址:', apiUrl)
    return apiUrl
  }

  // 开发环境使用代理
  console.log('开发环境API地址: /api')
  return '/api'
}

// 创建axios实例
const request = axios.create({
  baseURL: getBaseURL(),
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    const userStore = useUserStore()
    
    // 添加token到请求头
    if (userStore.token) {
      config.headers.Authorization = `Bearer ${userStore.token}`
    }
    
    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    const { code, message, data } = response.data
    
    // 请求成功
    if (code === 200) {
      return response.data
    }
    
    // 业务错误
    showToast(message || '请求失败')
    return Promise.reject(new Error(message || '请求失败'))
  },
  (error) => {
    console.error('响应拦截器错误:', error)
    
    if (error.response) {
      const { status, data } = error.response
      
      // 401 未授权，清除登录状态
      if (status === 401) {
        const userStore = useUserStore()
        userStore.logout()
        showToast('登录已过期，请重新登录')
        return Promise.reject(new Error('登录已过期'))
      }
      
      // 其他HTTP错误
      const message = data?.message || `请求失败 (${status})`
      showToast(message)
      return Promise.reject(new Error(message))
    }
    
    // 网络错误
    if (error.code === 'NETWORK_ERROR' || error.message === 'Network Error') {
      showToast('网络连接失败，请检查网络')
      return Promise.reject(new Error('网络连接失败'))
    }
    
    // 超时错误
    if (error.code === 'ECONNABORTED') {
      showToast('请求超时，请重试')
      return Promise.reject(new Error('请求超时'))
    }
    
    // 其他错误
    showToast('请求失败，请重试')
    return Promise.reject(error)
  }
)

export default request
