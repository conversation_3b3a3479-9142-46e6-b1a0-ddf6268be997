import request from './request'

/**
 * 获取收藏列表
 * @param {Object} params 查询参数
 */
export const getFavorites = (params) => {
  return request.get('/favorite', { params })
}

/**
 * 添加收藏
 * @param {Object} data 收藏数据
 */
export const addFavorite = (data) => {
  return request.post('/favorite', data)
}

/**
 * 取消收藏
 * @param {number} id 收藏ID
 */
export const removeFavorite = (id) => {
  return request.delete(`/favorite/${id}`)
}

/**
 * 检查收藏状态
 * @param {number} type 收藏类型
 * @param {number} id 收藏对象ID
 */
export const checkFavoriteStatus = (type, id) => {
  return request.get(`/favorite/check/${type}/${id}`)
}
