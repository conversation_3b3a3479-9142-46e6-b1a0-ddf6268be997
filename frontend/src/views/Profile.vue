<template>
  <div class="page has-tabbar">
    <!-- 用户信息头部 -->
    <div class="profile-header">
      <div class="user-avatar">
        <van-icon name="user-circle-o" />
      </div>
      <div class="user-info">
        <h2 class="user-name">{{ userInfo?.nickname || '用户' }}</h2>
        <p class="user-type">{{ getUserTypeName() }}</p>
        <p class="user-phone" v-if="userInfo?.phone">{{ userInfo.phone }}</p>
      </div>
      <div class="profile-actions">
        <van-button size="small" @click="goToMyInfo">
          编辑资料
        </van-button>
      </div>
    </div>

    <!-- 功能菜单 -->
    <div class="menu-section">
      <van-cell-group>
        <van-cell
          title="我的信息"
          icon="user-o"
          is-link
          @click="goToMyInfo"
        />
        <van-cell
          title="我的展会"
          icon="calendar-o"
          is-link
          @click="goToMyExhibitions"
        />
        <van-cell
          title="我的收藏"
          icon="star-o"
          is-link
          @click="goToMyFavorites"
        />
        <van-cell
          title="意见反馈"
          icon="chat-o"
          is-link
          @click="goToFeedback"
        />
        <van-cell
          title="关于我们"
          icon="info-o"
          is-link
          @click="goToAbout"
        />
      </van-cell-group>
    </div>

    <!-- 公司信息（如果有） -->
    <div v-if="userInfo?.companyInfo" class="company-section">
      <h3 class="section-title">公司信息</h3>
      <div class="company-card">
        <div class="company-header">
          <h4 class="company-name">{{ getCompanyName() }}</h4>
          <span class="company-type">{{ getUserTypeName() }}</span>
        </div>
        <div class="company-details">
          <div class="detail-item" v-if="getCompanyCity()">
            <van-icon name="location-o" />
            <span>{{ getCompanyCity() }}</span>
          </div>
          <div class="detail-item" v-if="getCompanyContact()">
            <van-icon name="manager-o" />
            <span>{{ getCompanyContact() }}</span>
          </div>
          <div class="detail-item" v-if="getCompanyPhone()">
            <van-icon name="phone-o" />
            <span>{{ getCompanyPhone() }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 退出登录 -->
    <div class="logout-section">
      <van-button
        type="danger"
        size="large"
        block
        @click="handleLogout"
      >
        退出登录
      </van-button>
    </div>

    <!-- 底部导航栏 -->
    <van-tabbar route class="custom-tabbar">
      <van-tabbar-item icon="home-o" to="/home">首页</van-tabbar-item>
      <van-tabbar-item icon="brush-o" to="/find-design">找设计</van-tabbar-item>
      <van-tabbar-item icon="shop-o" to="/find-factory">找工厂</van-tabbar-item>
      <van-tabbar-item icon="user-o" to="/profile">我的</van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showDialog, showToast } from 'vant'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()

// 计算属性
const userInfo = computed(() => userStore.userInfo)

// 获取用户类型名称
const getUserTypeName = () => {
  const typeMap = {
    1: '参展商',
    2: '设计公司',
    3: '工厂',
    4: '综合商'
  }
  return typeMap[userInfo.value?.userType] || '用户'
}

// 获取公司名称
const getCompanyName = () => {
  const companyInfo = userInfo.value?.companyInfo
  if (!companyInfo) return ''
  
  if (userInfo.value.userType === 4) {
    // 综合商可能有多个公司信息
    return companyInfo.designCompany?.name || companyInfo.factory?.name || ''
  }
  
  return companyInfo.name || ''
}

// 获取公司城市
const getCompanyCity = () => {
  const companyInfo = userInfo.value?.companyInfo
  if (!companyInfo) return ''
  
  if (userInfo.value.userType === 4) {
    return companyInfo.designCompany?.city || companyInfo.factory?.city || ''
  }
  
  return companyInfo.city || ''
}

// 获取公司联系人
const getCompanyContact = () => {
  const companyInfo = userInfo.value?.companyInfo
  if (!companyInfo) return ''
  
  if (userInfo.value.userType === 4) {
    return companyInfo.designCompany?.contactPerson || companyInfo.factory?.contactPerson || ''
  }
  
  return companyInfo.contactPerson || ''
}

// 获取公司电话
const getCompanyPhone = () => {
  const companyInfo = userInfo.value?.companyInfo
  if (!companyInfo) return ''
  
  if (userInfo.value.userType === 4) {
    return companyInfo.designCompany?.contactPhone || companyInfo.factory?.contactPhone || ''
  }
  
  return companyInfo.contactPhone || ''
}

// 导航方法
const goToMyInfo = () => router.push('/my-info')
const goToMyExhibitions = () => router.push('/my-exhibitions')
const goToMyFavorites = () => router.push('/my-favorites')
const goToFeedback = () => router.push('/feedback')
const goToAbout = () => router.push('/about')

// 退出登录
const handleLogout = () => {
  showDialog({
    title: '确认退出',
    message: '确定要退出登录吗？',
    confirmButtonText: '确定',
    cancelButtonText: '取消'
  }).then(() => {
    userStore.logout()
    router.push('/login')
  }).catch(() => {
    // 用户取消
  })
}

// 页面初始化
onMounted(() => {
  // 如果没有用户信息，尝试获取
  if (!userInfo.value) {
    userStore.fetchUserProfile()
  }
})
</script>

<style lang="scss" scoped>
.profile-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  padding: var(--spacing-xxl) var(--spacing-lg) var(--spacing-lg);
  color: white;
  display: flex;
  align-items: center;
  
  .user-avatar {
    margin-right: var(--spacing-lg);
    
    .van-icon {
      font-size: 60px;
      color: rgba(255, 255, 255, 0.9);
    }
  }
  
  .user-info {
    flex: 1;
    
    .user-name {
      font-size: 20px;
      font-weight: bold;
      margin: 0 0 4px 0;
    }
    
    .user-type {
      font-size: 14px;
      opacity: 0.9;
      margin: 0 0 4px 0;
    }
    
    .user-phone {
      font-size: 12px;
      opacity: 0.8;
      margin: 0;
    }
  }
  
  .profile-actions {
    .van-button {
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;
      
      &:active {
        background: rgba(255, 255, 255, 0.3);
      }
    }
  }
}

.menu-section {
  margin: var(--spacing-lg) 0;
}

.company-section {
  margin: var(--spacing-lg);
  
  .section-title {
    font-size: 16px;
    font-weight: bold;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-md) 0;
  }
  
  .company-card {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-light);
    
    .company-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-md);
      
      .company-name {
        font-size: 16px;
        font-weight: bold;
        color: var(--text-primary);
        margin: 0;
      }
      
      .company-type {
        font-size: 12px;
        padding: 2px 8px;
        background: var(--primary-color);
        color: white;
        border-radius: var(--radius-sm);
      }
    }
    
    .company-details {
      .detail-item {
        display: flex;
        align-items: center;
        margin-bottom: 6px;
        font-size: 14px;
        color: var(--text-regular);
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .van-icon {
          margin-right: 6px;
          color: var(--text-secondary);
        }
      }
    }
  }
}

.logout-section {
  margin: var(--spacing-xxl) var(--spacing-lg) var(--spacing-lg);
}
</style>
