<template>
  <div class="page">
    <!-- 导航栏 -->
    <van-nav-bar
      title="我的收藏"
      left-arrow
      @click-left="goBack"
    />

    <!-- 筛选标签 -->
    <div class="filter-tabs">
      <div
        v-for="tab in filterTabs"
        :key="tab.value"
        :class="['filter-tab', { active: activeFilter === tab.value }]"
        @click="selectFilter(tab.value)"
      >
        {{ tab.label }}
      </div>
    </div>

    <!-- 收藏列表 -->
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
      >
        <div
          v-for="item in favoriteList"
          :key="item.id"
          class="favorite-item"
          @click="goToDetail(item)"
        >
          <div class="item-header">
            <div class="item-info">
              <h3 class="item-name">{{ getItemName(item) }}</h3>
              <span class="item-type">{{ getItemTypeName(item) }}</span>
            </div>
            <div class="item-actions">
              <van-icon
                name="delete-o"
                @click.stop="removeFavorite(item)"
              />
            </div>
          </div>
          
          <div class="item-details">
            <div class="detail-item" v-if="getItemCity(item)">
              <van-icon name="location-o" />
              <span>{{ getItemCity(item) }}</span>
            </div>
            <div class="detail-item" v-if="getItemContact(item)">
              <van-icon name="manager-o" />
              <span>{{ getItemContact(item) }}</span>
            </div>
            <div class="detail-item" v-if="getItemPhone(item)">
              <van-icon name="phone-o" />
              <span>{{ getItemPhone(item) }}</span>
            </div>
          </div>
          
          <!-- 工厂特长标签 -->
          <div v-if="item.type === 'factory' && item.specialties" class="item-tags">
            <span
              v-for="tag in item.specialties.split(',')"
              :key="tag"
              class="tag"
            >
              {{ tag.trim() }}
            </span>
          </div>
          
          <div class="item-footer">
            <div class="item-rating" v-if="getItemLevel(item)">
              <span class="stars">{{ '★'.repeat(getItemLevel(item)) }}</span>
              <span class="level">{{ getItemLevel(item) }}星</span>
            </div>
            <div class="item-time">
              {{ formatTime(item.createdAt) }}
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="!loading && favoriteList.length === 0" class="empty-state">
          <van-icon name="star-o" class="empty-icon" />
          <div class="empty-text">暂无收藏</div>
          <div class="empty-desc">快去收藏您感兴趣的公司吧</div>
        </div>
      </van-list>
    </van-pull-refresh>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showDialog, showToast } from 'vant'
import { getFavorites, removeFavorite as removeFavoriteApi } from '@/api/favorite'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const finished = ref(false)
const refreshing = ref(false)
const favoriteList = ref([])
const activeFilter = ref('')

// 分页参数
const pagination = reactive({
  page: 1,
  pageSize: 10
})

// 筛选标签
const filterTabs = [
  { label: '全部', value: '' },
  { label: '设计公司', value: '2' },
  { label: '工厂', value: '3' },
  { label: '综合商', value: '4' }
]

// 获取收藏列表
const fetchFavorites = async (isRefresh = false) => {
  try {
    if (isRefresh) {
      pagination.page = 1
      finished.value = false
    }

    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize
    }

    if (activeFilter.value) {
      params.type = activeFilter.value
    }

    const response = await getFavorites(params)
    const { list, pagination: paginationInfo } = response.data

    if (isRefresh) {
      favoriteList.value = list
    } else {
      favoriteList.value.push(...list)
    }

    // 检查是否还有更多数据
    if (paginationInfo.page >= paginationInfo.totalPages) {
      finished.value = true
    } else {
      pagination.page++
    }
  } catch (error) {
    showToast(error.message || '获取收藏列表失败')
  }
}

// 下拉刷新
const onRefresh = async () => {
  refreshing.value = true
  await fetchFavorites(true)
  refreshing.value = false
}

// 上拉加载
const onLoad = async () => {
  if (finished.value) return
  loading.value = true
  await fetchFavorites()
  loading.value = false
}

// 选择筛选条件
const selectFilter = (filter) => {
  activeFilter.value = filter
  fetchFavorites(true)
}

// 获取项目名称
const getItemName = (item) => {
  if (item.type === 'comprehensive') {
    return item.designCompany?.name || item.factory?.name || '未知'
  }
  return item.name || '未知'
}

// 获取项目类型名称
const getItemTypeName = (item) => {
  const typeMap = {
    design: '设计公司',
    factory: '工厂',
    comprehensive: '综合商'
  }
  return typeMap[item.type] || '未知'
}

// 获取城市
const getItemCity = (item) => {
  if (item.type === 'comprehensive') {
    return item.designCompany?.city || item.factory?.city
  }
  return item.city
}

// 获取联系人
const getItemContact = (item) => {
  if (item.type === 'comprehensive') {
    return item.designCompany?.contactPerson || item.factory?.contactPerson
  }
  return item.contactPerson
}

// 获取联系电话
const getItemPhone = (item) => {
  if (item.type === 'comprehensive') {
    return item.designCompany?.contactPhone || item.factory?.contactPhone
  }
  return item.contactPhone
}

// 获取等级
const getItemLevel = (item) => {
  if (item.type === 'comprehensive') {
    return item.designCompany?.level || item.factory?.level
  }
  return item.level
}

// 格式化时间
const formatTime = (time) => {
  if (!time) return ''
  const date = new Date(time)
  const now = new Date()
  const diff = now - date
  
  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  if (diff < 2592000000) return `${Math.floor(diff / 86400000)}天前`
  
  return date.toLocaleDateString('zh-CN')
}

// 取消收藏
const removeFavorite = (item) => {
  showDialog({
    title: '确认取消',
    message: `确定要取消收藏"${getItemName(item)}"吗？`,
    confirmButtonText: '确定',
    cancelButtonText: '取消'
  }).then(async () => {
    try {
      await removeFavoriteApi(item.id)
      showToast('取消收藏成功')
      // 从列表中移除
      const index = favoriteList.value.findIndex(fav => fav.id === item.id)
      if (index > -1) {
        favoriteList.value.splice(index, 1)
      }
    } catch (error) {
      showToast(error.message || '取消收藏失败')
    }
  }).catch(() => {
    // 用户取消
  })
}

// 跳转到详情页
const goToDetail = (item) => {
  const type = item.type === 'design' ? 'design' : 'factory'
  router.push(`/company-detail/${type}/${item.favoriteId}`)
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 页面初始化
onMounted(() => {
  fetchFavorites(true)
})
</script>

<style lang="scss" scoped>
.filter-tabs {
  display: flex;
  background: var(--bg-primary);
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  gap: var(--spacing-md);
  
  .filter-tab {
    padding: 6px 12px;
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    font-size: 14px;
    color: var(--text-regular);
    cursor: pointer;
    transition: all 0.3s ease;
    
    &.active {
      background: var(--primary-color);
      color: white;
    }
  }
}

.favorite-item {
  background: var(--bg-primary);
  margin: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-light);
  cursor: pointer;
  
  .item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);
    
    .item-info {
      flex: 1;
      
      .item-name {
        font-size: 16px;
        font-weight: bold;
        color: var(--text-primary);
        margin: 0 0 4px 0;
      }
      
      .item-type {
        font-size: 12px;
        padding: 2px 8px;
        background: var(--bg-secondary);
        color: var(--text-secondary);
        border-radius: var(--radius-sm);
      }
    }
    
    .item-actions {
      .van-icon {
        font-size: 18px;
        color: var(--text-secondary);
        padding: 4px;
        
        &:active {
          color: var(--danger-color);
        }
      }
    }
  }
  
  .item-details {
    margin-bottom: var(--spacing-md);
    
    .detail-item {
      display: flex;
      align-items: center;
      margin-bottom: 6px;
      font-size: 14px;
      color: var(--text-regular);
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .van-icon {
        margin-right: 6px;
        color: var(--text-secondary);
      }
    }
  }
  
  .item-tags {
    margin-bottom: var(--spacing-md);
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    
    .tag {
      font-size: 12px;
      padding: 2px 6px;
      background: var(--bg-tertiary);
      color: var(--text-secondary);
      border-radius: var(--radius-sm);
    }
  }
  
  .item-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .item-rating {
      display: flex;
      align-items: center;
      gap: 4px;
      
      .stars {
        color: #ffd21e;
        font-size: 14px;
      }
      
      .level {
        font-size: 12px;
        color: var(--text-secondary);
      }
    }
    
    .item-time {
      font-size: 12px;
      color: var(--text-placeholder);
    }
  }
}
</style>
