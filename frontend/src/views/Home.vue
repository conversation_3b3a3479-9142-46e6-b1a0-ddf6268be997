<template>
  <div class="page has-tabbar">
    <!-- Banner区域 -->
    <div class="banner-section">
      <div class="logo-area">
        <van-icon name="shop-o" size="40" color="#1989fa" />
        <h1 class="app-title">馆拼拼</h1>
      </div>

      <!-- 搜索表单 -->
      <div class="search-form">
        <div class="search-row">
          <van-field
            v-model="searchForm.cityName"
            label="城市"
            readonly
            is-link
            @click="showCityPicker = true"
          />
          <van-field
            v-model="searchForm.dateName"
            label="日期"
            readonly
            is-link
            @click="handleDateClick"
          />
        </div>

        <div class="search-row">
          <van-field
            v-model="searchForm.venueName"
            label="场馆"
            readonly
            is-link
            @click="handleVenueClick"
          />
          <van-field
            v-model="searchForm.typeName"
            label="找什么"
            readonly
            is-link
            @click="handleTypeClick"
          />
        </div>

        <van-button
          type="primary"
          size="large"
          block
          class="search-button"
          @click="handleSearch"
        >
          开始搜索
        </van-button>
      </div>
    </div>

    <!-- 近期展会 -->
    <div class="section">
      <div class="section-header">
        <h3>近期展会</h3>
        <van-button type="primary" size="small" plain @click="goToExhibitions">
          查看更多
        </van-button>
      </div>
      
      <div class="exhibitions-list">
        <div
          v-for="exhibition in recentExhibitions"
          :key="exhibition.id"
          class="exhibition-card"
          @click="goToExhibitionDetail(exhibition.id)"
        >
          <div class="exhibition-header">
            <div class="exhibition-name">{{ exhibition.name }}</div>
            <div class="exhibition-category">{{ exhibition.category?.name }}</div>
          </div>
          
          <div class="exhibition-info">
            <div class="info-item">
              <van-icon name="location-o" class="icon" />
              <span>{{ exhibition.venue?.name }} - {{ exhibition.venue?.city }}</span>
            </div>
            <div class="info-item">
              <van-icon name="clock-o" class="icon" />
              <span>{{ formatDateRange(exhibition.startDate, exhibition.endDate) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 推荐工厂 -->
    <div class="section">
      <div class="section-header">
        <h3>推荐工厂</h3>
        <van-button type="primary" size="small" plain @click="goToFindFactory">
          查看更多
        </van-button>
      </div>
      
      <div class="companies-list">
        <div
          v-for="factory in recommendFactories"
          :key="factory.id"
          class="company-card"
          @click="goToCompanyDetail('factory', factory.id)"
        >
          <div class="company-header">
            <div class="company-info">
              <div class="company-name">{{ factory.name }}</div>
              <div class="company-type">工厂</div>
            </div>
            <div class="company-actions">
              <van-icon
                name="star"
                :class="{ 'favorited': factory.isFavorited }"
                @click.stop="toggleFavorite(factory)"
              />
            </div>
          </div>
          
          <div class="company-details">
            <div class="detail-item">
              <van-icon name="location-o" class="icon" />
              <span>{{ factory.city }}</span>
            </div>
            <div class="detail-item">
              <van-icon name="phone-o" class="icon" />
              <span>{{ factory.contactPerson }}</span>
            </div>
            <div class="company-tags" v-if="factory.specialties">
              <span
                v-for="tag in factory.specialties.split(',')"
                :key="tag"
                class="tag"
              >
                {{ tag.trim() }}
              </span>
            </div>
          </div>
          
          <div class="company-footer">
            <div class="rating">
              <span class="stars">{{ '★'.repeat(factory.level) }}</span>
              <span class="level">{{ factory.level }}星工厂</span>
            </div>
            <div :class="['status', factory.isFull ? 'full' : 'available']">
              {{ factory.isFull ? '已满额' : '可预约' }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 推荐设计公司 -->
    <div class="section">
      <div class="section-header">
        <h3>推荐设计</h3>
        <van-button type="primary" size="small" plain @click="goToFindDesign">
          查看更多
        </van-button>
      </div>
      
      <div class="companies-list">
        <div
          v-for="company in recommendDesignCompanies"
          :key="company.id"
          class="company-card"
          @click="goToCompanyDetail('design', company.id)"
        >
          <div class="company-header">
            <div class="company-info">
              <div class="company-name">{{ company.name }}</div>
              <div class="company-type">设计公司</div>
            </div>
            <div class="company-actions">
              <van-icon
                name="star"
                :class="{ 'favorited': company.isFavorited }"
                @click.stop="toggleFavorite(company)"
              />
            </div>
          </div>
          
          <div class="company-details">
            <div class="detail-item">
              <van-icon name="location-o" class="icon" />
              <span>{{ company.city }}</span>
            </div>
            <div class="detail-item">
              <van-icon name="phone-o" class="icon" />
              <span>{{ company.contactPerson }}</span>
            </div>
          </div>
          
          <div class="company-footer">
            <div class="rating">
              <span class="stars">{{ '★'.repeat(company.level) }}</span>
              <span class="level">{{ company.level }}星设计</span>
            </div>
            <div :class="['status', company.isFull ? 'full' : 'available']">
              {{ company.isFull ? '已满额' : '可预约' }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部导航栏 -->
    <van-tabbar class="custom-tabbar">
      <van-tabbar-item icon="home-o" @click="handleNavClick('/home')">首页</van-tabbar-item>
      <van-tabbar-item icon="brush-o" @click="handleNavClick('/find-design')">找设计</van-tabbar-item>
      <van-tabbar-item icon="shop-o" @click="handleNavClick('/find-factory')">找工厂</van-tabbar-item>
      <van-tabbar-item icon="user-o" @click="handleNavClick('/profile')">我的</van-tabbar-item>
    </van-tabbar>

    <!-- 城市选择器 -->
    <van-popup v-model:show="showCityPicker" position="bottom">
      <van-picker
        :columns="cityColumns"
        @confirm="onCityConfirm"
        @cancel="showCityPicker = false"
      />
    </van-popup>

    <!-- 日期选择器 -->
    <van-popup v-model:show="showDatePicker" position="bottom">
      <van-date-picker
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
        :min-date="new Date()"
      />
    </van-popup>

    <!-- 场馆选择器 -->
    <van-popup v-model:show="showVenuePicker" position="bottom">
      <van-picker
        v-if="venueColumns.length > 0"
        :columns="venueColumns"
        @confirm="onVenueConfirm"
        @cancel="showVenuePicker = false"
      />
      <div v-else style="padding: 20px; text-align: center;">
        {{ searchForm.cityValue ? '加载场馆中...' : '请先选择城市' }}
      </div>
    </van-popup>

    <!-- 类型选择器 -->
    <van-popup v-model:show="showTypePicker" position="bottom">
      <van-picker
        :columns="typeColumns"
        @confirm="onTypeConfirm"
        @cancel="showTypePicker = false"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { getRecentExhibitions, searchFactories, searchDesignCompanies } from '@/api/search'
import { addFavorite, removeFavorite, checkFavoriteStatus } from '@/api/favorite'
import { getVenues } from '@/api/search'
import { CITY_OPTIONS } from '@/constants/cities'

const router = useRouter()

// 响应式数据
const recentExhibitions = ref([])
const recommendFactories = ref([])
const recommendDesignCompanies = ref([])

// 搜索表单
const searchForm = reactive({
  cityName: '',
  cityValue: '',
  dateName: '',
  dateValue: '',
  venueName: '',
  venueValue: '',
  typeName: '',
  typeValue: ''
})

// 选择器显示状态
const showCityPicker = ref(false)
const showDatePicker = ref(false)
const showVenuePicker = ref(false)
const showTypePicker = ref(false)

// 选择器数据
const cityColumns = CITY_OPTIONS
const venueColumns = ref([])
const typeColumns = [
  { text: '找设计', value: 'design' },
  { text: '找工厂', value: 'factory' }
]

// 获取近期展会
const fetchRecentExhibitions = async () => {
  try {
    const response = await getRecentExhibitions(5)
    recentExhibitions.value = response.data
  } catch (error) {
    console.error('获取近期展会失败:', error)
  }
}

// 获取推荐工厂
const fetchRecommendFactories = async () => {
  try {
    const response = await searchFactories({ page: 1, pageSize: 3 })
    recommendFactories.value = response.data.list
    
    // 检查收藏状态
    for (const factory of recommendFactories.value) {
      try {
        const favoriteResponse = await checkFavoriteStatus(3, factory.id)
        factory.isFavorited = favoriteResponse.data.isFavorited
      } catch (error) {
        factory.isFavorited = false
      }
    }
  } catch (error) {
    console.error('获取推荐工厂失败:', error)
  }
}

// 获取推荐设计公司
const fetchRecommendDesignCompanies = async () => {
  try {
    const response = await searchDesignCompanies({ page: 1, pageSize: 3 })
    recommendDesignCompanies.value = response.data.list
    
    // 检查收藏状态
    for (const company of recommendDesignCompanies.value) {
      try {
        const favoriteResponse = await checkFavoriteStatus(2, company.id)
        company.isFavorited = favoriteResponse.data.isFavorited
      } catch (error) {
        company.isFavorited = false
      }
    }
  } catch (error) {
    console.error('获取推荐设计公司失败:', error)
  }
}

// 格式化日期范围
const formatDateRange = (startDate, endDate) => {
  const start = new Date(startDate).toLocaleDateString('zh-CN', { month: 'numeric', day: 'numeric' })
  const end = new Date(endDate).toLocaleDateString('zh-CN', { month: 'numeric', day: 'numeric' })
  return `${start} - ${end}`
}

// 切换收藏状态
const toggleFavorite = async (company) => {
  try {
    if (company.isFavorited) {
      // 取消收藏 - 这里需要先获取收藏ID
      showToast('取消收藏功能待完善')
    } else {
      // 添加收藏
      const type = company.type === 'factory' ? 3 : 2
      await addFavorite({ type, id: company.id })
      company.isFavorited = true
      showToast('收藏成功')
    }
  } catch (error) {
    showToast(error.message || '操作失败')
  }
}

// 获取场馆列表（根据城市）
const fetchVenues = async (city) => {
  try {
    console.log('开始获取场馆列表，城市:', city)

    if (!city) {
      console.log('城市为空，清空场馆列表')
      venueColumns.value = []
      return
    }

    // 调用API获取指定城市的场馆
    console.log('调用API获取场馆，参数:', { city })
    const response = await getVenues()
    console.log('场馆API响应:', response)

    if (response && response.data) {
      // 过滤出指定城市的场馆
      const filteredVenues = response.data.filter(venue => venue.city === city)
      venueColumns.value = filteredVenues.map(venue => ({
        text: venue.name,
        value: venue.id
      }))
      console.log('场馆选择器数据:', venueColumns.value)
      console.log('找到场馆数量:', venueColumns.value.length)
    } else {
      console.log('API响应数据为空')
      venueColumns.value = []
    }
  } catch (error) {
    console.error('获取场馆列表失败:', error)
    venueColumns.value = []
  }
}

// 选择器确认方法
const onCityConfirm = ({ selectedOptions }) => {
  searchForm.cityName = selectedOptions[0].text
  searchForm.cityValue = selectedOptions[0].value
  showCityPicker.value = false

  // 清空后续选择
  searchForm.dateName = ''
  searchForm.dateValue = ''
  searchForm.venueName = ''
  searchForm.venueValue = ''

  // 根据选择的城市获取场馆列表
  fetchVenues(selectedOptions[0].value)
}

const onDateConfirm = ({ selectedValues }) => {
  // selectedValues 是 [年, 月, 日] 的数组
  const year = selectedValues[0]
  const month = selectedValues[1]
  const day = selectedValues[2]
  const date = new Date(year, month - 1, day) // 月份需要减1

  searchForm.dateName = date.toLocaleDateString('zh-CN')
  searchForm.dateValue = date.toISOString().split('T')[0]
  showDatePicker.value = false

  // 清空场馆选择
  searchForm.venueName = ''
  searchForm.venueValue = ''
}

const onVenueConfirm = ({ selectedOptions }) => {
  searchForm.venueName = selectedOptions[0].text
  searchForm.venueValue = selectedOptions[0].value
  showVenuePicker.value = false
}

const onTypeConfirm = ({ selectedOptions }) => {
  searchForm.typeName = selectedOptions[0].text
  searchForm.typeValue = selectedOptions[0].value
  showTypePicker.value = false
}

// 搜索处理
const handleSearch = () => {
  // 验证必填项
  if (!searchForm.cityValue) {
    showToast('请选择城市')
    return
  }
  if (!searchForm.dateValue) {
    showToast('请选择日期')
    return
  }
  if (!searchForm.venueValue) {
    showToast('请选择场馆')
    return
  }
  if (!searchForm.typeValue) {
    showToast('请选择找什么')
    return
  }

  // 构建搜索参数
  const searchParams = {
    city: searchForm.cityValue,
    date: searchForm.dateValue,
    venueId: searchForm.venueValue,
    type: searchForm.typeValue
  }

  // 根据类型跳转到对应页面
  if (searchForm.typeValue === 'design') {
    router.push({
      path: '/find-design',
      query: searchParams
    })
  } else if (searchForm.typeValue === 'factory') {
    router.push({
      path: '/find-factory',
      query: searchParams
    })
  }
}

const goToExhibitionDetail = (id) => {
  router.push(`/exhibition-detail/${id}`)
}

const goToCompanyDetail = (type, id) => {
  router.push(`/company-detail/${type}/${id}`)
}

// 字段点击处理
const handleDateClick = () => {
  if (!searchForm.cityValue) {
    showToast('请先选择城市')
    return
  }
  showDatePicker.value = true
}

const handleVenueClick = () => {
  if (!searchForm.cityValue) {
    showToast('请先选择城市')
    return
  }

  if (!searchForm.dateValue) {
    showToast('请先选择日期')
    return
  }

  if (venueColumns.value.length === 0) {
    showToast('该城市暂无可用场馆')
    return
  }

  showVenuePicker.value = true
}

const handleTypeClick = () => {
  showTypePicker.value = true
}

// 导航点击处理
const handleNavClick = (path) => {
  router.push(path)
}

// 页面初始化
onMounted(() => {
  fetchRecentExhibitions()
  fetchRecommendFactories()
  fetchRecommendDesignCompanies()
})
</script>

<style lang="scss" scoped>
.banner-section {
  background: linear-gradient(135deg, #1989fa 0%, #1976d2 100%);
  padding: 20px 16px;
  color: white;

  .logo-area {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;

    .app-title {
      font-size: 28px;
      font-weight: bold;
      margin: 0 0 0 12px;
      color: white;
    }
  }

  .search-form {
    background: white;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    .search-row {
      display: flex;
      gap: 8px;
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 16px;
      }

      .van-field {
        flex: 1;
        border: 1px solid #ebedf0;
        border-radius: 6px;

        &:deep(.van-field__label) {
          color: #323233;
          font-weight: 500;
          width: 50px;
        }

        &:deep(.van-field__value) {
          color: #646566;
        }
      }
    }

    .search-button {
      height: 44px;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 500;
    }
  }
}

.section {
  margin-bottom: var(--spacing-lg);
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 var(--spacing-lg);
    margin-bottom: var(--spacing-md);
    
    h3 {
      font-size: 16px;
      font-weight: bold;
      color: var(--text-primary);
      margin: 0;
    }
  }
}

.favorited {
  color: #ffd21e !important;
}
</style>
