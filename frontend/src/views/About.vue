<template>
  <div class="page">
    <!-- 导航栏 -->
    <van-nav-bar
      title="关于我们"
      left-arrow
      @click-left="goBack"
    />

    <div class="about-content">
      <!-- 应用信息 -->
      <div class="app-info">
        <div class="app-logo">
          <van-icon name="shop-o" />
        </div>
        <h1 class="app-name">馆拼拼</h1>
        <p class="app-version">版本 1.0.0</p>
        <p class="app-desc">专业的展会服务平台，连接参展商、设计公司和搭建工厂</p>
      </div>

      <!-- 功能介绍 -->
      <div class="feature-section">
        <h3 class="section-title">主要功能</h3>
        <div class="feature-list">
          <div class="feature-item">
            <van-icon name="brush-o" />
            <div class="feature-content">
              <h4>找设计</h4>
              <p>快速找到专业的展台设计公司</p>
            </div>
          </div>
          
          <div class="feature-item">
            <van-icon name="shop-o" />
            <div class="feature-content">
              <h4>找工厂</h4>
              <p>寻找可靠的展台搭建工厂</p>
            </div>
          </div>
          
          <div class="feature-item">
            <van-icon name="star-o" />
            <div class="feature-content">
              <h4>收藏管理</h4>
              <p>收藏感兴趣的公司，方便后续联系</p>
            </div>
          </div>
          
          <div class="feature-item">
            <van-icon name="calendar-o" />
            <div class="feature-content">
              <h4>展会信息</h4>
              <p>获取最新的展会资讯和参展信息</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 联系我们 -->
      <div class="contact-section">
        <h3 class="section-title">联系我们</h3>
        <van-cell-group>
          <van-cell
            title="客服电话"
            value="************"
            icon="phone-o"
            @click="makeCall('************')"
          />
          <van-cell
            title="客服邮箱"
            value="<EMAIL>"
            icon="envelop-o"
            @click="sendEmail('<EMAIL>')"
          />
          <van-cell
            title="官方网站"
            value="www.guanpinpin.com"
            icon="globe-o"
            @click="openWebsite('https://www.guanpinpin.com')"
          />
        </van-cell-group>
      </div>

      <!-- 法律信息 -->
      <div class="legal-section">
        <h3 class="section-title">法律信息</h3>
        <van-cell-group>
          <van-cell
            title="用户协议"
            is-link
            @click="openUserAgreement"
          />
          <van-cell
            title="隐私政策"
            is-link
            @click="openPrivacyPolicy"
          />
        </van-cell-group>
      </div>

      <!-- 版权信息 -->
      <div class="copyright">
        <p>© 2024 馆拼拼 版权所有</p>
        <p>上海某某科技有限公司</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { showDialog, showToast } from 'vant'

const router = useRouter()

// 拨打电话
const makeCall = (phone) => {
  showDialog({
    title: '拨打电话',
    message: `确定要拨打 ${phone} 吗？`,
    confirmButtonText: '拨打',
    cancelButtonText: '取消'
  }).then(() => {
    window.location.href = `tel:${phone}`
  }).catch(() => {
    // 用户取消
  })
}

// 发送邮件
const sendEmail = (email) => {
  window.location.href = `mailto:${email}`
}

// 打开网站
const openWebsite = (url) => {
  window.open(url, '_blank')
}

// 打开用户协议
const openUserAgreement = () => {
  showToast('用户协议页面开发中')
}

// 打开隐私政策
const openPrivacyPolicy = () => {
  showToast('隐私政策页面开发中')
}

// 返回上一页
const goBack = () => {
  router.back()
}
</script>

<style lang="scss" scoped>
.about-content {
  padding: var(--spacing-lg);
  padding-bottom: var(--spacing-xxl);
}

.app-info {
  text-align: center;
  padding: var(--spacing-xxl) 0;
  
  .app-logo {
    margin-bottom: var(--spacing-lg);
    
    .van-icon {
      font-size: 80px;
      color: var(--primary-color);
    }
  }
  
  .app-name {
    font-size: 28px;
    font-weight: bold;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
  }
  
  .app-version {
    font-size: 14px;
    color: var(--text-secondary);
    margin: 0 0 var(--spacing-md) 0;
  }
  
  .app-desc {
    font-size: 16px;
    color: var(--text-regular);
    line-height: 1.5;
    margin: 0;
  }
}

.feature-section,
.contact-section,
.legal-section {
  margin-bottom: var(--spacing-xxl);
  
  .section-title {
    font-size: 16px;
    font-weight: bold;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-lg) 0;
  }
}

.feature-list {
  .feature-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-lg);
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-md);
    box-shadow: var(--shadow-light);
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .van-icon {
      font-size: 24px;
      color: var(--primary-color);
      margin-right: var(--spacing-lg);
    }
    
    .feature-content {
      flex: 1;
      
      h4 {
        font-size: 16px;
        font-weight: bold;
        color: var(--text-primary);
        margin: 0 0 4px 0;
      }
      
      p {
        font-size: 14px;
        color: var(--text-secondary);
        margin: 0;
        line-height: 1.4;
      }
    }
  }
}

.copyright {
  text-align: center;
  padding-top: var(--spacing-xxl);
  border-top: 1px solid var(--border-light);
  
  p {
    font-size: 12px;
    color: var(--text-placeholder);
    margin: 4px 0;
    line-height: 1.4;
  }
}
</style>
