<template>
  <div class="page">
    <!-- 导航栏 -->
    <van-nav-bar
      :title="pageTitle"
      left-arrow
      @click-left="goBack"
    />

    <div v-if="loading" class="loading-container">
      <van-loading type="spinner" />
    </div>

    <div v-else-if="companyInfo" class="company-detail">
      <!-- 公司基本信息 -->
      <div class="company-header">
        <div class="company-main-info">
          <h1 class="company-name">{{ companyInfo.name }}</h1>
          <div class="company-meta">
            <span class="company-type">{{ getCompanyTypeName() }}</span>
            <div class="rating">
              <span class="stars">{{ '★'.repeat(companyInfo.level || 1) }}</span>
              <span class="level">{{ companyInfo.level || 1 }}星</span>
            </div>
          </div>
        </div>
        
        <div class="company-actions">
          <van-button
            :icon="isFavorited ? 'star' : 'star-o'"
            :type="isFavorited ? 'warning' : 'default'"
            size="small"
            @click="toggleFavorite"
          >
            {{ isFavorited ? '已收藏' : '收藏' }}
          </van-button>
        </div>
      </div>

      <!-- 联系信息 -->
      <div class="info-section">
        <h3 class="section-title">联系信息</h3>
        <div class="info-list">
          <div class="info-item" v-if="companyInfo.contactPerson">
            <van-icon name="manager-o" />
            <span class="label">联系人：</span>
            <span class="value">{{ companyInfo.contactPerson }}</span>
          </div>
          
          <div class="info-item" v-if="companyInfo.contactPhone">
            <van-icon name="phone-o" />
            <span class="label">联系电话：</span>
            <span class="value">{{ companyInfo.contactPhone }}</span>
            <van-button
              type="primary"
              size="mini"
              @click="makeCall(companyInfo.contactPhone)"
            >
              拨打
            </van-button>
          </div>
          
          <div class="info-item" v-if="companyInfo.city">
            <van-icon name="location-o" />
            <span class="label">所在城市：</span>
            <span class="value">{{ companyInfo.city }}</span>
          </div>
        </div>
      </div>

      <!-- 工厂特长（仅工厂显示） -->
      <div v-if="type === 'factory' && companyInfo.specialties" class="info-section">
        <h3 class="section-title">专业特长</h3>
        <div class="specialties-tags">
          <span
            v-for="specialty in companyInfo.specialties.split(',')"
            :key="specialty"
            class="specialty-tag"
          >
            {{ specialty.trim() }}
          </span>
        </div>
      </div>

      <!-- 参展信息 -->
      <div class="info-section">
        <h3 class="section-title">参展信息</h3>
        <div class="exhibition-status">
          <div :class="['status-badge', companyInfo.isFull ? 'full' : 'available']">
            {{ companyInfo.isFull ? '已满额' : '可预约' }}
          </div>
          <p class="status-desc">
            {{ companyInfo.isFull ? '当前档期已满，可联系了解后续安排' : '当前有空档期，欢迎咨询合作' }}
          </p>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <van-button
          type="primary"
          size="large"
          block
          @click="contactCompany"
        >
          立即联系
        </van-button>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else class="error-state">
      <van-icon name="warning-o" class="error-icon" />
      <div class="error-text">加载失败</div>
      <van-button type="primary" @click="fetchCompanyDetail">重试</van-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast, showDialog } from 'vant'
import { getFactoryDetail, getDesignCompanyDetail } from '@/api/search'
import { addFavorite, removeFavorite, checkFavoriteStatus as checkFavoriteAPI } from '@/api/favorite'

const route = useRoute()
const router = useRouter()

// 路由参数
const type = route.params.type // 'factory' 或 'design'
const id = route.params.id

// 响应式数据
const loading = ref(true)
const companyInfo = ref(null)
const isFavorited = ref(false)

// 计算属性
const pageTitle = computed(() => {
  if (type === 'factory') return '工厂详情'
  if (type === 'design') return '设计公司详情'
  return '公司详情'
})

// 获取公司类型名称
const getCompanyTypeName = () => {
  if (type === 'factory') {
    return companyInfo.value?.type === 'comprehensive' ? '综合商' : '工厂'
  }
  if (type === 'design') {
    return companyInfo.value?.type === 'comprehensive' ? '综合商' : '设计公司'
  }
  return '公司'
}

// 获取公司详情
const fetchCompanyDetail = async () => {
  try {
    loading.value = true
    
    let response
    if (type === 'factory') {
      response = await getFactoryDetail(id)
    } else if (type === 'design') {
      response = await getDesignCompanyDetail(id)
    } else {
      throw new Error('无效的公司类型')
    }
    
    companyInfo.value = response.data
    
    // 检查收藏状态
    await checkFavoriteStatus()
  } catch (error) {
    showToast(error.message || '获取公司详情失败')
  } finally {
    loading.value = false
  }
}

// 检查收藏状态
const checkFavoriteStatus = async () => {
  try {
    const favoriteType = type === 'factory' ? 3 : 2
    const response = await checkFavoriteAPI(favoriteType, id)
    isFavorited.value = response.data.isFavorited
  } catch (error) {
    isFavorited.value = false
  }
}

// 切换收藏状态
const toggleFavorite = async () => {
  try {
    if (isFavorited.value) {
      // 取消收藏 - 这里需要先获取收藏ID，暂时提示
      showToast('取消收藏功能待完善')
    } else {
      // 添加收藏
      const favoriteType = type === 'factory' ? 3 : 2
      await addFavorite({ type: favoriteType, id: parseInt(id) })
      isFavorited.value = true
      showToast('收藏成功')
    }
  } catch (error) {
    showToast(error.message || '操作失败')
  }
}

// 拨打电话
const makeCall = (phone) => {
  showDialog({
    title: '拨打电话',
    message: `确定要拨打 ${phone} 吗？`,
    confirmButtonText: '拨打',
    cancelButtonText: '取消'
  }).then(() => {
    window.location.href = `tel:${phone}`
  }).catch(() => {
    // 用户取消
  })
}

// 联系公司
const contactCompany = () => {
  if (companyInfo.value?.contactPhone) {
    makeCall(companyInfo.value.contactPhone)
  } else {
    showToast('暂无联系电话')
  }
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 页面初始化
onMounted(() => {
  fetchCompanyDetail()
})
</script>

<style lang="scss" scoped>
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.company-detail {
  padding-bottom: 80px;
}

.company-header {
  background: var(--bg-primary);
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  
  .company-main-info {
    flex: 1;
    
    .company-name {
      font-size: 20px;
      font-weight: bold;
      color: var(--text-primary);
      margin: 0 0 var(--spacing-sm) 0;
    }
    
    .company-meta {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      
      .company-type {
        font-size: 12px;
        padding: 4px 8px;
        background: var(--primary-color);
        color: white;
        border-radius: var(--radius-sm);
      }
      
      .rating {
        display: flex;
        align-items: center;
        gap: 4px;
        
        .stars {
          color: #ffd21e;
          font-size: 14px;
        }
        
        .level {
          font-size: 12px;
          color: var(--text-secondary);
        }
      }
    }
  }
  
  .company-actions {
    flex-shrink: 0;
  }
}

.info-section {
  background: var(--bg-primary);
  margin-top: var(--spacing-sm);
  padding: var(--spacing-lg);
  
  .section-title {
    font-size: 16px;
    font-weight: bold;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-md) 0;
  }
  
  .info-list {
    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: var(--spacing-md);
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .van-icon {
        margin-right: var(--spacing-sm);
        color: var(--text-secondary);
      }
      
      .label {
        color: var(--text-secondary);
        margin-right: var(--spacing-sm);
      }
      
      .value {
        flex: 1;
        color: var(--text-primary);
      }
      
      .van-button {
        margin-left: var(--spacing-sm);
      }
    }
  }
}

.specialties-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  
  .specialty-tag {
    padding: 6px 12px;
    background: var(--bg-secondary);
    color: var(--text-regular);
    border-radius: var(--radius-md);
    font-size: 14px;
  }
}

.exhibition-status {
  .status-badge {
    display: inline-block;
    padding: 6px 12px;
    border-radius: var(--radius-md);
    font-size: 14px;
    font-weight: bold;
    margin-bottom: var(--spacing-sm);
    
    &.available {
      background: #f0f9ff;
      color: var(--success-color);
    }
    
    &.full {
      background: #fef2f2;
      color: var(--danger-color);
    }
  }
  
  .status-desc {
    color: var(--text-secondary);
    font-size: 14px;
    margin: 0;
    line-height: 1.5;
  }
}

.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: var(--spacing-lg);
  background: var(--bg-primary);
  border-top: 1px solid var(--border-color);
  z-index: 100;
}

.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  
  .error-icon {
    font-size: 48px;
    color: var(--text-placeholder);
    margin-bottom: var(--spacing-lg);
  }
  
  .error-text {
    font-size: 16px;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
  }
}
</style>
