<template>
  <div class="page">
    <!-- 导航栏 -->
    <van-nav-bar title="选择身份" />

    <div class="user-type-content">
      <!-- 标题说明 -->
      <div class="header-section">
        <h2>请选择您的身份</h2>
        <p>选择最符合您需求的身份类型，以便为您提供更精准的服务</p>
      </div>

      <!-- 身份选择 -->
      <div class="type-options">
        <div
          v-for="option in userTypeOptions"
          :key="option.value"
          :class="['type-option', { active: selectedType === option.value }]"
          @click="selectType(option.value)"
        >
          <div class="option-icon">
            <van-icon :name="option.icon" />
          </div>
          <div class="option-content">
            <h3 class="option-title">{{ option.title }}</h3>
            <p class="option-desc">{{ option.description }}</p>
          </div>
          <div class="option-check">
            <van-icon
              :name="selectedType === option.value ? 'checked' : 'circle'"
              :color="selectedType === option.value ? '#1989fa' : '#c8c9cc'"
            />
          </div>
        </div>
      </div>

      <!-- 确认按钮 -->
      <div class="action-section">
        <van-button
          type="primary"
          size="large"
          block
          :disabled="!selectedType"
          :loading="loading"
          @click="handleConfirm"
        >
          确认选择
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'

const router = useRouter()

// 响应式数据
const selectedType = ref('')
const loading = ref(false)

// 用户类型选项
const userTypeOptions = [
  {
    value: 1,
    title: '参展商',
    description: '我需要参加展会，寻找设计公司和工厂合作',
    icon: 'shop-o'
  },
  {
    value: 2,
    title: '设计公司',
    description: '我是设计公司，为参展商提供展台设计服务',
    icon: 'brush-o'
  },
  {
    value: 3,
    title: '工厂',
    description: '我是搭建工厂，为参展商提供展台搭建服务',
    icon: 'shop-collect-o'
  },
  {
    value: 4,
    title: '综合商',
    description: '我既提供设计服务，也提供搭建服务',
    icon: 'cluster-o'
  }
]

// 选择类型
const selectType = (type) => {
  selectedType.value = type
}

// 确认选择
const handleConfirm = () => {
  if (!selectedType.value) {
    showToast('请选择您的身份')
    return
  }

  // 跳转到完善信息页面
  router.push({
    name: 'CompleteProfile',
    query: { userType: selectedType.value }
  })
}
</script>

<style lang="scss" scoped>
.user-type-content {
  padding: var(--spacing-lg);
  min-height: calc(100vh - 46px);
  display: flex;
  flex-direction: column;
}

.header-section {
  text-align: center;
  margin-bottom: var(--spacing-xxl);
  
  h2 {
    font-size: 24px;
    font-weight: bold;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-md) 0;
  }
  
  p {
    font-size: 14px;
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.5;
  }
}

.type-options {
  flex: 1;
  margin-bottom: var(--spacing-xxl);
}

.type-option {
  display: flex;
  align-items: center;
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  background: var(--bg-primary);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  &.active {
    border-color: var(--primary-color);
    background: #f0f9ff;
  }
  
  &:active {
    transform: scale(0.98);
  }
  
  .option-icon {
    margin-right: var(--spacing-lg);
    
    .van-icon {
      font-size: 32px;
      color: var(--primary-color);
    }
  }
  
  .option-content {
    flex: 1;
    
    .option-title {
      font-size: 16px;
      font-weight: bold;
      color: var(--text-primary);
      margin: 0 0 4px 0;
    }
    
    .option-desc {
      font-size: 14px;
      color: var(--text-secondary);
      margin: 0;
      line-height: 1.4;
    }
  }
  
  .option-check {
    margin-left: var(--spacing-md);
    
    .van-icon {
      font-size: 20px;
    }
  }
}

.action-section {
  padding-top: var(--spacing-lg);
}
</style>
