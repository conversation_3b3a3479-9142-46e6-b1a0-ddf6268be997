<template>
  <div class="page">
    <!-- 页面标题 -->
    <div class="page-header">
      <van-nav-bar
        title="展会详情"
        left-arrow
        @click-left="$router.back()"
      />
    </div>

    <div v-if="loading" class="loading-container">
      <van-loading type="spinner" />
    </div>

    <div v-else-if="exhibition" class="exhibition-detail">
      <!-- 展会基本信息 -->
      <div class="exhibition-info">
        <div class="exhibition-header">
          <h1 class="exhibition-name">{{ exhibition.name }}</h1>
          <span :class="['status-badge', getStatusClass(exhibition.status)]">
            {{ getStatusText(exhibition.status) }}
          </span>
        </div>
        
        <div class="exhibition-details">
          <div class="detail-item">
            <van-icon name="location-o" class="icon" />
            <div class="detail-content">
              <div class="detail-label">举办地点</div>
              <div class="detail-value">{{ exhibition.venue?.name }}</div>
              <div class="detail-sub">{{ exhibition.city }} {{ exhibition.venue?.address }}</div>
            </div>
          </div>
          
          <div class="detail-item">
            <van-icon name="calendar-o" class="icon" />
            <div class="detail-content">
              <div class="detail-label">展会时间</div>
              <div class="detail-value">
                {{ formatDate(exhibition.startDate) }} - {{ formatDate(exhibition.endDate) }}
              </div>
            </div>
          </div>
          
          <div class="detail-item" v-if="exhibition.description">
            <van-icon name="info-o" class="icon" />
            <div class="detail-content">
              <div class="detail-label">展会介绍</div>
              <div class="detail-value">{{ exhibition.description }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-section" v-if="exhibition.status !== 3">
        <van-button
          v-if="!exhibition.isJoined"
          type="primary"
          size="large"
          block
          @click="showJoinDialog"
        >
          立即参加
        </van-button>
        <van-button
          v-else
          type="danger"
          size="large"
          block
          plain
          @click="showLeaveDialog"
        >
          退出展会
        </van-button>
      </div>
    </div>

    <!-- 参展类型选择弹窗 -->
    <van-popup v-model:show="showTypeSelector" position="bottom">
      <div class="type-selector">
        <div class="selector-header">
          <h3>选择参展类型</h3>
          <p>请选择您要以什么身份参加此展会</p>
        </div>
        <div class="type-options">
          <div
            v-for="type in participantTypes"
            :key="type.value"
            class="type-option"
            @click="confirmJoin(type.value)"
          >
            <van-icon :name="type.icon" class="type-icon" />
            <div class="type-info">
              <div class="type-name">{{ type.text }}</div>
              <div class="type-desc">{{ type.desc }}</div>
            </div>
            <van-icon name="arrow" class="arrow-icon" />
          </div>
        </div>
        <div class="selector-actions">
          <van-button block @click="showTypeSelector = false">取消</van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast, showDialog } from 'vant'
import { getExhibitionDetail, joinExhibition, leaveExhibition } from '../api/exhibition'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(true)
const exhibition = ref(null)
const showTypeSelector = ref(false)

// 参展类型选项
const participantTypes = [
  {
    value: 'design',
    text: '设计公司',
    desc: '以设计公司身份参展',
    icon: 'brush-o'
  },
  {
    value: 'factory',
    text: '工厂',
    desc: '以工厂身份参展',
    icon: 'shop-o'
  },
  {
    value: 'comprehensive',
    text: '综合商',
    desc: '以综合商身份参展',
    icon: 'cluster-o'
  }
]

// 获取展会详情
const fetchExhibitionDetail = async () => {
  try {
    loading.value = true
    const response = await getExhibitionDetail(route.params.id)
    exhibition.value = response.data
  } catch (error) {
    showToast(error.message || '获取展会详情失败')
    router.back()
  } finally {
    loading.value = false
  }
}

// 获取状态样式类
const getStatusClass = (status) => {
  const statusMap = {
    1: 'upcoming',    // 未开始
    2: 'ongoing',     // 进行中
    3: 'ended'        // 已结束
  }
  return statusMap[status] || 'upcoming'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    1: '未开始',
    2: '进行中',
    3: '已结束'
  }
  return statusMap[status] || '未知'
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

// 显示参加对话框
const showJoinDialog = () => {
  if (exhibition.value.status === 3) {
    showToast('展会已结束，无法参加')
    return
  }
  showTypeSelector.value = true
}

// 确认参加
const confirmJoin = async (type) => {
  try {
    showTypeSelector.value = false
    
    await joinExhibition({
      exhibitionId: exhibition.value.id,
      participantType: type
    })
    
    showToast('参展成功！')
    exhibition.value.isJoined = true
  } catch (error) {
    showToast(error.message || '参展失败')
  }
}

// 显示退出对话框
const showLeaveDialog = () => {
  showDialog({
    title: '确认退出',
    message: '确定要退出此展会吗？',
    confirmButtonText: '确定',
    cancelButtonText: '取消'
  }).then(() => {
    confirmLeave()
  }).catch(() => {
    // 用户取消
  })
}

// 确认退出
const confirmLeave = async () => {
  try {
    await leaveExhibition(exhibition.value.id)
    showToast('退出成功')
    exhibition.value.isJoined = false
  } catch (error) {
    showToast(error.message || '退出失败')
  }
}

// 页面初始化
onMounted(() => {
  fetchExhibitionDetail()
})
</script>

<style lang="scss" scoped>
.page-header {
  background: white;
  border-bottom: 1px solid #ebedf0;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.exhibition-detail {
  padding-bottom: 80px;
}

.exhibition-info {
  background: white;
  margin: 16px;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .exhibition-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;

    .exhibition-name {
      flex: 1;
      font-size: 20px;
      font-weight: bold;
      color: #323233;
      margin: 0 16px 0 0;
      line-height: 1.4;
    }

    .status-badge {
      padding: 6px 12px;
      border-radius: 16px;
      font-size: 12px;
      font-weight: 500;
      white-space: nowrap;

      &.upcoming {
        background: #e8f4ff;
        color: #1989fa;
      }

      &.ongoing {
        background: #e8f8e8;
        color: #07c160;
      }

      &.ended {
        background: #f5f5f5;
        color: #969799;
      }
    }
  }

  .exhibition-details {
    .detail-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      .icon {
        font-size: 20px;
        color: #1989fa;
        margin-right: 12px;
        margin-top: 2px;
      }

      .detail-content {
        flex: 1;

        .detail-label {
          font-size: 14px;
          color: #646566;
          margin-bottom: 4px;
        }

        .detail-value {
          font-size: 16px;
          color: #323233;
          font-weight: 500;
          margin-bottom: 2px;
        }

        .detail-sub {
          font-size: 14px;
          color: #969799;
        }
      }
    }
  }
}

.action-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 16px;
  border-top: 1px solid #ebedf0;
  z-index: 100;
}

.type-selector {
  padding: 20px;

  .selector-header {
    text-align: center;
    margin-bottom: 20px;

    h3 {
      margin: 0 0 8px 0;
      font-size: 18px;
      color: #323233;
    }

    p {
      margin: 0;
      font-size: 14px;
      color: #646566;
    }
  }

  .type-options {
    margin-bottom: 20px;

    .type-option {
      display: flex;
      align-items: center;
      padding: 16px;
      border: 1px solid #ebedf0;
      border-radius: 8px;
      margin-bottom: 8px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        border-color: #1989fa;
        background: #f8f9fa;
      }

      &:last-child {
        margin-bottom: 0;
      }

      .type-icon {
        font-size: 24px;
        color: #1989fa;
        margin-right: 12px;
      }

      .type-info {
        flex: 1;

        .type-name {
          font-size: 16px;
          font-weight: 500;
          color: #323233;
          margin-bottom: 4px;
        }

        .type-desc {
          font-size: 14px;
          color: #646566;
        }
      }

      .arrow-icon {
        color: #c8c9cc;
      }
    }
  }
}
</style>
