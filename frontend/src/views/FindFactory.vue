<template>
  <div class="page has-tabbar">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">找工厂</h1>
    </div>

    <!-- 搜索栏 -->
    <div class="search-bar">
      <div class="search-row">
        <van-field
          v-model="searchForm.cityName"
          label="城市"
          readonly
          is-link
          @click="showCityPicker = true"
          class="search-field"
        />
        <van-field
          v-model="searchForm.dateName"
          label="日期"
          readonly
          is-link
          @click="handleDateClick"
          class="search-field"
        />
        <van-field
          v-model="searchForm.venueName"
          label="场馆"
          readonly
          is-link
          @click="handleVenueClick"
          class="search-field"
        />
      </div>
      <div class="search-row">
        <van-field
          v-model="searchForm.keyword"
          label="关键字"
          placeholder="输入关键字"
          @input="handleKeywordChange"
          class="search-field"
        />
        <van-button
          type="primary"
          size="small"
          @click="handleSearch"
          class="search-button"
        >
          搜索
        </van-button>
      </div>
    </div>

    <!-- 工厂列表 -->
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
      >
        <div
          v-for="factory in factories"
          :key="factory.id"
          class="company-card"
          @click="goToDetail(factory.id)"
        >
          <div class="company-header">
            <div class="company-info">
              <div class="company-name">{{ factory.name }}</div>
              <div class="company-type">
                {{ factory.type === 'comprehensive' ? '综合商' : '工厂' }}
              </div>
            </div>
            <div class="company-actions">
              <van-icon
                name="star"
                :class="{ 'favorited': factory.isFavorited }"
                @click.stop="toggleFavorite(factory)"
              />
            </div>
          </div>
          
          <div class="company-details">
            <div class="detail-item" v-if="factory.city">
              <van-icon name="location-o" class="icon" />
              <span>{{ factory.city }}</span>
            </div>
            <div class="detail-item" v-if="factory.contactPerson">
              <van-icon name="manager-o" class="icon" />
              <span>{{ factory.contactPerson }}</span>
            </div>
            <div class="detail-item" v-if="factory.contactPhone">
              <van-icon name="phone-o" class="icon" />
              <span>{{ factory.contactPhone }}</span>
            </div>
            
            <!-- 特长标签 -->
            <div class="company-tags" v-if="factory.specialties">
              <span
                v-for="tag in factory.specialties.split(',')"
                :key="tag"
                class="tag"
              >
                {{ tag.trim() }}
              </span>
            </div>
          </div>
          
          <div class="company-footer">
            <div class="rating">
              <span class="stars">{{ '★'.repeat(factory.level || 1) }}</span>
              <span class="level">{{ factory.level || 1 }}星工厂</span>
            </div>
            <div :class="['status', factory.isFull ? 'full' : 'available']">
              {{ factory.isFull ? '已满额' : '可预约' }}
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="!loading && factories.length === 0" class="empty-state">
          <van-icon name="search" class="empty-icon" />
          <div class="empty-text">暂无工厂</div>
          <div class="empty-desc">试试调整搜索条件</div>
        </div>
      </van-list>
    </van-pull-refresh>

    <!-- 底部导航栏 -->
    <van-tabbar route class="custom-tabbar">
      <van-tabbar-item icon="home-o" to="/home">首页</van-tabbar-item>
      <van-tabbar-item icon="brush-o" to="/find-design">找设计</van-tabbar-item>
      <van-tabbar-item icon="shop-o" to="/find-factory">找工厂</van-tabbar-item>
      <van-tabbar-item icon="user-o" to="/profile">我的</van-tabbar-item>
    </van-tabbar>

    <!-- 城市选择器 -->
    <van-popup v-model:show="showCityPicker" position="bottom">
      <van-picker
        :columns="cityColumns"
        @confirm="onCityConfirm"
        @cancel="showCityPicker = false"
      />
    </van-popup>

    <!-- 日期选择器 -->
    <van-popup v-model:show="showDatePicker" position="bottom">
      <van-date-picker
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
        :min-date="new Date()"
      />
    </van-popup>

    <!-- 场馆选择器 -->
    <van-popup v-model:show="showVenuePicker" position="bottom">
      <van-picker
        v-if="venueColumns.length > 0"
        :columns="venueColumns"
        @confirm="onVenueConfirm"
        @cancel="showVenuePicker = false"
      />
      <div v-else style="padding: 20px; text-align: center;">
        {{ searchForm.cityValue ? '加载场馆中...' : '请先选择城市' }}
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast } from 'vant'
import { searchFactories, getVenues } from '@/api/search'
import { addFavorite, removeFavorite, checkFavoriteStatus } from '@/api/favorite'
import { CITY_OPTIONS } from '@/constants/cities'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const finished = ref(false)
const refreshing = ref(false)
const factories = ref([])

// 选择器显示状态
const showCityPicker = ref(false)
const showDatePicker = ref(false)
const showVenuePicker = ref(false)

// 选择器数据
const cityColumns = CITY_OPTIONS
const venueColumns = ref([])

// 搜索表单
const searchForm = reactive({
  keyword: '',
  cityName: '',
  cityValue: '',
  dateName: '',
  dateValue: '',
  venueName: '',
  venueValue: '',
  page: 1,
  pageSize: 10
})

// 获取工厂列表
const fetchFactories = async (isRefresh = false) => {
  try {
    if (isRefresh) {
      searchForm.page = 1
      finished.value = false
    }

    const params = {
      keyword: searchForm.keyword,
      city: searchForm.cityValue,
      date: searchForm.dateValue,
      venueId: searchForm.venueValue,
      page: searchForm.page,
      pageSize: searchForm.pageSize
    }

    const response = await searchFactories(params)
    const { list, pagination } = response.data

    // 检查收藏状态（批量处理，避免并发请求过多）
    for (let i = 0; i < list.length; i++) {
      const factory = list[i]
      try {
        // 添加延迟，避免并发请求过多
        if (i > 0) {
          await new Promise(resolve => setTimeout(resolve, 100))
        }
        const favoriteResponse = await checkFavoriteStatus(3, factory.id)
        factory.isFavorited = favoriteResponse.data.isFavorited
      } catch (error) {
        console.warn('检查收藏状态失败:', error)
        factory.isFavorited = false
      }
    }

    if (isRefresh) {
      factories.value = list
    } else {
      factories.value.push(...list)
    }

    // 检查是否还有更多数据
    if (pagination.page >= pagination.totalPages) {
      finished.value = true
    } else {
      searchForm.page++
    }
  } catch (error) {
    console.error('获取工厂列表失败:', error)
    showToast(error.message || '获取数据失败')

    // 如果是429错误，停止加载更多
    if (error.message && error.message.includes('429')) {
      finished.value = true
    }
  }
}

// 下拉刷新
const onRefresh = async () => {
  refreshing.value = true
  await fetchFactories(true)
  refreshing.value = false
}

// 上拉加载
const onLoad = async () => {
  if (finished.value) return
  loading.value = true
  await fetchFactories()
  loading.value = false
}

// 获取场馆列表（根据城市）
const fetchVenues = async (city) => {
  try {
    console.log('找工厂页面 - 开始获取场馆列表，城市:', city)

    if (!city) {
      console.log('找工厂页面 - 城市为空，清空场馆列表')
      venueColumns.value = []
      return
    }

    // 调用API获取指定城市的场馆
    console.log('找工厂页面 - 调用API获取场馆，参数:', { city })
    const response = await getVenues()
    console.log('找工厂页面 - 场馆API响应:', response)

    if (response && response.data) {
      // 过滤出指定城市的场馆
      const filteredVenues = response.data.filter(venue => venue.city === city)
      venueColumns.value = filteredVenues.map(venue => ({
        text: venue.name,
        value: venue.id
      }))
      console.log('找工厂页面 - 场馆选择器数据:', venueColumns.value)
      console.log('找工厂页面 - 找到场馆数量:', venueColumns.value.length)
    } else {
      console.log('找工厂页面 - API响应数据为空')
      venueColumns.value = []
    }
  } catch (error) {
    console.error('找工厂页面 - 获取场馆列表失败:', error)
    venueColumns.value = []
  }
}

// 选择器确认方法
const onCityConfirm = ({ selectedOptions }) => {
  searchForm.cityName = selectedOptions[0].text
  searchForm.cityValue = selectedOptions[0].value
  showCityPicker.value = false

  // 清空后续选择
  searchForm.dateName = ''
  searchForm.dateValue = ''
  searchForm.venueName = ''
  searchForm.venueValue = ''

  // 根据选择的城市获取场馆列表
  fetchVenues(selectedOptions[0].value)

  // 只有在有完整搜索条件时才搜索
  if (searchForm.cityValue && searchForm.dateValue && searchForm.venueValue) {
    fetchFactories(true)
  }
}

const onDateConfirm = ({ selectedValues }) => {
  // selectedValues 是 [年, 月, 日] 的数组
  const year = selectedValues[0]
  const month = selectedValues[1]
  const day = selectedValues[2]
  const date = new Date(year, month - 1, day) // 月份需要减1

  searchForm.dateName = date.toLocaleDateString('zh-CN')
  searchForm.dateValue = date.toISOString().split('T')[0]
  showDatePicker.value = false

  // 清空场馆选择
  searchForm.venueName = ''
  searchForm.venueValue = ''

  // 只有在有完整搜索条件时才搜索
  if (searchForm.cityValue && searchForm.dateValue && searchForm.venueValue) {
    fetchFactories(true)
  }
}

const onVenueConfirm = ({ selectedOptions }) => {
  searchForm.venueName = selectedOptions[0].text
  searchForm.venueValue = selectedOptions[0].value
  showVenuePicker.value = false

  // 有了完整的搜索条件，执行搜索
  if (searchForm.cityValue && searchForm.dateValue && searchForm.venueValue) {
    fetchFactories(true)
  }
}

// 字段点击处理
const handleDateClick = () => {
  if (!searchForm.cityValue) {
    showToast('请先选择城市')
    return
  }
  showDatePicker.value = true
}

const handleVenueClick = () => {
  if (!searchForm.cityValue) {
    showToast('请先选择城市')
    return
  }

  if (!searchForm.dateValue) {
    showToast('请先选择日期')
    return
  }

  if (venueColumns.value.length === 0) {
    showToast('该城市暂无可用场馆')
    return
  }

  showVenuePicker.value = true
}

// 关键字输入处理
const handleKeywordChange = () => {
  // 延迟搜索，避免频繁请求
  clearTimeout(handleKeywordChange.timer)
  handleKeywordChange.timer = setTimeout(() => {
    fetchFactories(true)
  }, 500)
}

// 搜索按钮处理
const handleSearch = () => {
  fetchFactories(true)
}

// 切换收藏状态
const toggleFavorite = async (factory) => {
  try {
    if (factory.isFavorited) {
      // 取消收藏 - 这里需要先获取收藏ID，暂时提示
      showToast('取消收藏功能待完善')
    } else {
      // 添加收藏
      const type = factory.type === 'comprehensive' ? 4 : 3
      await addFavorite({ type, id: factory.id })
      factory.isFavorited = true
      showToast('收藏成功')
    }
  } catch (error) {
    showToast(error.message || '操作失败')
  }
}

// 跳转到详情页
const goToDetail = (id) => {
  router.push(`/company-detail/factory/${id}`)
}

// 页面初始化
onMounted(() => {
  // 处理从首页传来的搜索参数
  let shouldSearch = false

  if (route.query.city) {
    const city = cityColumns.find(c => c.value === route.query.city)
    if (city) {
      searchForm.cityName = city.text
      searchForm.cityValue = city.value

      // 获取该城市的场馆列表
      fetchVenues(city.value)
      shouldSearch = true
    }
  }

  if (route.query.date) {
    searchForm.dateName = new Date(route.query.date).toLocaleDateString('zh-CN')
    searchForm.dateValue = route.query.date
    shouldSearch = true
  }

  if (route.query.venueId) {
    // 场馆信息需要等API返回后再设置
    setTimeout(() => {
      const venue = venueColumns.value.find(v => v.value == route.query.venueId)
      if (venue) {
        searchForm.venueName = venue.text
        searchForm.venueValue = venue.value

        // 如果有完整的搜索条件，执行搜索
        if (searchForm.cityValue && searchForm.dateValue && searchForm.venueValue) {
          fetchFactories(true)
        }
      }
    }, 1000)
    shouldSearch = true
  }

  // 只有在有搜索参数时才执行搜索，否则显示空列表
  if (shouldSearch && searchForm.cityValue && searchForm.dateValue && !route.query.venueId) {
    // 如果没有venueId参数，直接搜索
    fetchFactories(true)
  } else if (!shouldSearch) {
    // 没有搜索参数，显示空状态
    factories.value = []
    finished.value = true
  }
})
</script>

<style lang="scss" scoped>
.page-header {
  background: #1989fa;
  color: white;
  padding: 16px;
  text-align: center;

  .page-title {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
  }
}

.search-bar {
  background: white;
  padding: 12px 16px;
  border-bottom: 1px solid #ebedf0;

  .search-row {
    display: flex;
    gap: 8px;
    align-items: flex-end;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }

    .search-field {
      flex: 1;
      border: 1px solid #ebedf0;
      border-radius: 6px;

      &:deep(.van-field__label) {
        color: #323233;
        font-weight: 500;
        width: 50px;
      }
    }

    .search-button {
      height: 32px;
      padding: 0 16px;
      border-radius: 6px;
      font-size: 14px;
      white-space: nowrap;
    }
  }
}

.favorited {
  color: #ffd21e !important;
}
</style>
