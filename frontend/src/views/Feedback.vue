<template>
  <div class="page">
    <!-- 导航栏 -->
    <van-nav-bar
      title="意见反馈"
      left-arrow
      @click-left="goBack"
    />

    <div class="feedback-content">
      <van-form @submit="handleSubmit">
        <div class="form-section">
          <van-field
            v-model="form.type"
            name="type"
            label="反馈类型"
            placeholder="请选择反馈类型"
            readonly
            is-link
            @click="showTypePicker = true"
            :rules="[{ required: true, message: '请选择反馈类型' }]"
          />
          
          <van-field
            v-model="form.content"
            name="content"
            label="反馈内容"
            type="textarea"
            placeholder="请详细描述您遇到的问题或建议"
            rows="5"
            :rules="[
              { required: true, message: '请输入反馈内容' },
              { min: 10, message: '反馈内容至少10个字符' }
            ]"
          />
          
          <van-field
            v-model="form.contact"
            name="contact"
            label="联系方式"
            placeholder="请输入您的联系方式（可选）"
          />
        </div>

        <div class="submit-section">
          <van-button
            type="primary"
            size="large"
            block
            native-type="submit"
            :loading="loading"
          >
            提交反馈
          </van-button>
        </div>
      </van-form>
    </div>

    <!-- 反馈类型选择器 -->
    <van-popup v-model:show="showTypePicker" position="bottom">
      <van-picker
        :columns="typeColumns"
        @confirm="onTypeConfirm"
        @cancel="showTypePicker = false"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const showTypePicker = ref(false)

// 表单数据
const form = reactive({
  type: '',
  content: '',
  contact: ''
})

// 反馈类型
const typeColumns = [
  '功能建议',
  '界面优化',
  '性能问题',
  '数据错误',
  '其他问题'
]

// 反馈类型选择确认
const onTypeConfirm = ({ selectedValues }) => {
  form.type = selectedValues[0]
  showTypePicker.value = false
}

// 提交反馈
const handleSubmit = async () => {
  try {
    loading.value = true
    
    // 模拟提交
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    showToast('反馈提交成功，感谢您的建议！')
    
    // 清空表单
    form.type = ''
    form.content = ''
    form.contact = ''
    
    // 返回上一页
    setTimeout(() => {
      router.back()
    }, 1500)
  } catch (error) {
    showToast('提交失败，请重试')
  } finally {
    loading.value = false
  }
}

// 返回上一页
const goBack = () => {
  router.back()
}
</script>

<style lang="scss" scoped>
.feedback-content {
  padding: var(--spacing-lg);
}

.form-section {
  margin-bottom: var(--spacing-xxl);
}

.submit-section {
  margin-top: var(--spacing-xxl);
}
</style>
