<template>
  <div class="login-page">
    <div class="login-content">
      <!-- Logo区域 -->
      <div class="logo-section">
        <div class="logo">
          <van-icon name="shop-o" size="60" color="#1989fa" />
        </div>
        <h1 class="app-name">馆拼拼</h1>
        <p class="app-desc">连接参展商、设计公司、工厂</p>
      </div>

      <!-- 登录按钮 -->
      <div class="login-section">
        <van-button
          type="primary"
          size="large"
          round
          block
          :loading="loading"
          @click="handleWechatLogin"
        >
          <van-icon name="wechat" />
          微信授权登录
        </van-button>
      </div>

      <!-- 说明文字 -->
      <div class="tips">
        <p>登录即表示同意《用户协议》和《隐私政策》</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { showToast } from 'vant'

const router = useRouter()
const userStore = useUserStore()
const loading = ref(false)

// 微信登录
const handleWechatLogin = async () => {
  loading.value = true

  try {
    // 使用微信小程序云开发登录
    const loginResult = await wechatCloudLogin()

    if (loginResult.isNewUser) {
      // 新用户，跳转到选择身份页面
      router.push('/user-type')
    } else {
      // 老用户，跳转到首页
      router.push('/home')
    }
  } catch (error) {
    console.error('登录失败:', error)
    // 显示错误提示
    showToast(error.message || '登录失败，请重试')
  } finally {
    loading.value = false
  }
}

// 微信登录（最简单实现）
const wechatCloudLogin = async () => {
  try {
    // 检查是否在微信小程序环境中
    if (typeof wx !== 'undefined' && wx.cloud) {
      console.log('检测到微信小程序环境，使用云开发登录')

      // 在微信小程序中，使用云开发登录
      const result = await wx.cloud.callContainer({
        config: {
          env: 'prod-9gffvobi5a3b742a'
        },
        path: '/api/auth/cloud-login',
        method: 'POST',
        header: {
          'X-WX-SERVICE': 'backend',
          'content-type': 'application/json'
        },
        data: {}
      })

      console.log('云开发登录结果:', result)

      if (result.errCode === 0) {
        // 登录成功，保存用户信息到store
        const userStore = useUserStore()
        userStore.setUserInfo(result.data.userInfo)
        userStore.setToken(result.data.token)

        return {
          isNewUser: result.data.isNewUser,
          userInfo: result.data.userInfo
        }
      } else {
        throw new Error(result.errMsg || '云开发登录失败')
      }
    } else {
      // 非小程序环境，使用模拟登录
      console.log('非小程序环境，使用模拟登录')
      showToast('非小程序环境，使用模拟登录')

      const userStore = useUserStore()
      const mockCode = 'test_code_' + Date.now()
      console.log('发送模拟登录请求，code:', mockCode)
      const result = await userStore.wechatLogin(mockCode)

      return result
    }
  } catch (error) {
    console.error('登录失败:', error)
    throw new Error('登录失败，请重试')
  }
}


</script>

<style lang="scss" scoped>
.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
}

.login-content {
  width: 100%;
  max-width: 320px;
}

.logo-section {
  text-align: center;
  margin-bottom: 60px;
  
  .logo {
    margin-bottom: var(--spacing-lg);
  }
  
  .app-name {
    font-size: 32px;
    font-weight: bold;
    color: white;
    margin: 0 0 var(--spacing-sm) 0;
  }
  
  .app-desc {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
  }
}

.login-section {
  margin-bottom: 40px;
  
  .van-button {
    height: 50px;
    font-size: 16px;
    
    .van-icon {
      margin-right: var(--spacing-sm);
    }
  }
}

.tips {
  text-align: center;
  
  p {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    margin: 0;
    line-height: 1.4;
  }
}
</style>
