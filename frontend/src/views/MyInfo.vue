<template>
  <div class="page">
    <!-- 导航栏 -->
    <van-nav-bar
      title="我的信息"
      left-arrow
      @click-left="goBack"
    />

    <div class="my-info-content">
      <!-- 用户基本信息 -->
      <div class="info-section">
        <h3 class="section-title">基本信息</h3>
        <van-cell-group>
          <van-cell
            title="昵称"
            :value="userInfo?.nickname || '未设置'"
            is-link
            @click="editNickname"
          />
          <van-cell
            title="手机号"
            :value="userInfo?.phone || '未绑定'"
            is-link
            @click="editPhone"
          />
          <van-cell
            title="用户类型"
            :value="getUserTypeName()"
          />
        </van-cell-group>
      </div>

      <!-- 公司信息 -->
      <div v-if="userInfo?.companyInfo" class="info-section">
        <h3 class="section-title">公司信息</h3>
        
        <!-- 普通公司信息 -->
        <van-cell-group v-if="userInfo.userType !== 4">
          <van-cell
            title="公司名称"
            :value="userInfo.companyInfo.name || '未设置'"
            is-link
            @click="editCompanyName"
          />
          <van-cell
            title="联系人"
            :value="userInfo.companyInfo.contactPerson || '未设置'"
            is-link
            @click="editContactPerson"
          />
          <van-cell
            title="联系电话"
            :value="userInfo.companyInfo.contactPhone || '未设置'"
            is-link
            @click="editContactPhone"
          />
          <van-cell
            title="所在城市"
            :value="userInfo.companyInfo.city || '未设置'"
            is-link
            @click="editCity"
          />
          <van-cell
            v-if="userInfo.userType === 3"
            title="专业特长"
            :value="userInfo.companyInfo.specialties || '未设置'"
            is-link
            @click="editSpecialties"
          />
          <van-cell
            v-if="userInfo.userType === 2 || userInfo.userType === 3"
            title="星级等级"
            :value="`${userInfo.companyInfo.level || 1}星`"
          />
        </van-cell-group>
        
        <!-- 综合商信息 -->
        <div v-else>
          <!-- 设计公司信息 -->
          <div v-if="userInfo.companyInfo.designCompany" class="sub-section">
            <h4 class="sub-title">设计公司</h4>
            <van-cell-group>
              <van-cell
                title="公司名称"
                :value="userInfo.companyInfo.designCompany.name || '未设置'"
              />
              <van-cell
                title="联系人"
                :value="userInfo.companyInfo.designCompany.contactPerson || '未设置'"
              />
              <van-cell
                title="联系电话"
                :value="userInfo.companyInfo.designCompany.contactPhone || '未设置'"
              />
              <van-cell
                title="所在城市"
                :value="userInfo.companyInfo.designCompany.city || '未设置'"
              />
              <van-cell
                title="星级等级"
                :value="`${userInfo.companyInfo.designCompany.level || 1}星`"
              />
            </van-cell-group>
          </div>
          
          <!-- 工厂信息 -->
          <div v-if="userInfo.companyInfo.factory" class="sub-section">
            <h4 class="sub-title">工厂</h4>
            <van-cell-group>
              <van-cell
                title="工厂名称"
                :value="userInfo.companyInfo.factory.name || '未设置'"
              />
              <van-cell
                title="联系人"
                :value="userInfo.companyInfo.factory.contactPerson || '未设置'"
              />
              <van-cell
                title="联系电话"
                :value="userInfo.companyInfo.factory.contactPhone || '未设置'"
              />
              <van-cell
                title="所在城市"
                :value="userInfo.companyInfo.factory.city || '未设置'"
              />
              <van-cell
                title="专业特长"
                :value="userInfo.companyInfo.factory.specialties || '未设置'"
              />
              <van-cell
                title="星级等级"
                :value="`${userInfo.companyInfo.factory.level || 1}星`"
              />
            </van-cell-group>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑弹窗 -->
    <van-popup v-model:show="showEditDialog" position="bottom">
      <div class="edit-dialog">
        <div class="dialog-header">
          <van-button type="default" @click="cancelEdit">取消</van-button>
          <h3>{{ editTitle }}</h3>
          <van-button type="primary" @click="confirmEdit">确定</van-button>
        </div>
        <div class="dialog-content">
          <van-field
            v-if="editType !== 'city'"
            v-model="editValue"
            :placeholder="`请输入${editTitle}`"
            :type="editType === 'specialties' ? 'textarea' : 'text'"
            :rows="editType === 'specialties' ? 3 : 1"
          />
          <van-picker
            v-else
            :columns="cityColumns"
            @confirm="onCityConfirm"
            @cancel="cancelEdit"
          />
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { useUserStore } from '@/stores/user'
import { updateUserProfile } from '@/api/auth'
import { CITY_OPTIONS } from '@/constants/cities'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const showEditDialog = ref(false)
const editType = ref('')
const editTitle = ref('')
const editValue = ref('')

// 计算属性
const userInfo = computed(() => userStore.userInfo)

// 城市列表
const cityColumns = CITY_OPTIONS

// 获取用户类型名称
const getUserTypeName = () => {
  const typeMap = {
    1: '参展商',
    2: '设计公司',
    3: '工厂',
    4: '综合商'
  }
  return typeMap[userInfo.value?.userType] || '未知'
}

// 编辑方法
const editNickname = () => {
  editType.value = 'nickname'
  editTitle.value = '昵称'
  editValue.value = userInfo.value?.nickname || ''
  showEditDialog.value = true
}

const editPhone = () => {
  editType.value = 'phone'
  editTitle.value = '手机号'
  editValue.value = userInfo.value?.phone || ''
  showEditDialog.value = true
}

const editCompanyName = () => {
  editType.value = 'companyName'
  editTitle.value = '公司名称'
  editValue.value = userInfo.value?.companyInfo?.name || ''
  showEditDialog.value = true
}

const editContactPerson = () => {
  editType.value = 'contactPerson'
  editTitle.value = '联系人'
  editValue.value = userInfo.value?.companyInfo?.contactPerson || ''
  showEditDialog.value = true
}

const editContactPhone = () => {
  editType.value = 'contactPhone'
  editTitle.value = '联系电话'
  editValue.value = userInfo.value?.companyInfo?.contactPhone || ''
  showEditDialog.value = true
}

const editCity = () => {
  editType.value = 'city'
  editTitle.value = '所在城市'
  editValue.value = userInfo.value?.companyInfo?.city || ''
  showEditDialog.value = true
}

const editSpecialties = () => {
  editType.value = 'specialties'
  editTitle.value = '专业特长'
  editValue.value = userInfo.value?.companyInfo?.specialties || ''
  showEditDialog.value = true
}

// 城市选择确认
const onCityConfirm = ({ selectedOptions }) => {
  editValue.value = selectedOptions[0].value
  confirmEdit()
}

// 取消编辑
const cancelEdit = () => {
  showEditDialog.value = false
  editType.value = ''
  editTitle.value = ''
  editValue.value = ''
}

// 确认编辑
const confirmEdit = async () => {
  try {
    if (!editValue.value.trim()) {
      showToast(`${editTitle.value}不能为空`)
      return
    }

    // 构建更新数据
    const updateData = {}
    
    if (editType.value === 'nickname' || editType.value === 'phone') {
      updateData[editType.value] = editValue.value
    } else {
      // 公司信息更新
      updateData.companyInfo = {
        [editType.value]: editValue.value
      }
    }

    await updateUserProfile(updateData)
    
    // 更新本地用户信息
    await userStore.fetchUserProfile()
    
    showToast('更新成功')
    cancelEdit()
  } catch (error) {
    showToast(error.message || '更新失败')
  }
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 页面初始化
onMounted(() => {
  if (!userInfo.value) {
    userStore.fetchUserProfile()
  }
})
</script>

<style lang="scss" scoped>
.my-info-content {
  padding-bottom: var(--spacing-lg);
}

.info-section {
  margin-bottom: var(--spacing-lg);
  
  .section-title {
    font-size: 16px;
    font-weight: bold;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-md) 0;
    padding: 0 var(--spacing-lg);
  }
  
  .sub-section {
    margin-bottom: var(--spacing-md);
    
    .sub-title {
      font-size: 14px;
      font-weight: bold;
      color: var(--text-regular);
      margin: 0 0 var(--spacing-sm) 0;
      padding: 0 var(--spacing-lg);
    }
  }
}

.edit-dialog {
  background: var(--bg-primary);
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
  
  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    
    h3 {
      font-size: 16px;
      font-weight: bold;
      color: var(--text-primary);
      margin: 0;
    }
  }
  
  .dialog-content {
    padding: var(--spacing-lg);
  }
}
</style>
