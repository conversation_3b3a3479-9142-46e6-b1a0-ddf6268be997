<template>
  <div class="test-page">
    <h1>测试页面</h1>
    
    <!-- 测试按钮 -->
    <div class="test-buttons">
      <van-button type="primary" @click="testToast">测试Toast</van-button>
      <van-button type="success" @click="testRouter">测试路由</van-button>
      <van-button type="warning" @click="testPicker">测试选择器</van-button>
    </div>
    
    <!-- 测试选择器 -->
    <van-popup v-model:show="showTestPicker" position="bottom">
      <van-picker
        :columns="testColumns"
        @confirm="onTestConfirm"
        @cancel="showTestPicker = false"
      />
    </van-popup>
    
    <!-- 简化的底部导航 -->
    <div class="simple-nav">
      <button @click="goTo('/home')">首页</button>
      <button @click="goTo('/find-design')">找设计</button>
      <button @click="goTo('/find-factory')">找工厂</button>
      <button @click="goTo('/profile')">我的</button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'

const router = useRouter()

const showTestPicker = ref(false)
const testColumns = [
  { text: '选项1', value: 1 },
  { text: '选项2', value: 2 },
  { text: '选项3', value: 3 }
]

const testToast = () => {
  showToast('Toast测试成功！')
}

const testRouter = () => {
  console.log('测试路由跳转')
  showToast('准备跳转到找设计页面')
  router.push('/find-design')
}

const testPicker = () => {
  console.log('打开测试选择器')
  showToast('打开选择器')
  showTestPicker.value = true
}

const onTestConfirm = ({ selectedOptions }) => {
  console.log('选择器确认:', selectedOptions)
  showToast(`选择了: ${selectedOptions[0].text}`)
  showTestPicker.value = false
}

const goTo = (path) => {
  console.log('简单导航到:', path)
  showToast(`导航到: ${path}`)
  router.push(path)
}
</script>

<style scoped>
.test-page {
  padding: 20px;
}

.test-buttons {
  display: flex;
  gap: 10px;
  margin: 20px 0;
}

.simple-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  background: white;
  border-top: 1px solid #eee;
}

.simple-nav button {
  flex: 1;
  padding: 15px;
  border: none;
  background: white;
  cursor: pointer;
}

.simple-nav button:hover {
  background: #f5f5f5;
}
</style>
