<template>
  <div class="page">
    <!-- 导航栏 -->
    <van-nav-bar
      title="完善信息"
      left-arrow
      @click-left="goBack"
    />

    <div class="complete-profile-content">
      <!-- 表单 -->
      <van-form @submit="handleSubmit">
        <!-- 基本信息 -->
        <div class="form-section">
          <h3 class="section-title">基本信息</h3>
          
          <van-field
            v-model="form.companyName"
            name="companyName"
            label="公司名称"
            placeholder="请输入公司名称"
            :rules="[{ required: true, message: '请输入公司名称' }]"
          />
          
          <van-field
            v-model="form.contactPerson"
            name="contactPerson"
            label="联系人"
            placeholder="请输入联系人姓名"
            :rules="[{ required: true, message: '请输入联系人姓名' }]"
          />
          
          <van-field
            v-model="form.contactPhone"
            name="contactPhone"
            label="联系电话"
            placeholder="请输入联系电话"
            :rules="[
              { required: true, message: '请输入联系电话' },
              { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }
            ]"
          />
          
          <van-field
            v-model="form.city"
            name="city"
            label="所在城市"
            placeholder="请选择所在城市"
            readonly
            is-link
            @click="showCityPicker = true"
            :rules="[{ required: true, message: '请选择所在城市' }]"
          />
        </div>

        <!-- 参展商特有字段 -->
        <div v-if="userType === 1" class="form-section">
          <h3 class="section-title">参展信息</h3>
          
          <van-field
            v-model="form.categoryName"
            name="categoryName"
            label="参展类别"
            placeholder="请选择参展类别"
            readonly
            is-link
            @click="showCategoryPicker = true"
            :rules="[{ required: true, message: '请选择参展类别' }]"
          />
        </div>

        <!-- 工厂特有字段 -->
        <div v-if="userType === 3 || userType === 4" class="form-section">
          <h3 class="section-title">专业信息</h3>
          
          <van-field
            v-model="form.specialties"
            name="specialties"
            label="专业特长"
            type="textarea"
            placeholder="请输入专业特长，如：钢结构、木结构、特装搭建等"
            rows="3"
            :rules="[{ required: true, message: '请输入专业特长' }]"
          />
        </div>

        <!-- 提交按钮 -->
        <div class="submit-section">
          <van-button
            type="primary"
            size="large"
            block
            native-type="submit"
            :loading="loading"
          >
            完成注册
          </van-button>
        </div>
      </van-form>
    </div>

    <!-- 城市选择器 -->
    <van-popup v-model:show="showCityPicker" position="bottom">
      <van-picker
        :columns="cityColumns"
        @confirm="onCityConfirm"
        @cancel="showCityPicker = false"
      />
    </van-popup>

    <!-- 展会类别选择器 -->
    <van-popup v-model:show="showCategoryPicker" position="bottom">
      <van-picker
        :columns="categoryColumns"
        @confirm="onCategoryConfirm"
        @cancel="showCategoryPicker = false"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast } from 'vant'
import { completeProfile } from '@/api/auth'
import { useUserStore } from '@/stores/user'
import { CITY_OPTIONS } from '@/constants/cities'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 用户类型
const userType = computed(() => parseInt(route.query.userType))

// 响应式数据
const loading = ref(false)
const showCityPicker = ref(false)
const showCategoryPicker = ref(false)

// 表单数据
const form = reactive({
  companyName: '',
  contactPerson: '',
  contactPhone: '',
  city: '',
  categoryId: '',
  categoryName: '',
  specialties: ''
})

// 城市列表
const cityColumns = CITY_OPTIONS

// 展会类别列表
const categoryColumns = [
  { text: '服装展', value: 1 },
  { text: '水展', value: 2 },
  { text: '汽车展', value: 3 },
  { text: '科技展', value: 4 },
  { text: '箱包展', value: 5 },
  { text: '家具展', value: 6 },
  { text: '建材展', value: 7 },
  { text: '食品展', value: 8 },
  { text: '医疗展', value: 9 },
  { text: '教育展', value: 10 }
]

// 城市选择确认
const onCityConfirm = ({ selectedOptions }) => {
  form.city = selectedOptions[0].value
  showCityPicker.value = false
}

// 展会类别选择确认
const onCategoryConfirm = ({ selectedOptions }) => {
  form.categoryId = selectedOptions[0].value
  form.categoryName = selectedOptions[0].text
  showCategoryPicker.value = false
}

// 提交表单
const handleSubmit = async () => {
  try {
    loading.value = true

    // 构建提交数据
    const profileData = {
      userType: userType.value,
      companyInfo: {
        name: form.companyName,
        contactPerson: form.contactPerson,
        contactPhone: form.contactPhone,
        city: form.city
      }
    }

    // 根据用户类型添加特定字段
    if (userType.value === 1) {
      // 参展商
      profileData.companyInfo.categoryId = form.categoryId
    } else if (userType.value === 3 || userType.value === 4) {
      // 工厂或综合商
      profileData.companyInfo.specialties = form.specialties
      profileData.companyInfo.level = 1 // 默认1星
    }

    if (userType.value === 2 || userType.value === 4) {
      // 设计公司或综合商
      profileData.companyInfo.level = 1 // 默认1星
    }

    // 如果是综合商，需要同时创建设计公司和工厂信息
    if (userType.value === 4) {
      profileData.companyInfo = {
        designCompany: {
          name: form.companyName,
          contactPerson: form.contactPerson,
          contactPhone: form.contactPhone,
          city: form.city,
          level: 1
        },
        factory: {
          name: form.companyName,
          contactPerson: form.contactPerson,
          contactPhone: form.contactPhone,
          city: form.city,
          specialties: form.specialties,
          level: 1
        }
      }
    }

    // 调用API
    await completeProfile(profileData)

    showToast('注册完成')
    
    // 跳转到首页
    router.push('/home')
  } catch (error) {
    showToast(error.message || '注册失败')
  } finally {
    loading.value = false
  }
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 页面标题
const getPageTitle = () => {
  const typeNames = {
    1: '参展商',
    2: '设计公司',
    3: '工厂',
    4: '综合商'
  }
  return `完善${typeNames[userType.value] || ''}信息`
}

onMounted(() => {
  // 验证用户类型
  if (!userType.value || ![1, 2, 3, 4].includes(userType.value)) {
    showToast('无效的用户类型')
    router.push('/user-type')
  }
})
</script>

<style lang="scss" scoped>
.complete-profile-content {
  padding: var(--spacing-lg);
  padding-bottom: 100px;
}

.form-section {
  margin-bottom: var(--spacing-xxl);
  
  .section-title {
    font-size: 16px;
    font-weight: bold;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-lg) 0;
    padding-left: var(--spacing-sm);
    border-left: 3px solid var(--primary-color);
  }
}

.submit-section {
  margin-top: var(--spacing-xxl);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border-light);
}

:deep(.van-field__label) {
  width: 80px;
}

:deep(.van-field__control) {
  color: var(--text-primary);
}
</style>
