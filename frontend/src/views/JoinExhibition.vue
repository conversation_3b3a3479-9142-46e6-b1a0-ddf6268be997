<template>
  <div class="page">
    <!-- 页面标题 -->
    <div class="page-header">
      <van-nav-bar
        title="参加新展会"
        left-arrow
        @click-left="$router.back()"
      />
    </div>

    <!-- 搜索栏 -->
    <div class="search-bar">
      <van-field
        v-model="searchKeyword"
        placeholder="搜索展会名称或场馆"
        left-icon="search"
        @input="handleSearch"
      />
    </div>

    <!-- 展会列表 -->
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
      >
        <div
          v-for="exhibition in exhibitions"
          :key="exhibition.id"
          class="exhibition-card"
        >
          <div class="exhibition-header">
            <div class="exhibition-info">
              <div class="exhibition-name">{{ exhibition.name }}</div>
              <div class="exhibition-venue">{{ exhibition.venue?.name }}</div>
            </div>
            <div class="exhibition-status">
              <span :class="['status-badge', getStatusClass(exhibition.status)]">
                {{ getStatusText(exhibition.status) }}
              </span>
            </div>
          </div>
          
          <div class="exhibition-details">
            <div class="detail-item">
              <van-icon name="location-o" class="icon" />
              <span>{{ exhibition.city }}</span>
            </div>
            <div class="detail-item">
              <van-icon name="calendar-o" class="icon" />
              <span>{{ formatDate(exhibition.startDate) }} - {{ formatDate(exhibition.endDate) }}</span>
            </div>
            <div class="detail-item" v-if="exhibition.description">
              <van-icon name="info-o" class="icon" />
              <span>{{ exhibition.description }}</span>
            </div>
          </div>
          
          <div class="exhibition-footer">
            <div class="exhibition-actions">
              <van-button
                size="small"
                type="primary"
                plain
                @click="viewDetail(exhibition)"
              >
                查看详情
              </van-button>
              <van-button
                size="small"
                type="primary"
                :disabled="exhibition.isJoined"
                @click="joinExhibition(exhibition)"
              >
                {{ exhibition.isJoined ? '已参加' : '立即参加' }}
              </van-button>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="!loading && exhibitions.length === 0" class="empty-state">
          <van-icon name="calendar-o" class="empty-icon" />
          <div class="empty-text">暂无可参加的展会</div>
          <div class="empty-desc">试试调整搜索条件</div>
        </div>
      </van-list>
    </van-pull-refresh>

    <!-- 参展类型选择弹窗 -->
    <van-popup v-model:show="showTypeSelector" position="bottom">
      <div class="type-selector">
        <div class="selector-header">
          <h3>选择参展类型</h3>
          <p>请选择您要以什么身份参加此展会</p>
        </div>
        <div class="type-options">
          <div
            v-for="type in participantTypes"
            :key="type.value"
            class="type-option"
            @click="selectType(type.value)"
          >
            <van-icon :name="type.icon" class="type-icon" />
            <div class="type-info">
              <div class="type-name">{{ type.text }}</div>
              <div class="type-desc">{{ type.desc }}</div>
            </div>
            <van-icon name="arrow" class="arrow-icon" />
          </div>
        </div>
        <div class="selector-actions">
          <van-button block @click="showTypeSelector = false">取消</van-button>
        </div>
      </div>
    </van-popup>

    <!-- 满额状态选择弹窗 -->
    <van-popup v-model:show="showFullSelector" position="bottom">
      <div class="full-selector">
        <div class="selector-header">
          <h3>您还希望接待其他同馆客户吗？</h3>
          <p>请选择您的接待状态</p>
        </div>
        <div class="full-options">
          <div
            class="full-option"
            @click="confirmJoin(0)"
          >
            <van-icon name="smile-o" class="full-icon" />
            <div class="full-info">
              <div class="full-name">是，可预约</div>
              <div class="full-desc">愿意接待其他同馆客户</div>
            </div>
            <van-icon name="arrow" class="arrow-icon" />
          </div>
          <div
            class="full-option"
            @click="confirmJoin(1)"
          >
            <van-icon name="close" class="full-icon" />
            <div class="full-info">
              <div class="full-name">否，已满额</div>
              <div class="full-desc">暂不接待其他客户</div>
            </div>
            <van-icon name="arrow" class="arrow-icon" />
          </div>
        </div>
        <div class="selector-actions">
          <van-button block @click="showFullSelector = false">取消</van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showDialog } from 'vant'
import { getAvailableExhibitions, joinExhibition as joinExhibitionAPI } from '../api/exhibition'
import { useUserStore } from '../stores/user'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const finished = ref(false)
const refreshing = ref(false)
const exhibitions = ref([])
const searchKeyword = ref('')
const showTypeSelector = ref(false)
const showFullSelector = ref(false)
const selectedExhibition = ref(null)
const selectedParticipantType = ref('')

// 分页参数
const page = ref(1)
const pageSize = 10

// 参展类型选项
const participantTypes = [
  {
    value: 'design',
    text: '设计公司',
    desc: '以设计公司身份参展',
    icon: 'brush-o'
  },
  {
    value: 'factory',
    text: '工厂',
    desc: '以工厂身份参展',
    icon: 'shop-o'
  },
  {
    value: 'comprehensive',
    text: '综合商',
    desc: '以综合商身份参展',
    icon: 'cluster-o'
  }
]

// 获取可参加的展会列表
const fetchExhibitions = async (isRefresh = false) => {
  try {
    if (isRefresh) {
      page.value = 1
      finished.value = false
    }

    const response = await getAvailableExhibitions({
      page: page.value,
      pageSize,
      keyword: searchKeyword.value
    })
    
    const { list, pagination } = response.data

    if (isRefresh) {
      exhibitions.value = list
    } else {
      exhibitions.value.push(...list)
    }

    // 检查是否还有更多数据
    if (pagination.page >= pagination.totalPages) {
      finished.value = true
    } else {
      page.value++
    }
  } catch (error) {
    console.error('获取展会列表失败:', error)
    showToast(error.message || '获取数据失败')

    // 如果是429错误或其他错误，停止加载更多
    finished.value = true
  }
}

// 搜索处理
const handleSearch = () => {
  clearTimeout(handleSearch.timer)
  handleSearch.timer = setTimeout(() => {
    fetchExhibitions(true)
  }, 500)
}

// 下拉刷新
const onRefresh = async () => {
  refreshing.value = true
  await fetchExhibitions(true)
  refreshing.value = false
}

// 上拉加载
const onLoad = async () => {
  if (finished.value) return
  loading.value = true
  try {
    await fetchExhibitions()
  } catch (error) {
    console.error('加载展会失败:', error)
    finished.value = true // 出错时停止加载更多
  } finally {
    loading.value = false
  }
}

// 获取状态样式类
const getStatusClass = (status) => {
  const statusMap = {
    1: 'upcoming',    // 未开始
    2: 'ongoing',     // 进行中
    3: 'ended'        // 已结束
  }
  return statusMap[status] || 'upcoming'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    1: '未开始',
    2: '进行中',
    3: '已结束'
  }
  return statusMap[status] || '未知'
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

// 查看详情
const viewDetail = (exhibition) => {
  router.push(`/exhibition-detail/${exhibition.id}`)
}

// 参加展会
const joinExhibition = (exhibition) => {
  if (exhibition.status === 3) {
    showToast('展会已结束，无法参加')
    return
  }

  selectedExhibition.value = exhibition
  showTypeSelector.value = true
}

// 选择参展类型
const selectType = (type) => {
  selectedParticipantType.value = type
  showTypeSelector.value = false
  showFullSelector.value = true
}

// 确认参加展会
const confirmJoin = async (isFull) => {
  try {
    showFullSelector.value = false

    await joinExhibitionAPI({
      exhibitionId: selectedExhibition.value.id,
      participantType: selectedParticipantType.value,
      isFull: isFull
    })

    showToast('参展成功！')

    // 更新展会状态
    selectedExhibition.value.isJoined = true

    // 刷新列表
    fetchExhibitions(true)
  } catch (error) {
    showToast(error.message || '参展失败')
  }
}

// 页面初始化
onMounted(() => {
  fetchExhibitions(true)
})
</script>

<style lang="scss" scoped>
.page-header {
  background: white;
  border-bottom: 1px solid #ebedf0;
}

.search-bar {
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #ebedf0;
}

.exhibition-card {
  background: white;
  margin: 8px 16px;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .exhibition-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;

    .exhibition-info {
      flex: 1;

      .exhibition-name {
        font-size: 16px;
        font-weight: bold;
        color: #323233;
        margin-bottom: 4px;
      }

      .exhibition-venue {
        font-size: 14px;
        color: #646566;
      }
    }

    .exhibition-status {
      .status-badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;

        &.upcoming {
          background: #e8f4ff;
          color: #1989fa;
        }

        &.ongoing {
          background: #e8f8e8;
          color: #07c160;
        }

        &.ended {
          background: #f5f5f5;
          color: #969799;
        }
      }
    }
  }

  .exhibition-details {
    margin-bottom: 12px;

    .detail-item {
      display: flex;
      align-items: center;
      margin-bottom: 6px;
      font-size: 14px;
      color: #646566;

      &:last-child {
        margin-bottom: 0;
      }

      .icon {
        margin-right: 6px;
        color: #969799;
      }
    }
  }

  .exhibition-footer {
    padding-top: 12px;
    border-top: 1px solid #f0f0f0;

    .exhibition-actions {
      display: flex;
      gap: 8px;
      justify-content: flex-end;
    }
  }
}

.type-selector,
.full-selector {
  padding: 20px;

  .selector-header {
    text-align: center;
    margin-bottom: 20px;

    h3 {
      margin: 0 0 8px 0;
      font-size: 18px;
      color: #323233;
    }

    p {
      margin: 0;
      font-size: 14px;
      color: #646566;
    }
  }

  .type-options,
  .full-options {
    margin-bottom: 20px;

    .type-option,
    .full-option {
      display: flex;
      align-items: center;
      padding: 16px;
      border: 1px solid #ebedf0;
      border-radius: 8px;
      margin-bottom: 8px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        border-color: #1989fa;
        background: #f8f9fa;
      }

      &:last-child {
        margin-bottom: 0;
      }

      .type-icon,
      .full-icon {
        font-size: 24px;
        color: #1989fa;
        margin-right: 12px;
      }

      .type-info,
      .full-info {
        flex: 1;

        .type-name,
        .full-name {
          font-size: 16px;
          font-weight: 500;
          color: #323233;
          margin-bottom: 4px;
        }

        .type-desc,
        .full-desc {
          font-size: 14px;
          color: #646566;
        }
      }

      .arrow-icon {
        color: #c8c9cc;
      }
    }
  }
}

.empty-state {
  text-align: center;
  padding: 60px 20px;

  .empty-icon {
    font-size: 64px;
    color: #dcdee0;
    margin-bottom: 16px;
  }

  .empty-text {
    font-size: 16px;
    color: #969799;
    margin-bottom: 8px;
  }

  .empty-desc {
    font-size: 14px;
    color: #c8c9cc;
  }
}
</style>
