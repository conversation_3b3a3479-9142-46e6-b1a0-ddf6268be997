<template>
  <div class="page">
    <!-- 页面标题 -->
    <div class="page-header">
      <van-nav-bar
        title="我的展会"
        left-arrow
        @click-left="$router.back()"
      />
    </div>

    <!-- 参加新展会按钮 -->
    <div class="action-section">
      <van-button
        type="primary"
        size="large"
        block
        icon="plus"
        @click="goToJoinExhibition"
      >
        参加新展会
      </van-button>
    </div>

    <!-- 展会列表 -->
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
      >
        <div
          v-for="exhibition in exhibitions"
          :key="exhibition.id"
          class="exhibition-card"
          @click="goToExhibitionDetail(exhibition)"
        >
          <div class="exhibition-header">
            <div class="exhibition-info">
              <div class="exhibition-name">{{ exhibition.name }}</div>
              <div class="exhibition-venue">{{ exhibition.venueName }}</div>
            </div>
            <div class="exhibition-status">
              <span :class="['status-badge', getStatusClass(exhibition.status)]">
                {{ getStatusText(exhibition.status) }}
              </span>
            </div>
          </div>
          
          <div class="exhibition-details">
            <div class="detail-item">
              <van-icon name="location-o" class="icon" />
              <span>{{ exhibition.city }}</span>
            </div>
            <div class="detail-item">
              <van-icon name="calendar-o" class="icon" />
              <span>{{ formatDate(exhibition.startDate) }} - {{ formatDate(exhibition.endDate) }}</span>
            </div>
            <div class="detail-item" v-if="exhibition.participantType">
              <van-icon name="user-o" class="icon" />
              <span>{{ getParticipantTypeText(exhibition.participantType) }}</span>
            </div>
            <div class="detail-item">
              <van-icon name="service-o" class="icon" />
              <span>接待状态：</span>
              <van-switch
                v-model="exhibition.isFull"
                :active-value="0"
                :inactive-value="1"
                active-text="可预约"
                inactive-text="已满额"
                @click.stop
                @change="updateFullStatus(exhibition)"
              />
            </div>
          </div>

          <div class="exhibition-footer">
            <div class="join-date">
              参展时间：{{ formatDateTime(exhibition.joinedAt) }}
            </div>
            <div class="exhibition-actions">
              <van-button
                size="mini"
                type="primary"
                plain
                @click.stop="viewExhibitionDetail(exhibition)"
              >
                查看详情
              </van-button>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="!loading && exhibitions.length === 0" class="empty-state">
          <van-icon name="calendar-o" class="empty-icon" />
          <div class="empty-text">暂无参展记录</div>
          <div class="empty-desc">点击上方按钮参加新展会</div>
        </div>
      </van-list>
    </van-pull-refresh>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { getMyExhibitions, updateParticipantStatus } from '../api/exhibition'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const finished = ref(false)
const refreshing = ref(false)
const exhibitions = ref([])

// 分页参数
const page = ref(1)
const pageSize = 10

// 获取我的展会列表
const fetchExhibitions = async (isRefresh = false) => {
  try {
    if (isRefresh) {
      page.value = 1
      finished.value = false
    }

    const response = await getMyExhibitions({
      page: page.value,
      pageSize
    })
    
    const { list, pagination } = response.data

    if (isRefresh) {
      exhibitions.value = list
    } else {
      exhibitions.value.push(...list)
    }

    // 检查是否还有更多数据
    if (pagination.page >= pagination.totalPages) {
      finished.value = true
    } else {
      page.value++
    }
  } catch (error) {
    console.error('获取展会列表失败:', error)
    showToast(error.message || '获取数据失败')

    // 如果是429错误或其他错误，停止加载更多
    finished.value = true
  }
}

// 下拉刷新
const onRefresh = async () => {
  refreshing.value = true
  await fetchExhibitions(true)
  refreshing.value = false
}

// 上拉加载
const onLoad = async () => {
  if (finished.value) return
  loading.value = true
  try {
    await fetchExhibitions()
  } catch (error) {
    console.error('加载展会失败:', error)
    finished.value = true // 出错时停止加载更多
  } finally {
    loading.value = false
  }
}

// 获取状态样式类
const getStatusClass = (status) => {
  const statusMap = {
    1: 'upcoming',    // 未开始
    2: 'ongoing',     // 进行中
    3: 'ended'        // 已结束
  }
  return statusMap[status] || 'upcoming'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    1: '未开始',
    2: '进行中',
    3: '已结束'
  }
  return statusMap[status] || '未知'
}

// 获取参展类型文本
const getParticipantTypeText = (type) => {
  const typeMap = {
    'design': '设计公司',
    'factory': '工厂',
    'comprehensive': '综合商'
  }
  return typeMap[type] || '参展商'
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

// 格式化日期时间
const formatDateTime = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}

// 导航方法
const goToJoinExhibition = () => {
  router.push('/join-exhibition')
}

const goToExhibitionDetail = (exhibition) => {
  router.push(`/exhibition-detail/${exhibition.exhibitionId}`)
}

const viewExhibitionDetail = (exhibition) => {
  router.push(`/exhibition-detail/${exhibition.exhibitionId}`)
}

// 更新满额状态
const updateFullStatus = async (exhibition) => {
  try {
    await updateParticipantStatus(exhibition.id, exhibition.isFull)
    showToast(exhibition.isFull === 0 ? '已设置为可预约' : '已设置为满额')
  } catch (error) {
    // 如果更新失败，恢复原状态
    exhibition.isFull = exhibition.isFull === 0 ? 1 : 0
    showToast(error.message || '更新失败')
  }
}

// 页面初始化
onMounted(() => {
  fetchExhibitions(true)
})
</script>

<style lang="scss" scoped>
.page-header {
  background: white;
  border-bottom: 1px solid #ebedf0;
}

.action-section {
  padding: 16px;
  background: white;
  border-bottom: 1px solid #ebedf0;
}

.exhibition-card {
  background: white;
  margin: 8px 16px;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .exhibition-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;

    .exhibition-info {
      flex: 1;

      .exhibition-name {
        font-size: 16px;
        font-weight: bold;
        color: #323233;
        margin-bottom: 4px;
      }

      .exhibition-venue {
        font-size: 14px;
        color: #646566;
      }
    }

    .exhibition-status {
      .status-badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;

        &.upcoming {
          background: #e8f4ff;
          color: #1989fa;
        }

        &.ongoing {
          background: #e8f8e8;
          color: #07c160;
        }

        &.ended {
          background: #f5f5f5;
          color: #969799;
        }
      }
    }
  }

  .exhibition-details {
    margin-bottom: 12px;

    .detail-item {
      display: flex;
      align-items: center;
      margin-bottom: 6px;
      font-size: 14px;
      color: #646566;

      &:last-child {
        margin-bottom: 0;
      }

      .icon {
        margin-right: 6px;
        color: #969799;
      }
    }
  }

  .exhibition-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 12px;
    border-top: 1px solid #f0f0f0;

    .join-date {
      font-size: 12px;
      color: #969799;
    }

    .exhibition-actions {
      display: flex;
      gap: 8px;
    }
  }
}

.empty-state {
  text-align: center;
  padding: 60px 20px;

  .empty-icon {
    font-size: 64px;
    color: #dcdee0;
    margin-bottom: 16px;
  }

  .empty-text {
    font-size: 16px;
    color: #969799;
    margin-bottom: 8px;
  }

  .empty-desc {
    font-size: 14px;
    color: #c8c9cc;
  }
}
</style>
