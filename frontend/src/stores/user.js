import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { showToast } from 'vant'
import { login, getUserProfile } from '@/api/auth'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref(localStorage.getItem('token') || '')
  const userInfo = ref(null)
  const isInitialized = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const needCompleteProfile = computed(() => {
    return isLoggedIn.value && (!userInfo.value || !userInfo.value.userType)
  })

  // 设置Token
  const setToken = (newToken) => {
    token.value = newToken
    if (newToken) {
      localStorage.setItem('token', newToken)
    } else {
      localStorage.removeItem('token')
    }
  }

  // 设置用户信息
  const setUserInfo = (info) => {
    userInfo.value = info
  }

  // 微信登录
  const wechatLogin = async (code) => {
    try {
      const response = await login(code)
      const { token: newToken, userInfo: info, isNewUser } = response.data
      
      setToken(newToken)
      setUserInfo(info)
      
      showToast('登录成功')
      return { isNewUser }
    } catch (error) {
      showToast(error.message || '登录失败')
      throw error
    }
  }

  // 获取用户信息
  const fetchUserProfile = async () => {
    try {
      const response = await getUserProfile()
      setUserInfo(response.data)
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 如果获取用户信息失败，可能是token过期，清除登录状态
      logout()
    }
  }

  // 退出登录
  const logout = () => {
    setToken('')
    setUserInfo(null)
    isInitialized.value = false
    showToast('已退出登录')
  }

  // 初始化应用
  const initApp = async () => {
    if (isInitialized.value) return
    
    if (isLoggedIn.value) {
      try {
        await fetchUserProfile()
      } catch (error) {
        console.error('初始化用户信息失败:', error)
      }
    }
    
    isInitialized.value = true
  }

  return {
    // 状态
    token,
    userInfo,
    isInitialized,
    
    // 计算属性
    isLoggedIn,
    needCompleteProfile,
    
    // 方法
    setToken,
    setUserInfo,
    wechatLogin,
    fetchUserProfile,
    logout,
    initApp
  }
})
