/**
 * 城市列表常量
 */

// 主要城市列表（用于选择器）
export const CITY_OPTIONS = [
  { text: '上海', value: '上海' },
  { text: '北京', value: '北京' },
  { text: '深圳', value: '深圳' },
  { text: '广州', value: '广州' },
  { text: '杭州', value: '杭州' },
  { text: '成都', value: '成都' },
  { text: '武汉', value: '武汉' },
  { text: '南京', value: '南京' },
  { text: '苏州', value: '苏州' },
  { text: '天津', value: '天津' },
  { text: '重庆', value: '重庆' },
  { text: '西安', value: '西安' },
  { text: '青岛', value: '青岛' },
  { text: '大连', value: '大连' },
  { text: '宁波', value: '宁波' },
  { text: '厦门', value: '厦门' },
  { text: '福州', value: '福州' },
  { text: '济南', value: '济南' },
  { text: '长沙', value: '长沙' },
  { text: '郑州', value: '郑州' },
  { text: '沈阳', value: '沈阳' },
  { text: '哈尔滨', value: '哈尔滨' },
  { text: '长春', value: '长春' },
  { text: '石家庄', value: '石家庄' },
  { text: '太原', value: '太原' },
  { text: '呼和浩特', value: '呼和浩特' },
  { text: '兰州', value: '兰州' },
  { text: '银川', value: '银川' },
  { text: '西宁', value: '西宁' },
  { text: '乌鲁木齐', value: '乌鲁木齐' },
  { text: '拉萨', value: '拉萨' },
  { text: '昆明', value: '昆明' },
  { text: '贵阳', value: '贵阳' },
  { text: '南宁', value: '南宁' },
  { text: '海口', value: '海口' },
  { text: '三亚', value: '三亚' }
]

// 筛选用城市列表（包含"全部"选项）
export const FILTER_CITIES = ['全部', '上海', '北京', '深圳', '广州', '杭州', '成都', '武汉']

// 热门城市列表
export const HOT_CITIES = ['上海', '北京', '深圳', '广州', '杭州', '成都']

// 获取城市名称（根据value获取text）
export const getCityName = (value) => {
  const city = CITY_OPTIONS.find(item => item.value === value)
  return city ? city.text : value
}

// 检查是否为有效城市
export const isValidCity = (city) => {
  return CITY_OPTIONS.some(item => item.value === city || item.text === city)
}
