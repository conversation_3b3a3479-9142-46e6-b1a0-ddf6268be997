import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'

// 路由配置
const routes = [
  {
    path: '/',
    redirect: '/home'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { title: '登录' }
  },
  {
    path: '/user-type',
    name: 'UserType',
    component: () => import('@/views/UserType.vue'),
    meta: { title: '选择身份', requiresAuth: true }
  },
  {
    path: '/complete-profile',
    name: 'CompleteProfile',
    component: () => import('@/views/CompleteProfile.vue'),
    meta: { title: '完善信息', requiresAuth: true }
  },
  {
    path: '/home',
    name: 'Home',
    component: () => import('@/views/Home.vue'),
    meta: { title: '首页', requiresAuth: true, showTabbar: true }
  },
  {
    path: '/find-design',
    name: 'FindDesign',
    component: () => import('@/views/FindDesign.vue'),
    meta: { title: '找设计', requiresAuth: true, showTabbar: true }
  },
  {
    path: '/find-factory',
    name: 'FindFactory',
    component: () => import('@/views/FindFactory.vue'),
    meta: { title: '找工厂', requiresAuth: true, showTabbar: true }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('@/views/Profile.vue'),
    meta: { title: '我的', requiresAuth: true, showTabbar: true }
  },
  {
    path: '/company-detail/:type/:id',
    name: 'CompanyDetail',
    component: () => import('@/views/CompanyDetail.vue'),
    meta: { title: '公司详情', requiresAuth: true }
  },
  {
    path: '/my-info',
    name: 'MyInfo',
    component: () => import('@/views/MyInfo.vue'),
    meta: { title: '我的信息', requiresAuth: true }
  },
  {
    path: '/my-exhibitions',
    name: 'MyExhibitions',
    component: () => import('@/views/MyExhibitions.vue'),
    meta: { title: '我的展会', requiresAuth: true }
  },
  {
    path: '/join-exhibition',
    name: 'JoinExhibition',
    component: () => import('@/views/JoinExhibition.vue'),
    meta: { title: '参加新展会', requiresAuth: true }
  },
  {
    path: '/exhibition-detail/:id',
    name: 'ExhibitionDetail',
    component: () => import('@/views/ExhibitionDetail.vue'),
    meta: { title: '展会详情', requiresAuth: true }
  },
  {
    path: '/my-favorites',
    name: 'MyFavorites',
    component: () => import('@/views/MyFavorites.vue'),
    meta: { title: '我的收藏', requiresAuth: true }
  },
  {
    path: '/feedback',
    name: 'Feedback',
    component: () => import('@/views/Feedback.vue'),
    meta: { title: '意见反馈', requiresAuth: true }
  },
  {
    path: '/about',
    name: 'About',
    component: () => import('@/views/About.vue'),
    meta: { title: '关于我们', requiresAuth: true }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()
  
  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - 馆拼拼` : '馆拼拼'
  
  // 检查是否需要登录
  if (to.meta.requiresAuth) {
    if (!userStore.isLoggedIn) {
      // 未登录，跳转到登录页
      next('/login')
      return
    }
    
    // 检查是否需要完善信息
    if (userStore.needCompleteProfile && to.name !== 'UserType' && to.name !== 'CompleteProfile') {
      next('/user-type')
      return
    }
  }
  
  // 已登录用户访问登录页，跳转到首页
  if (to.name === 'Login' && userStore.isLoggedIn) {
    next('/home')
    return
  }
  
  next()
})

export default router
