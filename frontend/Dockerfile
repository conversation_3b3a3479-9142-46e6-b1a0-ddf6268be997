# 使用Node.js构建
FROM node:16-alpine

WORKDIR /app

# 复制所有文件
COPY . .

# 安装依赖并构建
RUN npm install && npm run build

# 使用nginx提供静态文件
FROM nginx:alpine

# 复制构建产物
COPY --from=0 /app/dist /usr/share/nginx/html

# 创建简单的nginx配置
RUN echo 'server { \
    listen 80; \
    location / { \
        root /usr/share/nginx/html; \
        index index.html; \
        try_files $uri $uri/ /index.html; \
    } \
}' > /etc/nginx/conf.d/default.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
