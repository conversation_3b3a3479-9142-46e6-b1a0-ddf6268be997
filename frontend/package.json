{"name": "guanpinpin-frontend", "version": "1.0.0", "description": "馆拼拼H5前端应用", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "axios": "^1.6.2", "vant": "^4.8.1", "@vant/touch-emulator": "^1.4.0", "weixin-js-sdk": "^1.6.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.2", "vite": "^4.5.3", "eslint": "^8.55.0", "eslint-plugin-vue": "^9.19.2", "prettier": "^3.1.1", "sass": "^1.69.5"}}