# 馆拼拼数据库设计文档

## 1. 数据库概述

### 1.1 数据库选择
- **数据库类型**: MySQL 8.0
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_unicode_ci
- **存储引擎**: InnoDB

### 1.2 命名规范
- **表名**: 小写字母，单词间用下划线分隔
- **字段名**: 小写字母，单词间用下划线分隔
- **索引名**: idx_表名_字段名
- **外键名**: fk_表名_字段名

## 2. 数据表设计

### 2.1 用户信息表 (users)
```sql
CREATE TABLE users (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
  openid VARCHAR(100) UNIQUE NOT NULL COMMENT '微信openid',
  unionid VARCHAR(100) COMMENT '微信unionid',
  nickname VARCHAR(100) COMMENT '用户昵称',
  phone VARCHAR(20) COMMENT '手机号',
  user_type TINYINT NOT NULL COMMENT '用户类型：1参展商 2设计公司 3工厂 4综合商',
  is_deleted TINYINT DEFAULT 0 COMMENT '是否删除：0否 1是',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_users_openid (openid),
  INDEX idx_users_unionid (unionid),
  INDEX idx_users_user_type (user_type),
  INDEX idx_users_is_deleted (is_deleted)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户信息表';
```

### 2.2 展会类别表 (exhibition_categories)
```sql
CREATE TABLE exhibition_categories (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '类别ID',
  name VARCHAR(50) NOT NULL COMMENT '类别名称',
  is_deleted TINYINT DEFAULT 0 COMMENT '是否删除：0否 1是',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_categories_name (name),
  INDEX idx_categories_is_deleted (is_deleted)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='展会类别表';
```

### 2.3 展馆信息表 (venues)
```sql
CREATE TABLE venues (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '展馆ID',
  city VARCHAR(50) NOT NULL COMMENT '所在城市',
  name VARCHAR(100) NOT NULL COMMENT '展馆名称',
  address VARCHAR(200) COMMENT '展馆地址',
  is_deleted TINYINT DEFAULT 0 COMMENT '是否删除：0否 1是',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_venues_city (city),
  INDEX idx_venues_name (name),
  INDEX idx_venues_is_deleted (is_deleted)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='展馆信息表';
```

### 2.4 展会信息表 (exhibitions)
```sql
CREATE TABLE exhibitions (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '展会ID',
  venue_id INT NOT NULL COMMENT '展馆ID',
  name VARCHAR(100) NOT NULL COMMENT '展会名称',
  start_date DATE NOT NULL COMMENT '开始日期',
  end_date DATE NOT NULL COMMENT '结束日期',
  category_id INT NOT NULL COMMENT '展会类别ID',
  status TINYINT DEFAULT 0 COMMENT '状态：0未开始 1进行中 9已结束',
  is_deleted TINYINT DEFAULT 0 COMMENT '是否删除：0否 1是',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_exhibitions_venue_id (venue_id),
  INDEX idx_exhibitions_category_id (category_id),
  INDEX idx_exhibitions_start_date (start_date),
  INDEX idx_exhibitions_end_date (end_date),
  INDEX idx_exhibitions_status (status),
  INDEX idx_exhibitions_is_deleted (is_deleted),
  INDEX idx_exhibitions_date_range (start_date, end_date),
  
  FOREIGN KEY fk_exhibitions_venue_id (venue_id) REFERENCES venues(id),
  FOREIGN KEY fk_exhibitions_category_id (category_id) REFERENCES exhibition_categories(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='展会信息表';
```

### 2.5 工厂信息表 (factories)
```sql
CREATE TABLE factories (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '工厂ID',
  user_id INT NOT NULL COMMENT '用户ID',
  name VARCHAR(100) NOT NULL COMMENT '工厂名称',
  contact_person VARCHAR(50) COMMENT '联系人',
  contact_phone VARCHAR(20) COMMENT '联系电话',
  city VARCHAR(50) COMMENT '所在城市',
  specialties TEXT COMMENT '特长：钢结构、木结构等',
  level TINYINT DEFAULT 1 COMMENT '级别：1-5星',
  is_deleted TINYINT DEFAULT 0 COMMENT '是否删除：0否 1是',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

  INDEX idx_factories_user_id (user_id),
  INDEX idx_factories_city (city),
  INDEX idx_factories_level (level),
  INDEX idx_factories_is_deleted (is_deleted),

  FOREIGN KEY fk_factories_user_id (user_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工厂信息表';
```

### 2.6 设计公司表 (design_companies)
```sql
CREATE TABLE design_companies (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '设计公司ID',
  user_id INT NOT NULL COMMENT '用户ID',
  name VARCHAR(100) NOT NULL COMMENT '公司名称',
  contact_person VARCHAR(50) COMMENT '联系人',
  contact_phone VARCHAR(20) COMMENT '联系电话',
  city VARCHAR(50) COMMENT '所在城市',
  level TINYINT DEFAULT 1 COMMENT '级别：1-5星',
  is_deleted TINYINT DEFAULT 0 COMMENT '是否删除：0否 1是',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

  INDEX idx_design_companies_user_id (user_id),
  INDEX idx_design_companies_city (city),
  INDEX idx_design_companies_level (level),
  INDEX idx_design_companies_is_deleted (is_deleted),

  FOREIGN KEY fk_design_companies_user_id (user_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='设计公司表';
```

### 2.7 参展商表 (exhibitors)
```sql
CREATE TABLE exhibitors (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '参展商ID',
  user_id INT NOT NULL COMMENT '用户ID',
  name VARCHAR(100) NOT NULL COMMENT '公司名称',
  contact_person VARCHAR(50) COMMENT '联系人',
  contact_phone VARCHAR(20) COMMENT '联系电话',
  exhibition_category_id INT COMMENT '参展类别ID',
  is_deleted TINYINT DEFAULT 0 COMMENT '是否删除：0否 1是',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

  INDEX idx_exhibitors_user_id (user_id),
  INDEX idx_exhibitors_category_id (exhibition_category_id),
  INDEX idx_exhibitors_is_deleted (is_deleted),

  FOREIGN KEY fk_exhibitors_user_id (user_id) REFERENCES users(id),
  FOREIGN KEY fk_exhibitors_category_id (exhibition_category_id) REFERENCES exhibition_categories(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='参展商表';
```

### 2.8 参展信息表 (exhibition_participants)
```sql
CREATE TABLE exhibition_participants (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '参展信息ID',
  exhibition_id INT NOT NULL COMMENT '展会ID',
  participant_id INT NOT NULL COMMENT '参展方ID（对应工厂或设计公司ID）',
  participant_type TINYINT NOT NULL COMMENT '参展方类型：2设计公司 3工厂 4综合商',
  is_full TINYINT DEFAULT 0 COMMENT '是否满额：0否 1是',
  is_deleted TINYINT DEFAULT 0 COMMENT '是否删除：0否 1是',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

  INDEX idx_participants_exhibition_id (exhibition_id),
  INDEX idx_participants_participant_id (participant_id),
  INDEX idx_participants_participant_type (participant_type),
  INDEX idx_participants_is_full (is_full),
  INDEX idx_participants_is_deleted (is_deleted),
  INDEX idx_participants_exhibition_type (exhibition_id, participant_type),

  FOREIGN KEY fk_participants_exhibition_id (exhibition_id) REFERENCES exhibitions(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='参展信息表';
```

### 2.9 用户收藏表 (user_favorites)
```sql
CREATE TABLE user_favorites (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '收藏ID',
  user_id INT NOT NULL COMMENT '用户ID',
  favorite_type TINYINT NOT NULL COMMENT '收藏类型：2设计公司 3工厂 4综合商',
  favorite_id INT NOT NULL COMMENT '收藏对象ID',
  is_deleted TINYINT DEFAULT 0 COMMENT '是否删除：0否 1是',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

  INDEX idx_favorites_user_id (user_id),
  INDEX idx_favorites_type_id (favorite_type, favorite_id),
  INDEX idx_favorites_is_deleted (is_deleted),
  UNIQUE KEY uk_favorites_user_type_id (user_id, favorite_type, favorite_id),

  FOREIGN KEY fk_favorites_user_id (user_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户收藏表';
```

### 2.10 管理员表 (admins)
```sql
CREATE TABLE admins (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '管理员ID',
  username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
  password VARCHAR(255) NOT NULL COMMENT '密码（加密）',
  is_deleted TINYINT DEFAULT 0 COMMENT '是否删除：0否 1是',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_admins_username (username),
  INDEX idx_admins_is_deleted (is_deleted)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员表';
```

## 3. 初始数据

### 3.1 展会类别初始数据
```sql
INSERT INTO exhibition_categories (name) VALUES
('服装展'),
('水展'),
('汽车展'),
('科技展'),
('箱包展'),
('家具展'),
('建材展'),
('食品展'),
('医疗展'),
('教育展');
```

### 3.2 管理员初始数据
```sql
-- 密码为 gpp13141234 的bcrypt加密值（需要在代码中生成）
INSERT INTO admins (username, password) VALUES
('admin', 'gpp13141234'); -- 实际部署时需要用bcrypt加密
```

### 3.3 示例展馆数据
```sql
INSERT INTO venues (city, name, address) VALUES
('上海', '新国际博览中心', '上海市浦东新区龙阳路2345号'),
('上海', '国家会展中心', '上海市青浦区崧泽大道333号'),
('北京', '中国国际展览中心', '北京市朝阳区北三环东路6号'),
('深圳', '深圳会展中心', '深圳市福田区福华三路111号'),
('广州', '中国进出口商品交易会展馆', '广州市海珠区阅江中路380号');
```

## 4. 数据库优化策略

### 4.1 索引优化
- **主键索引**: 所有表都有自增主键
- **唯一索引**: openid、用户收藏组合
- **复合索引**: 展会日期范围、参展信息查询
- **外键索引**: 所有外键字段都有索引

### 4.2 查询优化
- **分页查询**: 使用LIMIT和OFFSET
- **条件查询**: 合理使用WHERE条件
- **排序查询**: 在索引字段上排序
- **连接查询**: 优化JOIN操作

### 4.3 存储优化
- **字段类型**: 选择合适的数据类型
- **字段长度**: 根据实际需要设置长度
- **NULL值**: 尽量避免NULL值
- **默认值**: 设置合理的默认值

## 5. 数据安全策略

### 5.1 数据备份
- **备份频率**: 每日自动备份
- **备份保留**: 保留30天备份
- **备份验证**: 定期验证备份完整性

### 5.2 权限控制
- **用户权限**: 应用用户只有必要权限
- **管理权限**: 管理员有完整权限
- **网络访问**: 限制数据库网络访问

### 5.3 数据加密
- **密码加密**: 使用bcrypt加密
- **敏感数据**: 手机号等敏感信息考虑加密
- **传输加密**: 使用SSL连接

## 6. 性能监控

### 6.1 慢查询监控
- **慢查询日志**: 开启慢查询日志
- **查询时间**: 监控查询执行时间
- **查询优化**: 定期优化慢查询

### 6.2 连接池监控
- **连接数**: 监控数据库连接数
- **连接池**: 合理配置连接池大小
- **连接超时**: 设置合理的连接超时

### 6.3 存储监控
- **磁盘空间**: 监控磁盘使用情况
- **表大小**: 监控表数据增长
- **索引大小**: 监控索引空间占用

## 7. 数据迁移策略

### 7.1 版本控制
- **迁移脚本**: 使用版本化迁移脚本
- **回滚策略**: 每个迁移都有回滚方案
- **测试验证**: 迁移前充分测试

### 7.2 数据一致性
- **事务控制**: 使用事务保证一致性
- **外键约束**: 保证数据完整性
- **数据验证**: 迁移后验证数据正确性
