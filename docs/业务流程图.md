# 馆拼拼业务流程图设计

## 1. 用户注册登录流程

### 1.1 微信授权登录流程
```mermaid
graph TD
    A[用户打开小程序] --> B[小程序加载H5页面]
    B --> C{检查本地Token}
    C -->|有效Token| D[直接进入首页]
    C -->|无Token或过期| E[显示登录页面]
    E --> F[点击微信授权登录]
    F --> G[调用微信JS-SDK]
    G --> H[获取用户openid和基本信息]
    H --> I[发送到后端验证]
    I --> J{用户是否已注册}
    J -->|已注册| K[返回JWT Token]
    J -->|未注册| L[跳转用户类型选择页]
    K --> M[保存Token到本地]
    M --> D
    L --> N[选择用户类型]
    N --> O[填写公司基本信息]
    O --> P[提交注册信息]
    P --> Q[后端创建用户记录]
    Q --> K
```

### 1.2 用户类型选择和信息完善流程
```mermaid
graph TD
    A[新用户选择类型] --> B{选择用户类型}
    B -->|参展商| C[填写参展商信息]
    B -->|设计公司| D[填写设计公司信息]
    B -->|工厂| E[填写工厂信息]
    B -->|综合商| F[同时填写设计公司和工厂信息]
    
    C --> G[参展商信息表单]
    G --> H[公司名称、联系人、联系方式、参展类别]
    
    D --> I[设计公司信息表单]
    I --> J[公司名称、联系人、联系方式、所在城市]
    
    E --> K[工厂信息表单]
    K --> L[公司名称、联系人、联系方式、所在城市、特长]
    
    F --> M[综合商信息表单]
    M --> N[填写完整的设计公司和工厂信息]
    
    H --> O[提交信息]
    J --> O
    L --> O
    N --> O
    
    O --> P[后端验证信息]
    P --> Q{验证是否通过}
    Q -->|通过| R[创建对应业务表记录]
    Q -->|失败| S[返回错误信息]
    R --> T[注册完成，进入首页]
    S --> U[显示错误，重新填写]
```

## 2. 搜索业务流程

### 2.1 首页搜索流程
```mermaid
graph TD
    A[用户进入首页] --> B[显示搜索表单]
    B --> C[选择城市]
    C --> D[选择日期范围]
    D --> E[选择展馆]
    E --> F[选择查找类型：找设计/找工厂]
    F --> G[点击开始搜索]
    G --> H[根据条件查询展会]
    H --> I{是否找到匹配展会}
    I -->|找到| J[获取展会ID]
    I -->|未找到| K[显示无结果]
    J --> L{查找类型判断}
    L -->|找设计| M[查询参展的设计公司]
    L -->|找工厂| N[查询参展的工厂]
    M --> O[跳转到找设计页面显示结果]
    N --> P[跳转到找工厂页面显示结果]
```

### 2.2 找设计/找工厂页面搜索流程
```mermaid
graph TD
    A[进入找设计/找工厂页面] --> B[显示搜索条件]
    B --> C[城市下拉选择]
    C --> D[日期选择器]
    D --> E[关键字输入框]
    E --> F[展馆切换按钮]
    F --> G[执行搜索]
    G --> H[后端查询逻辑]
    
    H --> I[根据城市、日期、展馆查找展会]
    I --> J{找到展会?}
    J -->|否| K[返回空结果]
    J -->|是| L[根据展会ID查询参展信息]
    L --> M[根据参展方类型过滤]
    M --> N[获取对应的公司信息]
    N --> O[按级别排序]
    O --> P[前端关键字过滤]
    P --> Q[显示结果列表]
    
    Q --> R[用户点击列表项]
    R --> S[跳转到详情页面]
```

## 3. 详情页面和收藏流程

### 3.3 公司详情页面流程
```mermaid
graph TD
    A[用户点击公司列表项] --> B[获取公司ID和类型]
    B --> C[跳转到详情页面]
    C --> D[加载公司详细信息]
    D --> E[显示公司信息]
    E --> F[公司名称、联系人、联系方式]
    F --> G[所在城市、级别显示]
    G --> H{公司类型判断}
    H -->|工厂| I[显示特长信息]
    H -->|设计公司| J[显示设计公司特有信息]
    I --> K[显示操作按钮]
    J --> K
    K --> L[打电话按钮]
    K --> M[收藏/取消收藏按钮]
    
    L --> N[调用手机拨号功能]
    M --> O{当前收藏状态}
    O -->|未收藏| P[添加到收藏]
    O -->|已收藏| Q[取消收藏]
    P --> R[更新收藏状态显示]
    Q --> R
```

### 3.4 收藏管理流程
```mermaid
graph TD
    A[用户进入我的页面] --> B[点击收藏菜单]
    B --> C[加载用户收藏列表]
    C --> D[按收藏类型分组显示]
    D --> E[设计公司收藏列表]
    D --> F[工厂收藏列表]
    D --> G[综合商收藏列表]
    
    E --> H[点击收藏项]
    F --> H
    G --> H
    
    H --> I[跳转到对应详情页面]
    I --> J[显示详细信息]
    J --> K[可以取消收藏]
    K --> L[更新收藏列表]
```

## 4. 用户信息管理流程

### 4.1 个人信息修改流程
```mermaid
graph TD
    A[用户进入我的页面] --> B[点击我的信息]
    B --> C[加载当前用户信息]
    C --> D[显示用户基本信息]
    D --> E[昵称、手机号等]
    E --> F[当前用户类型]
    F --> G[对应的公司信息]
    G --> H[点击修改按钮]
    H --> I{修改类型选择}
    I -->|修改基本信息| J[修改昵称、手机号]
    I -->|修改用户类型| K[重新选择用户类型]
    I -->|修改公司信息| L[修改对应公司信息]
    
    J --> M[提交修改]
    K --> N[类型变更确认]
    L --> M
    
    N --> O[更新用户类型]
    O --> P[更新或创建对应公司记录]
    P --> M
    
    M --> Q[后端验证和更新]
    Q --> R{更新是否成功}
    R -->|成功| S[显示成功提示]
    R -->|失败| T[显示错误信息]
    S --> U[刷新页面信息]
    T --> V[返回修改页面]
```

## 5. 后台管理流程

### 5.1 后台登录流程
```mermaid
graph TD
    A[管理员访问后台] --> B[显示登录页面]
    B --> C[输入用户名密码]
    C --> D[admin / gpp13141234]
    D --> E[提交登录]
    E --> F[后端验证]
    F --> G{验证是否通过}
    G -->|通过| H[生成管理员Token]
    G -->|失败| I[显示错误信息]
    H --> J[跳转到后台首页]
    I --> K[重新输入]
```

### 5.2 数据管理流程
```mermaid
graph TD
    A[管理员登录后台] --> B[选择管理模块]
    B --> C[展会类别管理]
    B --> D[展馆信息管理]
    B --> E[展会信息管理]
    B --> F[用户信息管理]
    B --> G[公司信息管理]
    
    C --> H[增删改查展会类别]
    D --> I[增删改查展馆信息]
    E --> J[增删改查展会信息]
    F --> K[查看修改用户信息]
    G --> L[查看修改公司信息]
    
    H --> M[数据验证]
    I --> M
    J --> M
    K --> M
    L --> M
    
    M --> N{验证是否通过}
    N -->|通过| O[执行数据库操作]
    N -->|失败| P[显示错误信息]
    O --> Q[操作成功提示]
    P --> R[返回修改页面]
```

## 6. 数据同步和状态更新流程

### 6.1 展会状态更新流程（手动）
```mermaid
graph TD
    A[管理员进入展会管理] --> B[查看展会列表]
    B --> C[检查展会日期]
    C --> D{展会状态判断}
    D -->|开始日期未到| E[状态：未开始]
    D -->|在开始和结束之间| F[状态：进行中]
    D -->|结束日期已过| G[状态：已结束]
    
    E --> H[手动更新状态]
    F --> H
    G --> H
    
    H --> I[批量更新数据库]
    I --> J[更新成功提示]
```

### 6.2 参展信息管理流程
```mermaid
graph TD
    A[管理员管理参展信息] --> B[选择展会]
    B --> C[查看参展公司列表]
    C --> D[添加参展公司]
    C --> E[修改参展状态]
    C --> F[删除参展记录]
    
    D --> G[选择公司类型]
    G --> H[选择具体公司]
    H --> I[设置参展状态]
    I --> J[保存参展记录]
    
    E --> K[修改是否满额状态]
    K --> L[更新数据库]
    
    F --> M[软删除参展记录]
    M --> L
    
    J --> L
    L --> N[操作完成]
```

## 7. 错误处理和异常流程

### 7.1 网络错误处理流程
```mermaid
graph TD
    A[用户操作触发API请求] --> B[发送HTTP请求]
    B --> C{网络是否正常}
    C -->|正常| D[服务器处理请求]
    C -->|异常| E[显示网络错误提示]
    
    D --> F{服务器响应}
    F -->|成功| G[处理返回数据]
    F -->|错误| H[显示服务器错误信息]
    
    E --> I[提供重试按钮]
    H --> I
    
    I --> J[用户点击重试]
    J --> B
    
    G --> K[更新页面内容]
```

### 7.2 数据验证错误处理
```mermaid
graph TD
    A[用户提交表单] --> B[前端数据验证]
    B --> C{前端验证是否通过}
    C -->|失败| D[显示前端验证错误]
    C -->|通过| E[发送到后端]
    
    E --> F[后端数据验证]
    F --> G{后端验证是否通过}
    G -->|失败| H[返回验证错误信息]
    G -->|通过| I[执行业务逻辑]
    
    D --> J[用户修改数据]
    H --> J
    
    J --> A
    
    I --> K[操作成功]
```
