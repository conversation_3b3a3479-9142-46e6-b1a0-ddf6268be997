# 馆拼拼UI设计规范

## 1. 设计原则

### 1.1 设计理念
- **简洁高效**: 界面简洁，操作高效，符合B2B用户使用习惯
- **移动优先**: 针对手机端优化，适配微信小程序环境
- **品牌一致**: 保持品牌色彩和视觉风格的一致性
- **易用性**: 降低学习成本，提高用户操作效率

### 1.2 色彩规范
```css
/* 主色调 */
--primary-color: #1989fa;      /* 主蓝色 - 按钮、链接 */
--primary-light: #66b1ff;     /* 浅蓝色 - 悬停状态 */
--primary-dark: #0960bd;      /* 深蓝色 - 按下状态 */

/* 辅助色 */
--success-color: #07c160;     /* 成功绿色 */
--warning-color: #ff976a;     /* 警告橙色 */
--danger-color: #ee0a24;      /* 错误红色 */
--info-color: #909399;        /* 信息灰色 */

/* 中性色 */
--text-primary: #323233;      /* 主要文字 */
--text-regular: #646566;      /* 常规文字 */
--text-secondary: #969799;    /* 次要文字 */
--text-placeholder: #c8c9cc;  /* 占位文字 */

/* 背景色 */
--bg-primary: #ffffff;        /* 主背景 */
--bg-secondary: #f7f8fa;      /* 次背景 */
--bg-tertiary: #ebedf0;       /* 三级背景 */

/* 边框色 */
--border-color: #ebedf0;      /* 默认边框 */
--border-light: #f2f3f5;      /* 浅色边框 */
```

### 1.3 字体规范
```css
/* 字体大小 */
--font-size-xs: 10px;         /* 极小字体 */
--font-size-sm: 12px;         /* 小字体 */
--font-size-md: 14px;         /* 中等字体 */
--font-size-lg: 16px;         /* 大字体 */
--font-size-xl: 18px;         /* 超大字体 */

/* 字体粗细 */
--font-weight-normal: 400;    /* 常规 */
--font-weight-medium: 500;    /* 中等 */
--font-weight-bold: 600;      /* 粗体 */

/* 行高 */
--line-height-xs: 14px;
--line-height-sm: 18px;
--line-height-md: 20px;
--line-height-lg: 22px;
--line-height-xl: 24px;
```

### 1.4 间距规范
```css
/* 间距系统 */
--spacing-xs: 4px;            /* 极小间距 */
--spacing-sm: 8px;            /* 小间距 */
--spacing-md: 12px;           /* 中等间距 */
--spacing-lg: 16px;           /* 大间距 */
--spacing-xl: 20px;           /* 超大间距 */
--spacing-xxl: 24px;          /* 极大间距 */
```

## 2. 页面布局设计

### 2.1 整体布局结构
```
┌─────────────────────────────────┐
│           状态栏区域             │  <- 微信小程序状态栏
├─────────────────────────────────┤
│           导航栏区域             │  <- 页面标题和返回按钮
├─────────────────────────────────┤
│                                │
│                                │
│           内容区域              │  <- 主要内容区域
│                                │
│                                │
├─────────────────────────────────┤
│           底部导航栏             │  <- Tab导航（首页、找设计、找工厂、我的）
└─────────────────────────────────┘
```

### 2.2 导航栏设计
- **高度**: 44px
- **背景色**: #ffffff
- **标题**: 居中显示，字体大小18px，颜色#323233
- **返回按钮**: 左侧，使用Vant的van-icon组件
- **右侧操作**: 根据页面需要显示操作按钮

### 2.3 底部Tab导航设计
- **高度**: 50px
- **背景色**: #ffffff
- **边框**: 顶部1px边框，颜色#ebedf0
- **Tab项**: 4个等宽Tab
- **图标**: 使用Vant内置图标
- **文字**: 12px，未选中#969799，选中#1989fa

## 3. 核心页面设计

### 3.1 登录页面设计
```
┌─────────────────────────────────┐
│              登录               │
├─────────────────────────────────┤
│                                │
│        [馆拼拼 LOGO]            │
│                                │
│      欢迎使用馆拼拼平台          │
│    连接参展商、设计公司、工厂    │
│                                │
│                                │
│    ┌─────────────────────┐     │
│    │   微信授权登录       │     │  <- 主按钮样式
│    └─────────────────────┘     │
│                                │
│                                │
└─────────────────────────────────┘
```

### 3.2 用户类型选择页面
```
┌─────────────────────────────────┐
│            选择身份              │
├─────────────────────────────────┤
│                                │
│      请选择您的身份类型          │
│                                │
│    ┌─────────────────────┐     │
│    │  📋  参展商          │     │
│    │  参加展会的企业       │     │
│    └─────────────────────┘     │
│                                │
│    ┌─────────────────────┐     │
│    │  🎨  设计公司        │     │
│    │  提供展台设计服务     │     │
│    └─────────────────────┘     │
│                                │
│    ┌─────────────────────┐     │
│    │  🏭  工厂           │     │
│    │  提供展台搭建服务     │     │
│    └─────────────────────┘     │
│                                │
│    ┌─────────────────────┐     │
│    │  🏢  综合商          │     │
│    │  设计+搭建一体化服务  │     │
│    └─────────────────────┘     │
│                                │
└─────────────────────────────────┘
```

### 3.3 首页设计
```
┌─────────────────────────────────┐
│              首页               │
├─────────────────────────────────┤
│                                │
│    ┌─────────────────────┐     │
│    │     Banner轮播图     │     │  <- 展示金牌服务商
│    └─────────────────────┘     │
│                                │
│         快速搜索                │
│    ┌─────────────────────┐     │
│    │ 城市 ▼ │ 日期 📅    │     │
│    ├─────────┼───────────┤     │
│    │ 展馆 ▼ │ 找什么 ▼   │     │
│    └─────────┴───────────┘     │
│    ┌─────────────────────┐     │
│    │      开始搜索        │     │  <- 主按钮
│    └─────────────────────┘     │
│                                │
│         近期展会                │
│    ┌─────────────────────┐     │
│    │ 🏢 第4届上海网络安全博览会 │
│    │    新国际博览中心      │     │
│    │    2025-06-05 ~ 06-07 │     │
│    │              [查看详情] │     │
│    └─────────────────────┘     │
│                                │
│    ┌─────────────────────┐     │
│    │ 🏢 GPOWER2025动力展   │     │
│    │    新国际博览中心      │     │
│    │    2025-06-11 ~ 06-13 │     │
│    │              [查看详情] │     │
│    └─────────────────────┘     │
│                                │
├─────────────────────────────────┤
│ 🏠首页 🎨找设计 🏭找工厂 👤我的 │
└─────────────────────────────────┘
```

### 3.4 找设计/找工厂页面设计
```
┌─────────────────────────────────┐
│             找设计               │
├─────────────────────────────────┤
│                                │
│    ┌─────┬─────┬─────┬─────┐   │
│    │城市▼│日期📅│关键字│展馆▼│   │  <- 搜索条件栏
│    └─────┴─────┴─────┴─────┘   │
│                                │
│         搜索结果 (12家)          │
│                                │
│    ┌─────────────────────┐     │
│    │ 🏢 上海XX设计公司     │     │
│    │ 👤 张经理 📞 138****8888 │
│    │ 📍 上海市 ⭐⭐⭐⭐⭐    │
│    │              💖 [收藏] │     │
│    └─────────────────────┘     │
│                                │
│    ┌─────────────────────┐     │
│    │ 🏢 北京YY设计公司     │     │
│    │ 👤 李经理 📞 139****9999 │
│    │ 📍 北京市 ⭐⭐⭐⭐      │
│    │              💖 [收藏] │     │
│    └─────────────────────┘     │
│                                │
│    ┌─────────────────────┐     │
│    │ 🏢 深圳ZZ设计公司     │     │
│    │ 👤 王经理 📞 137****7777 │
│    │ 📍 深圳市 ⭐⭐⭐        │
│    │              💖 [收藏] │     │
│    └─────────────────────┘     │
│                                │
├─────────────────────────────────┤
│ 🏠首页 🎨找设计 🏭找工厂 👤我的 │
└─────────────────────────────────┘
```

### 3.5 公司详情页面设计
```
┌─────────────────────────────────┐
│          ← 公司详情              │
├─────────────────────────────────┤
│                                │
│    ┌─────────────────────┐     │
│    │ 🏢 上海XX设计公司     │     │
│    │                    💖│     │  <- 收藏按钮
│    └─────────────────────┘     │
│                                │
│         基本信息                │
│    ┌─────────────────────┐     │
│    │ 👤 联系人：张经理     │     │
│    │ 📞 联系电话：138****8888 │
│    │ 📍 所在城市：上海市    │     │
│    │ ⭐ 服务级别：⭐⭐⭐⭐⭐ │     │
│    │ 🏷️ 特长：钢结构、木结构 │     │  <- 仅工厂显示
│    └─────────────────────┘     │
│                                │
│         联系方式                │
│    ┌─────────────────────┐     │
│    │      📞 拨打电话      │     │  <- 主按钮
│    └─────────────────────┘     │
│                                │
│                                │
│                                │
│                                │
│                                │
│                                │
│                                │
│                                │
│                                │
└─────────────────────────────────┘
```

### 3.6 我的页面设计
```
┌─────────────────────────────────┐
│              我的               │
├─────────────────────────────────┤
│                                │
│    ┌─────────────────────┐     │
│    │ 👤 张三              │     │
│    │ 📞 138****8888       │     │
│    │ 🏢 参展商            │     │
│    └─────────────────────┘     │
│                                │
│    ┌─────────────────────┐     │
│    │ 📋 我的信息     >    │     │
│    └─────────────────────┘     │
│                                │
│    ┌─────────────────────┐     │
│    │ 💖 我的收藏     >    │     │
│    └─────────────────────┘     │
│                                │
│    ┌─────────────────────┐     │
│    │ 💬 意见反馈     >    │     │
│    └─────────────────────┘     │
│                                │
│    ┌─────────────────────┐     │
│    │ ℹ️ 关于我们     >    │     │
│    └─────────────────────┘     │
│                                │
│    ┌─────────────────────┐     │
│    │ 🚪 退出登录          │     │  <- 危险按钮样式
│    └─────────────────────┘     │
│                                │
├─────────────────────────────────┤
│ 🏠首页 🎨找设计 🏭找工厂 👤我的 │
└─────────────────────────────────┘
```

## 4. 组件设计规范

### 4.1 按钮组件
```css
/* 主按钮 */
.btn-primary {
  background: #1989fa;
  color: #ffffff;
  border-radius: 6px;
  height: 44px;
  font-size: 16px;
  font-weight: 500;
}

/* 次要按钮 */
.btn-secondary {
  background: #ffffff;
  color: #1989fa;
  border: 1px solid #1989fa;
  border-radius: 6px;
  height: 44px;
  font-size: 16px;
}

/* 危险按钮 */
.btn-danger {
  background: #ee0a24;
  color: #ffffff;
  border-radius: 6px;
  height: 44px;
  font-size: 16px;
}
```

### 4.2 卡片组件
```css
.card {
  background: #ffffff;
  border-radius: 8px;
  padding: 16px;
  margin: 8px 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 16px;
  font-weight: 500;
  color: #323233;
  margin-bottom: 8px;
}

.card-content {
  font-size: 14px;
  color: #646566;
  line-height: 20px;
}
```

### 4.3 列表项组件
```css
.list-item {
  background: #ffffff;
  padding: 12px 16px;
  border-bottom: 1px solid #ebedf0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item-icon {
  width: 20px;
  height: 20px;
  margin-right: 12px;
}

.list-item-content {
  flex: 1;
}

.list-item-title {
  font-size: 16px;
  color: #323233;
  margin-bottom: 4px;
}

.list-item-desc {
  font-size: 14px;
  color: #969799;
}
```

## 5. 交互设计规范

### 5.1 页面跳转动画
- **进入动画**: 从右侧滑入，持续时间300ms
- **退出动画**: 向右侧滑出，持续时间300ms
- **缓动函数**: ease-out

### 5.2 按钮交互状态
- **正常状态**: 默认样式
- **悬停状态**: 透明度0.8
- **按下状态**: 透明度0.6，轻微缩放0.98
- **禁用状态**: 透明度0.5，不可点击

### 5.3 加载状态设计
- **页面加载**: 使用Vant的Loading组件，居中显示
- **列表加载**: 底部显示"加载中..."文字
- **按钮加载**: 按钮内显示loading图标

### 5.4 空状态设计
- **无数据**: 显示空状态图标和提示文字
- **网络错误**: 显示错误图标和重试按钮
- **搜索无结果**: 显示搜索无结果提示

## 6. 响应式设计

### 6.1 屏幕适配
- **设计基准**: 375px宽度（iPhone X）
- **最小支持**: 320px宽度
- **最大支持**: 414px宽度
- **单位使用**: 优先使用vw/vh，关键尺寸使用px

### 6.2 字体适配
- **基础字体**: 14px
- **缩放比例**: 根据屏幕宽度动态调整
- **最小字体**: 12px
- **最大字体**: 18px

## 7. 无障碍设计

### 7.1 颜色对比度
- **文字对比度**: 至少4.5:1
- **大文字对比度**: 至少3:1
- **图标对比度**: 至少3:1

### 7.2 触摸目标
- **最小尺寸**: 44px × 44px
- **推荐尺寸**: 48px × 48px
- **间距**: 相邻触摸目标间距至少8px

### 7.3 文字可读性
- **行高**: 至少1.4倍字体大小
- **段落间距**: 至少0.5倍行高
- **字符间距**: 正常或稍微增加
