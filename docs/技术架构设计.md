# 馆拼拼项目技术架构设计

## 1. 项目概述
馆拼拼是一个连接参展商、设计公司和工厂的B2B平台，通过微信小程序嵌入H5页面的方式提供服务。

## 2. 技术栈选择

### 2.1 前端技术栈
- **框架**: Vue 3 + Vite
- **UI组件库**: Vant 4 (移动端UI组件库)
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **HTTP客户端**: Axios
- **微信集成**: 微信JS-SDK
- **构建工具**: Vite
- **CSS预处理器**: SCSS

### 2.2 后端技术栈
- **运行环境**: Node.js 18+
- **Web框架**: Express.js
- **数据库**: MySQL 8.0
- **ORM**: Sequelize
- **身份验证**: JWT + 微信授权
- **进程管理**: PM2
- **日志管理**: Winston
- **API文档**: Swagger

### 2.3 数据库设计
- **主数据库**: MySQL 8.0
- **连接池**: 最大连接数10（考虑服务器资源限制）
- **备份策略**: 每日自动备份

## 3. 系统架构

### 3.1 整体架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   微信小程序     │    │   H5前端应用     │    │   后台管理系统   │
│                │    │   (Vue+Vant)    │    │   (Vue+Element) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Nginx反向代理  │
                    │   (静态文件服务) │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Express API   │
                    │   (Node.js后端) │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   MySQL数据库   │
                    └─────────────────┘
```

### 3.2 端口分配（避免与邮箱服务冲突）
- **Nginx**: 8080 (HTTP), 8443 (HTTPS)
- **Express API**: 3001
- **MySQL**: 3306 (默认)
- **后台管理**: 通过Nginx代理，无需独立端口

### 3.3 目录结构
```
guanpinpin/
├── frontend/                 # H5前端应用
│   ├── src/
│   │   ├── components/      # 公共组件
│   │   ├── views/          # 页面组件
│   │   ├── router/         # 路由配置
│   │   ├── store/          # 状态管理
│   │   ├── utils/          # 工具函数
│   │   └── api/            # API接口
│   ├── public/
│   └── package.json
├── admin/                   # 后台管理系统
│   ├── src/
│   └── package.json
├── backend/                 # 后端API
│   ├── src/
│   │   ├── controllers/    # 控制器
│   │   ├── models/         # 数据模型
│   │   ├── routes/         # 路由
│   │   ├── middleware/     # 中间件
│   │   ├── utils/          # 工具函数
│   │   └── config/         # 配置文件
│   └── package.json
├── database/               # 数据库脚本
│   ├── migrations/         # 数据库迁移
│   └── seeds/             # 初始数据
├── deployment/             # 部署配置
│   ├── nginx.conf
│   ├── pm2.config.js
│   └── deploy.sh
└── docs/                   # 项目文档
```

## 4. 数据库设计

### 4.1 核心表结构

#### 用户信息表 (users)
```sql
CREATE TABLE users (
  id INT PRIMARY KEY AUTO_INCREMENT,
  openid VARCHAR(100) UNIQUE NOT NULL,
  unionid VARCHAR(100),
  nickname VARCHAR(100),
  phone VARCHAR(20),
  user_type TINYINT NOT NULL COMMENT '1:参展商 2:设计公司 3:工厂 4:综合商',
  is_deleted TINYINT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 展会类别表 (exhibition_categories)
```sql
CREATE TABLE exhibition_categories (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(50) NOT NULL,
  is_deleted TINYINT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 展馆信息表 (venues)
```sql
CREATE TABLE venues (
  id INT PRIMARY KEY AUTO_INCREMENT,
  city VARCHAR(50) NOT NULL,
  name VARCHAR(100) NOT NULL,
  address VARCHAR(200),
  is_deleted TINYINT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 展会信息表 (exhibitions)
```sql
CREATE TABLE exhibitions (
  id INT PRIMARY KEY AUTO_INCREMENT,
  venue_id INT NOT NULL,
  name VARCHAR(100) NOT NULL,
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  category_id INT NOT NULL,
  status TINYINT DEFAULT 0 COMMENT '0:未开始 1:进行中 9:已结束',
  is_deleted TINYINT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (venue_id) REFERENCES venues(id),
  FOREIGN KEY (category_id) REFERENCES exhibition_categories(id)
);
```

#### 工厂信息表 (factories)
```sql
CREATE TABLE factories (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  name VARCHAR(100) NOT NULL,
  contact_person VARCHAR(50),
  contact_phone VARCHAR(20),
  city VARCHAR(50),
  specialties TEXT COMMENT '特长：钢结构、木结构等',
  level TINYINT DEFAULT 1 COMMENT '级别1-5',
  is_deleted TINYINT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);
```

#### 设计公司表 (design_companies)
```sql
CREATE TABLE design_companies (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  name VARCHAR(100) NOT NULL,
  contact_person VARCHAR(50),
  contact_phone VARCHAR(20),
  city VARCHAR(50),
  level TINYINT DEFAULT 1 COMMENT '级别1-5',
  is_deleted TINYINT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);
```

#### 参展商表 (exhibitors)
```sql
CREATE TABLE exhibitors (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  name VARCHAR(100) NOT NULL,
  contact_person VARCHAR(50),
  contact_phone VARCHAR(20),
  exhibition_category_id INT,
  is_deleted TINYINT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (exhibition_category_id) REFERENCES exhibition_categories(id)
);
```

#### 参展信息表 (exhibition_participants)
```sql
CREATE TABLE exhibition_participants (
  id INT PRIMARY KEY AUTO_INCREMENT,
  exhibition_id INT NOT NULL,
  participant_id INT NOT NULL,
  participant_type TINYINT NOT NULL COMMENT '2:设计公司 3:工厂 4:综合商',
  is_full TINYINT DEFAULT 0 COMMENT '是否满额',
  is_deleted TINYINT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (exhibition_id) REFERENCES exhibitions(id)
);
```

#### 用户收藏表 (user_favorites)
```sql
CREATE TABLE user_favorites (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  favorite_type TINYINT NOT NULL COMMENT '2:设计公司 3:工厂 4:综合商',
  favorite_id INT NOT NULL,
  is_deleted TINYINT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);
```

## 5. 性能优化策略

### 5.1 资源优化（考虑服务器限制）
- **内存使用**: Node.js进程限制在512MB
- **数据库连接池**: 最大10个连接
- **静态资源**: 启用Gzip压缩
- **图片优化**: WebP格式，懒加载

### 5.2 缓存策略
- **静态资源**: Nginx缓存，7天过期
- **API响应**: 内存缓存热点数据（展会列表等）
- **数据库查询**: 合理使用索引

## 6. 安全策略

### 6.1 身份验证
- **微信授权**: 使用微信JS-SDK获取用户信息
- **JWT Token**: 有效期7天，自动刷新
- **API鉴权**: 所有API接口需要验证Token

### 6.2 数据安全
- **SQL注入防护**: 使用Sequelize ORM
- **XSS防护**: 输入验证和输出转义
- **HTTPS**: 生产环境强制使用HTTPS

## 7. 部署策略

### 7.1 部署流程
1. **代码构建**: 前端打包，后端依赖安装
2. **数据库迁移**: 自动执行数据库脚本
3. **服务启动**: PM2管理Node.js进程
4. **Nginx配置**: 反向代理和静态文件服务
5. **健康检查**: 自动重启机制

### 7.2 监控和日志
- **应用监控**: PM2监控进程状态
- **错误日志**: Winston记录错误信息
- **访问日志**: Nginx访问日志
- **自动重启**: 进程异常时自动重启

## 8. 开发规范

### 8.1 代码规范
- **ESLint**: JavaScript代码规范
- **Prettier**: 代码格式化
- **Git提交**: 规范化提交信息

### 8.2 API设计规范
- **RESTful**: 遵循REST API设计原则
- **统一响应格式**: 
```json
{
  "code": 200,
  "message": "success",
  "data": {}
}
```
- **错误处理**: 统一错误码和错误信息
