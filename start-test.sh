#!/bin/bash

echo "🚀 馆拼拼项目测试启动脚本"
echo "================================"

# 检查Node.js版本
echo "📋 检查环境..."
node_version=$(node -v 2>/dev/null)
if [ $? -eq 0 ]; then
    echo "✅ Node.js版本: $node_version"
else
    echo "❌ 请先安装Node.js (建议版本 >= 16.0.0)"
    exit 1
fi

# 检查MySQL
echo "📋 检查MySQL连接..."
mysql --version >/dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ MySQL已安装"
else
    echo "⚠️  请确保MySQL已安装并运行"
fi

echo ""
echo "🔧 开始安装依赖和初始化..."

# 安装后端依赖
echo "📦 安装后端依赖..."
cd backend
if [ ! -d "node_modules" ]; then
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 后端依赖安装失败"
        exit 1
    fi
fi
echo "✅ 后端依赖安装完成"

# 检查环境变量文件
if [ ! -f ".env" ]; then
    echo "⚠️  .env文件不存在，请配置数据库连接信息"
    echo "📝 请编辑 backend/.env 文件，设置正确的数据库密码"
    echo ""
    echo "示例配置："
    echo "DB_HOST=localhost"
    echo "DB_PORT=3306"
    echo "DB_NAME=guanpinpin"
    echo "DB_USER=root"
    echo "DB_PASSWORD=your_mysql_password"
    echo ""
    read -p "请按回车键继续（确保已配置.env文件）..."
fi

# 数据库初始化
echo "🗄️  初始化数据库..."
echo "正在创建数据表..."
npm run migrate
if [ $? -ne 0 ]; then
    echo "❌ 数据库迁移失败，请检查数据库连接配置"
    exit 1
fi

echo "正在插入测试数据..."
npm run seed
if [ $? -ne 0 ]; then
    echo "❌ 插入测试数据失败"
    exit 1
fi

echo "✅ 数据库初始化完成"

# 返回根目录
cd ..

# 安装后台管理依赖
echo "📦 安装后台管理依赖..."
cd admin
if [ ! -d "node_modules" ]; then
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 后台管理依赖安装失败"
        exit 1
    fi
fi
echo "✅ 后台管理依赖安装完成"

cd ..

echo ""
echo "🎉 初始化完成！"
echo "================================"
echo ""
echo "📋 测试信息："
echo "• 后端API地址: http://localhost:3001"
echo "• 后台管理地址: http://localhost:5174"
echo "• 管理员账号: admin"
echo "• 管理员密码: gpp13141234"
echo ""
echo "🚀 启动服务："
echo "1. 启动后端服务: cd backend && npm run dev"
echo "2. 启动后台管理: cd admin && npm run dev"
echo ""
echo "💡 提示: 建议在两个终端窗口中分别运行上述命令"
