-- 馆拼拼数据库初始化脚本
-- 创建数据库
CREATE DATABASE IF NOT EXISTS guanpinpin DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE guanpinpin;

-- 1. 用户信息表
CREATE TABLE users (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
  openid VARCHAR(100) UNIQUE NOT NULL COMMENT '微信openid',
  unionid VARCHAR(100) COMMENT '微信unionid',
  nickname VARCHAR(100) COMMENT '用户昵称',
  phone VARCHAR(20) COMMENT '手机号',
  user_type TINYINT NOT NULL COMMENT '用户类型：1参展商 2设计公司 3工厂 4综合商',
  is_deleted TINYINT DEFAULT 0 COMMENT '是否删除：0否 1是',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_users_openid (openid),
  INDEX idx_users_unionid (unionid),
  INDEX idx_users_user_type (user_type),
  INDEX idx_users_is_deleted (is_deleted)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户信息表';

-- 2. 展会类别表
CREATE TABLE exhibition_categories (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '类别ID',
  name VARCHAR(50) NOT NULL COMMENT '类别名称',
  is_deleted TINYINT DEFAULT 0 COMMENT '是否删除：0否 1是',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_categories_name (name),
  INDEX idx_categories_is_deleted (is_deleted)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='展会类别表';

-- 3. 展馆信息表
CREATE TABLE venues (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '展馆ID',
  city VARCHAR(50) NOT NULL COMMENT '所在城市',
  name VARCHAR(100) NOT NULL COMMENT '展馆名称',
  address VARCHAR(200) COMMENT '展馆地址',
  is_deleted TINYINT DEFAULT 0 COMMENT '是否删除：0否 1是',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_venues_city (city),
  INDEX idx_venues_name (name),
  INDEX idx_venues_is_deleted (is_deleted)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='展馆信息表';

-- 4. 展会信息表
CREATE TABLE exhibitions (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '展会ID',
  venue_id INT NOT NULL COMMENT '展馆ID',
  name VARCHAR(100) NOT NULL COMMENT '展会名称',
  start_date DATE NOT NULL COMMENT '开始日期',
  end_date DATE NOT NULL COMMENT '结束日期',
  category_id INT NOT NULL COMMENT '展会类别ID',
  status TINYINT DEFAULT 0 COMMENT '状态：0未开始 1进行中 9已结束',
  is_deleted TINYINT DEFAULT 0 COMMENT '是否删除：0否 1是',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_exhibitions_venue_id (venue_id),
  INDEX idx_exhibitions_category_id (category_id),
  INDEX idx_exhibitions_start_date (start_date),
  INDEX idx_exhibitions_end_date (end_date),
  INDEX idx_exhibitions_status (status),
  INDEX idx_exhibitions_is_deleted (is_deleted),
  INDEX idx_exhibitions_date_range (start_date, end_date),
  
  FOREIGN KEY fk_exhibitions_venue_id (venue_id) REFERENCES venues(id),
  FOREIGN KEY fk_exhibitions_category_id (category_id) REFERENCES exhibition_categories(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='展会信息表';

-- 5. 工厂信息表
CREATE TABLE factories (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '工厂ID',
  user_id INT NOT NULL COMMENT '用户ID',
  name VARCHAR(100) NOT NULL COMMENT '工厂名称',
  contact_person VARCHAR(50) COMMENT '联系人',
  contact_phone VARCHAR(20) COMMENT '联系电话',
  city VARCHAR(50) COMMENT '所在城市',
  specialties TEXT COMMENT '特长：钢结构、木结构等',
  level TINYINT DEFAULT 1 COMMENT '级别：1-5星',
  is_deleted TINYINT DEFAULT 0 COMMENT '是否删除：0否 1是',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_factories_user_id (user_id),
  INDEX idx_factories_city (city),
  INDEX idx_factories_level (level),
  INDEX idx_factories_is_deleted (is_deleted),
  
  FOREIGN KEY fk_factories_user_id (user_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工厂信息表';

-- 6. 设计公司表
CREATE TABLE design_companies (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '设计公司ID',
  user_id INT NOT NULL COMMENT '用户ID',
  name VARCHAR(100) NOT NULL COMMENT '公司名称',
  contact_person VARCHAR(50) COMMENT '联系人',
  contact_phone VARCHAR(20) COMMENT '联系电话',
  city VARCHAR(50) COMMENT '所在城市',
  level TINYINT DEFAULT 1 COMMENT '级别：1-5星',
  is_deleted TINYINT DEFAULT 0 COMMENT '是否删除：0否 1是',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_design_companies_user_id (user_id),
  INDEX idx_design_companies_city (city),
  INDEX idx_design_companies_level (level),
  INDEX idx_design_companies_is_deleted (is_deleted),
  
  FOREIGN KEY fk_design_companies_user_id (user_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='设计公司表';

-- 7. 参展商表
CREATE TABLE exhibitors (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '参展商ID',
  user_id INT NOT NULL COMMENT '用户ID',
  name VARCHAR(100) NOT NULL COMMENT '公司名称',
  contact_person VARCHAR(50) COMMENT '联系人',
  contact_phone VARCHAR(20) COMMENT '联系电话',
  exhibition_category_id INT COMMENT '参展类别ID',
  is_deleted TINYINT DEFAULT 0 COMMENT '是否删除：0否 1是',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_exhibitors_user_id (user_id),
  INDEX idx_exhibitors_category_id (exhibition_category_id),
  INDEX idx_exhibitors_is_deleted (is_deleted),
  
  FOREIGN KEY fk_exhibitors_user_id (user_id) REFERENCES users(id),
  FOREIGN KEY fk_exhibitors_category_id (exhibition_category_id) REFERENCES exhibition_categories(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='参展商表';

-- 8. 参展信息表
CREATE TABLE exhibition_participants (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '参展信息ID',
  exhibition_id INT NOT NULL COMMENT '展会ID',
  participant_id INT NOT NULL COMMENT '参展方ID（对应工厂或设计公司ID）',
  participant_type TINYINT NOT NULL COMMENT '参展方类型：2设计公司 3工厂 4综合商',
  is_full TINYINT DEFAULT 0 COMMENT '是否满额：0否 1是',
  is_deleted TINYINT DEFAULT 0 COMMENT '是否删除：0否 1是',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_participants_exhibition_id (exhibition_id),
  INDEX idx_participants_participant_id (participant_id),
  INDEX idx_participants_participant_type (participant_type),
  INDEX idx_participants_is_full (is_full),
  INDEX idx_participants_is_deleted (is_deleted),
  INDEX idx_participants_exhibition_type (exhibition_id, participant_type),
  
  FOREIGN KEY fk_participants_exhibition_id (exhibition_id) REFERENCES exhibitions(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='参展信息表';

-- 9. 用户收藏表
CREATE TABLE user_favorites (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '收藏ID',
  user_id INT NOT NULL COMMENT '用户ID',
  favorite_type TINYINT NOT NULL COMMENT '收藏类型：2设计公司 3工厂 4综合商',
  favorite_id INT NOT NULL COMMENT '收藏对象ID',
  is_deleted TINYINT DEFAULT 0 COMMENT '是否删除：0否 1是',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  INDEX idx_favorites_user_id (user_id),
  INDEX idx_favorites_type_id (favorite_type, favorite_id),
  INDEX idx_favorites_is_deleted (is_deleted),
  UNIQUE KEY uk_favorites_user_type_id (user_id, favorite_type, favorite_id),
  
  FOREIGN KEY fk_favorites_user_id (user_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户收藏表';

-- 10. 管理员表
CREATE TABLE admins (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '管理员ID',
  username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
  password VARCHAR(255) NOT NULL COMMENT '密码（加密）',
  is_deleted TINYINT DEFAULT 0 COMMENT '是否删除：0否 1是',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_admins_username (username),
  INDEX idx_admins_is_deleted (is_deleted)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员表';
