-- 给展会信息表添加城市字段
-- 执行时间：2024-12-19

-- 1. 添加城市字段
ALTER TABLE exhibitions 
ADD COLUMN city VARCHAR(50) COMMENT '城市' AFTER venue_id;

-- 2. 根据展馆信息更新展会的城市字段
UPDATE exhibitions e 
INNER JOIN venues v ON e.venue_id = v.id 
SET e.city = v.city 
WHERE e.is_deleted = 0;

-- 3. 添加索引
ALTER TABLE exhibitions 
ADD INDEX idx_exhibitions_city (city);

-- 4. 验证数据
SELECT 
    e.id,
    e.name,
    e.city,
    v.city as venue_city,
    v.name as venue_name
FROM exhibitions e
INNER JOIN venues v ON e.venue_id = v.id
WHERE e.is_deleted = 0
LIMIT 10;
