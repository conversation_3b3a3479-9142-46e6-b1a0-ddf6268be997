-- 馆拼拼初始数据脚本
USE guanpinpin;

-- 插入展会类别数据
INSERT INTO exhibition_categories (name) VALUES
('服装展'),
('水展'),
('汽车展'),
('科技展'),
('箱包展'),
('家具展'),
('建材展'),
('食品展'),
('医疗展'),
('教育展');

-- 插入展馆数据
INSERT INTO venues (city, name, address) VALUES
('上海', '新国际博览中心', '上海市浦东新区龙阳路2345号'),
('上海', '国家会展中心', '上海市青浦区崧泽大道333号'),
('北京', '中国国际展览中心', '北京市朝阳区北三环东路6号'),
('深圳', '深圳会展中心', '深圳市福田区福华三路111号'),
('广州', '中国进出口商品交易会展馆', '广州市海珠区阅江中路380号'),
('杭州', '杭州国际博览中心', '杭州市萧山区奔竞大道353号'),
('成都', '中国西部国际博览城', '成都市天府新区福州路东段88号'),
('武汉', '武汉国际博览中心', '武汉市汉阳区鹦鹉大道619号');

-- 插入示例展会数据
INSERT INTO exhibitions (venue_id, name, start_date, end_date, category_id, status) VALUES
(1, '第4届上海网络安全博览会', '2025-06-05', '2025-06-07', 4, 0),
(1, 'GPOWER2025动力展', '2025-06-11', '2025-06-13', 3, 0),
(1, '2025润滑油聚焦产业周论坛推介会', '2025-06-02', '2025-06-03', 2, 0),
(2, '上海世环会 节能环保低碳舒适系统展', '2025-06-04', '2025-06-06', 2, 0),
(1, '2025设计上海展', '2025-06-04', '2025-06-07', 6, 0),
(1, '2025中国国际模具技术和设备展览会', '2025-06-04', '2025-06-07', 4, 0),
(2, 'WieTec 2025世环会 第七届上海国际水展', '2025-06-04', '2025-06-06', 2, 0),
(3, '2025北京国际汽车展览会', '2025-04-25', '2025-05-04', 3, 0),
(4, '2025深圳国际服装供应链博览会', '2025-07-10', '2025-07-12', 1, 0),
(5, '2025广州国际家具博览会', '2025-03-18', '2025-03-21', 6, 0);

-- 插入管理员数据（密码需要在应用中用bcrypt加密）
INSERT INTO admins (username, password) VALUES
('admin', 'gpp13141234'); -- 实际部署时需要用bcrypt加密

-- 插入测试用户数据（用于开发测试）
INSERT INTO users (openid, unionid, nickname, phone, user_type) VALUES
('test_openid_1', 'test_unionid_1', '测试参展商', '13800138001', 1),
('test_openid_2', 'test_unionid_2', '测试设计公司', '13800138002', 2),
('test_openid_3', 'test_unionid_3', '测试工厂', '13800138003', 3),
('test_openid_4', 'test_unionid_4', '测试综合商', '13800138004', 4);

-- 插入测试参展商数据
INSERT INTO exhibitors (user_id, name, contact_person, contact_phone, exhibition_category_id) VALUES
(1, '上海XX贸易有限公司', '张经理', '13800138001', 1);

-- 插入测试设计公司数据
INSERT INTO design_companies (user_id, name, contact_person, contact_phone, city, level) VALUES
(2, '上海YY设计有限公司', '李经理', '13800138002', '上海', 4),
(4, '北京ZZ创意设计公司', '王经理', '13800138004', '北京', 3);

-- 插入测试工厂数据
INSERT INTO factories (user_id, name, contact_person, contact_phone, city, specialties, level) VALUES
(3, '深圳AA搭建工厂', '赵经理', '13800138003', '深圳', '钢结构,木结构,特装搭建', 5),
(4, '广州BB展览工厂', '钱经理', '13800138004', '广州', '标准展位,特装展台', 4);

-- 插入测试参展信息数据
INSERT INTO exhibition_participants (exhibition_id, participant_id, participant_type, is_full) VALUES
(1, 1, 2, 0), -- 设计公司参加网络安全博览会
(1, 1, 3, 0), -- 工厂参加网络安全博览会
(2, 1, 2, 0), -- 设计公司参加动力展
(2, 1, 3, 1), -- 工厂参加动力展（已满额）
(3, 1, 2, 0), -- 设计公司参加润滑油展
(4, 1, 3, 0), -- 工厂参加世环会
(5, 2, 2, 0), -- 综合商（设计公司身份）参加设计展
(5, 2, 3, 0); -- 综合商（工厂身份）参加设计展
