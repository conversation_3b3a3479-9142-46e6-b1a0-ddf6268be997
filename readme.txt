在现在的项目目录下创建一个馆拼拼项目。
项目介绍：
1.这是个微信小程序项目，使用 H5（Vue+Vant） + Node.js（后端）开发
2. 项目主要逻辑：中国在各地经常举办各种展会，参展商需要在展会期间在展馆搭建展台。
过程是 参展商找设计公司设计，设计公司再找工厂搭建。工厂如果在某个展会期间只有一个台子需要搭建，那他的成本（比如交通、运输、人力等）就会高，如果能同时在一个展会有多个展台由他搭建，那他随后的成本就会降低很多。
所以底层逻辑就是工厂希望能在一个展会期间多找几家设计公司合作。设计公司经常合作的工厂有时也不会参展，所以设计公司偶尔也需要找工厂。有些参展公司也可以根据展会信息找设计公司，或者他有设计方案了，也可以直接找工厂搭建。
所以有了这个微信小程序：馆拼拼。用户通过微信授权登录，首次登录需要选择用户类别（参展商、设计公司、工厂、综合商（有设计能力的工厂））然后根据选择的类别填写公司基本信息，落库存储，后续登录就不需要再选择和填写了，设置界面应该允许他修改身份。
展会类别分为：服装展、水展、汽车展、科技展、箱包展等等。用一个展会类别表来维护这个枚举值吧,id、类别名称
工厂信息表：id、名称、联系人、联系方式、所在城市、展台类别（有这个吗？比如工厂是不是只做水展服装展）、特长（钢结构、木结构什么的）、级别（内部信息，用于搜索排序或者信息展示）。
展馆信息表：id、城市、展馆名称、展馆地址、是否删除。
展会信息表: id、展馆 id、展会名称、开始日期、结束日期、展会类别、状态（0未开始、1 开展中、9 已结束）、是否删除。
参展信息表，id、展会 id、参展方 id、参展方类别（即参展方的用户类别）、是否满额（可切换的状态、接满了，不想再接单了）、是否删除（参展方不参加了，不被搜索出来）。
一张设计公司表， id、公司名、联系人、联系方式、所在城市、级别（内部信息，用于搜索排序或者信息展示）、是否删除。
一张参展商表，id、公司名、联系人、联系方式、参展类别（即展会类别）、是否删除。
一张用户信息表，管理小程序登录的用户信息，id、小程序 id(是这个吧，也可以改为别的小程序相关的 id)、用户类别（1 参展商 2 设计公司 3 工厂 4 综合商（有设计能力的工厂，设计公司和工厂都要落一条数据））、是否删除。
一张用户收藏表，id、用户 id、收藏类型（1 工厂 2 设计公司 3 综合商）、收藏 id、是否删除。
以上表格需要有增删改查的接口，需要在后管界面可以维护
3.小程序的功能：
初次登录：选择用户类别，填写公司基本信息，落库存储，后续登录就不需要再选择和填写了，设置界面应该允许他修改身份。
小程序底部是 4 个 tab，分别是：首页、找设计、找工厂、我的
首页分 3 部分，最上分是展示 banner，展示 馆拼拼 logo，上方是搜索框，下方是近期展会。搜索框三个选项：城市、日期、展馆、找什么（找设计、找工厂），然后是开始搜索 按钮，点击后，根据搜索条件，跳转到找工厂或者找设计界面，展示结果列表
找设计：title 为 找设计，下方有一行搜索框，第一个是下拉列表选择城市，第二个是日期选择，第三个是 关键字输入（可用来过滤，前端用来筛选结果中的关键字），第四个是切换展馆（点击可以切换查询的展馆），结果列表为设计公司列表，点击后跳转设计公司信息详情页面，展示：设计公司名称、联系人、联系方式、所在城市、级别、操作（打电话、发消息）,还有收藏按钮，收藏后，在 我的 的 我的收藏 中展示
找工厂：title 为 找工厂，下方有一行搜索框，第一个是下拉列表选择城市，第二个是日期选择，第三个是 关键字输入（可用来过滤，前端用来筛选结果中的关键字），第四个是切换展馆（点击可以切换查询的展馆），结果列表为工厂列表，点击后跳转工厂信息详情页面，展示：工厂名称、联系人、联系方式、所在城市、展台类别、特长、级别、操作（打电话、发消息）,还有收藏按钮，收藏后，在 我的 的 我的收藏 中展示
搜索逻辑：在展会信息表中查找用户选择的日期在开始和结束之间的，地点等于展馆、状态非9、是否删除为 0 的一条数据，找到他的 id作为展会 id。再在参展信息表中找到同一个展会 id的， 结合用户选择找的类型（参展方类型），中找到参展方 id 列表，然后展示列表。
我的：title 为 我的，下方有 我的信息、收藏、反馈、关于、退出登录。我的信息：展示用户信息，可以修改。收藏：展示用户收藏的工厂和设计公司，点击后跳转工厂信息详情页面，展示：工厂名称、联系人、联系方式、所在城市、展台类别、特长、级别、操作（打电话、发消息）。反馈：展示用户反馈，点击后跳转反馈页面，展示：反馈内容。关于：展示用户关于，点击后跳转关于页面，展示：关于内容。
4.后端接口：待开发
5.数据库：待设计，待开发
6.小程序的UI：

1.前端技术栈：使用 H5 网页版吧，这样简单一些吧。最后
2.后端技术栈：使用Express
3.数据库：我的云服务是
Debian 12.0.0 64bit
2核 | 2GiB | 系统盘 40GiB，你自己看选择什么数据库合适。
4.用户身份切换：用户如果是综合商，且参展了该次展会，那两个列表都会出现
5. 参展信息表：是的，用来登记某次展会哪些工厂或者设计公司参展，搜索时也只会搜索这些参与这次展会的。
6.搜索逻辑：在展会信息表中查找用户选择的日期在开始和结束之间的，地点等于展馆、状态非9、是否删除为 0 的一条数据，找到他的 id作为展会 id。再在参展信息表中找到同一个展会 id的， 结合用户选择找的类型（参展方类型），中找到参展方 id 列表，然后展示列表。
7.微信授权登录：手机号和昵称即可
8.消息功能：先去掉发消息功能
9.后台管理：独立的后台管理界面吧，就先使用固定的用户名和密码：admin, 密码 gpp13141234
10.后管界面和 h5 界面 需要能直接部署到我的云服务上，然后使用微信小程序代码使用嵌入 h5 页面的方式。

你看看还有没有什么问题？


1.微信小程序嵌入H5：AppID(小程序ID)
wxaa87bd18d1cf1d1e
3.展会状态：定时任务最好，每天凌晨跑一次。但是我服务器资源很紧张，而且已经在跑邮箱服务器了。最好省着点，不行就后管页面维护吧。
4.用户表设计，openid和 unionid 都存吧
5.级别字段，就 1-5 的数字类型吧，展示时就用🌟工厂，🌟🌟🌟工厂这种
6.另外部署的话，我云服务器已经在跑邮箱服务器了，用了不少端口，这个部署不能占用已有端口，不能弄坏邮箱服务器，不能导致 oom，导致 oom 后不能自动重新启动。
7.开发顺序的话，你需要先完成 技术架构设计、流程图设计、ui 设计，然后再开始按你的四个阶段开始实施

