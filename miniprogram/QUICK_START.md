# 快速启动指南

## 🚀 立即开始测试

### 1. 启动小程序
1. 在微信开发者工具中打开项目
2. 确保云开发环境ID为：`cloud1-3g3fj8sde212780f`
3. 点击"编译"按钮

### 2. 测试基础功能
1. **用户登录**
   - 点击"微信授权登录"按钮
   - 应该显示登录成功

2. **查看个人信息**
   - 点击"个人信息"按钮
   - 可以编辑用户信息

3. **查看展会列表**
   - 点击"展会列表"按钮
   - 可以看到展会列表页面（目前数据为空，需要先部署云函数）

## 🔧 部署云函数（必需）

### 快速部署所有云函数：

1. **initBaseData** - 右键 → 上传并部署：云端安装依赖
2. **getExhibitions** - 右键 → 上传并部署：云端安装依赖
3. **manageExhibitionParticipation** - 右键 → 上传并部署：云端安装依赖
4. **searchCompanies** - 右键 → 上传并部署：云端安装依赖
5. **manageFavorites** - 右键 → 上传并部署：云端安装依赖
6. **manageCompany** - 右键 → 上传并部署：云端安装依赖
7. **getBaseData** - 右键 → 上传并部署：云端安装依赖

### 等待部署完成后：
- 重新编译小程序
- 测试展会列表功能

## 📊 创建数据库集合（必需）

在云开发控制台创建以下集合：

### 1. exhibition_categories
```json
权限规则：{"read": true, "write": false}
```

### 2. venues  
```json
权限规则：{"read": true, "write": false}
```

### 3. exhibitions
```json
权限规则：{"read": true, "write": false}
```

### 4. exhibition_participants
```json
权限规则：{"read": "doc._openid == auth.openid", "write": "doc._openid == auth.openid"}
```

### 5. factories
```json
权限规则：{"read": true, "write": "doc._openid == auth.openid"}
```

### 6. design_companies
```json
权限规则：{"read": true, "write": "doc._openid == auth.openid"}
```

### 7. user_favorites
```json
权限规则：{"read": "doc._openid == auth.openid", "write": "doc._openid == auth.openid"}
```

## ✅ 验证功能

### 部署完成后测试：
1. **基础数据初始化** - 第一次打开小程序会自动初始化
2. **展会列表** - 应该能看到示例展会数据
3. **参加展会** - 可以参加和退出展会
4. **搜索功能** - 可以搜索展会

## ⚠️ 常见问题

### 1. 组件找不到错误
- 已使用原生组件，无需安装Vant
- 如果仍有错误，检查页面的json配置

### 2. 云函数调用失败
- 确保云函数已正确部署
- 检查云开发环境ID是否正确

### 3. 数据库权限错误
- 确保集合权限规则设置正确
- 检查用户是否已登录

### 4. 数据为空
- 确保initBaseData云函数已部署
- 重新编译小程序触发初始化

## 🎯 成功标志

当以下功能都正常工作时，说明迁移成功：
- ✅ 用户登录
- ✅ 展会列表显示
- ✅ 参加/退出展会
- ✅ 个人信息管理

## 📞 需要帮助？

如果遇到问题：
1. 查看控制台错误信息
2. 检查云函数日志
3. 确认数据库集合和权限设置
4. 参考详细的 `MIGRATION_TEST_GUIDE.md`
