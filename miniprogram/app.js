// app.js
App({
  onLaunch() {
    // 初始化云开发
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力')
    } else {
      wx.cloud.init({
        env: 'cloud1-3g3fj8sde212780f',
        traceUser: true,
      })

      // 延迟初始化基础数据，避免阻塞启动
      setTimeout(() => {
        this.initBaseData()
        this.checkAndAutoLogin()
      }, 1000)
    }
  },

  // 初始化基础数据
  async initBaseData() {
    try {
      // 检查是否已经初始化过基础数据
      const hasInitialized = wx.getStorageSync('baseDataInitialized')
      if (hasInitialized) {
        return
      }

      console.log('开始初始化基础数据...')

      // 调用初始化云函数
      const result = await wx.cloud.callFunction({
        name: 'initBaseData'
      })

      if (result.result.success) {
        // 标记已初始化
        wx.setStorageSync('baseDataInitialized', true)
        console.log('基础数据初始化完成')
      }
    } catch (error) {
      console.error('初始化基础数据失败:', error)
    }
  },

  // 检查并自动登录
  async checkAndAutoLogin() {
    try {
      // 如果已经有登录标识，不需要重新登录
      if (wx.getStorageSync('isLoggedIn')) {
        console.log('用户已登录，跳过自动登录')
        return
      }

      console.log('首次启动，尝试自动登录...')

      // 调用登录云函数
      const result = await wx.cloud.callFunction({
        name: 'login',
        data: {}
      })

      if (result.result.success) {
        // 设置登录状态
        wx.setStorageSync('isLoggedIn', true)
        wx.setStorageSync('userInfo', result.result.data.userInfo)
        console.log('自动登录成功')

        // 检查是否需要选择身份（新用户或未设置身份的用户）
        if (result.result.data.needSelectUserType) {
          console.log('新用户需要选择身份')
          // 可以在这里设置一个全局标识，在首页显示身份选择提示
          wx.setStorageSync('needSelectUserType', true)
        }
      } else {
        console.log('自动登录失败:', result.result.message)
      }
    } catch (error) {
      console.error('自动登录出错:', error)
    }
  },

  globalData: {
    userInfo: null,
    searchParams: null
  }
})
