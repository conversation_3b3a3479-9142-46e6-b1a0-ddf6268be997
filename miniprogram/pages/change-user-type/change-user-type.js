// pages/change-user-type/change-user-type.js
import {
  changeUserType,
  showError,
  showSuccess,
  showLoading,
  hideLoading
} from '../../utils/api.js'

Page({
  data: {
    currentUserType: null,
    selectedType: '',
    loading: false,
    userTypeOptions: [
      {
        value: 1,
        title: '参展商',
        description: '我需要参加展会，寻找设计公司和工厂合作',
        icon: '🏢'
      },
      {
        value: 2,
        title: '设计公司',
        description: '我是设计公司，为参展商提供展台设计服务',
        icon: '🎨'
      },
      {
        value: 3,
        title: '工厂',
        description: '我是搭建工厂，为参展商提供展台搭建服务',
        icon: '🏭'
      },
      {
        value: 4,
        title: '综合商',
        description: '我既提供设计服务，也提供搭建服务',
        icon: '🔧'
      }
    ]
  },



  onLoad() {
    this.loadCurrentUserType()
  },

  // 加载当前用户身份
  loadCurrentUserType() {
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo && userInfo.userType) {
      this.setData({
        currentUserType: userInfo.userType
      })
    } else {
      showError('获取用户信息失败')
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  // 返回
  goBack() {
    wx.navigateBack()
  },

  // 选择类型
  selectType(e) {
    const { type } = e.currentTarget.dataset
    
    // 不能选择当前身份
    if (type === this.data.currentUserType) {
      return
    }
    
    this.setData({
      selectedType: type
    })
  },

  // 确认修改
  async handleConfirm() {
    if (!this.data.selectedType || this.data.selectedType === this.data.currentUserType) {
      showError('请选择新的身份')
      return
    }

    // 如果切换到需要公司信息的身份，先跳转到完善信息页面
    if (this.data.selectedType !== 1) {
      wx.showModal({
        title: '需要完善信息',
        content: '切换到此身份需要完善公司信息，是否继续？',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: `/pages/complete-profile/complete-profile?userType=${this.data.selectedType}&isChange=true`
            })
          }
        }
      })
      return
    }

    // 切换到参展商，直接修改
    try {
      this.setData({ loading: true })
      showLoading('修改中...')

      const result = await changeUserType(this.data.selectedType)

      if (result.success) {
        // 更新本地用户信息
        wx.setStorageSync('userInfo', result.data.userInfo)
        
        showSuccess('身份修改成功')

        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      } else {
        showError(result.message || '修改失败')
      }
    } catch (error) {
      console.error('修改身份失败:', error)
      showError('修改失败，请重试')
    } finally {
      this.setData({ loading: false })
      hideLoading()
    }
  }
})
