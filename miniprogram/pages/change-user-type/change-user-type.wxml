<!--pages/change-user-type/change-user-type.wxml-->
<view class="page">
  <!-- 导航栏 -->
  <view class="nav-bar">
    <view class="nav-back" bind:tap="goBack">‹</view>
    <view class="nav-title">修改身份</view>
    <view class="nav-placeholder"></view>
  </view>

  <view class="content">
    <!-- 选择新身份 -->
    <view class="select-section">
      <view class="section-title">选择身份</view>
      <view class="type-options">
        <view
          wx:for="{{userTypeOptions}}"
          wx:key="value"
          class="type-option {{selectedType === item.value ? 'active' : ''}} {{currentUserType === item.value ? 'disabled' : ''}}"
          bind:tap="selectType"
          data-type="{{item.value}}"
        >
          <view class="option-icon">{{item.icon}}</view>
          <view class="option-content">
            <view class="option-title">{{item.title}}</view>
            <view class="option-desc">{{item.description}}</view>
          </view>
          <view class="option-check">
            <text class="check-icon">
              {{currentUserType === item.value ? '当前' : (selectedType === item.value ? '✓' : '○')}}
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 确认按钮 -->
    <view class="action-section">
      <button
        class="confirm-btn"
        disabled="{{!selectedType || selectedType === currentUserType}}"
        loading="{{loading}}"
        bind:tap="handleConfirm"
      >
        确认修改
      </button>
    </view>
  </view>
</view>
