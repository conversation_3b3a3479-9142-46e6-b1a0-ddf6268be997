/* pages/change-user-type/change-user-type.wxss */
.page {
  min-height: 100vh;
  background: #f5f5f5;
}

.nav-bar {
  background: white;
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid #eee;
}

.nav-back {
  font-size: 40rpx;
  color: #1989fa;
  width: 60rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.nav-placeholder {
  width: 60rpx;
}

.content {
  padding: 30rpx;
}

.select-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.type-options {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
}

.type-option {
  padding: 30rpx;
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.type-option:last-child {
  border-bottom: none;
}

.type-option.active {
  background: #f0f8ff;
}

.type-option.disabled {
  opacity: 0.5;
  background: #f8f8f8;
}

.option-icon {
  font-size: 50rpx;
  margin-right: 30rpx;
  width: 70rpx;
  text-align: center;
}

.option-content {
  flex: 1;
}

.option-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.option-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.option-check {
  margin-left: 20rpx;
}

.check-icon {
  font-size: 28rpx;
  color: #1989fa;
  font-weight: bold;
}

.type-option:not(.active) .check-icon {
  color: #ddd;
}

.type-option.disabled .check-icon {
  color: #999;
  font-size: 24rpx;
}

.action-section {
  padding: 20rpx 0;
}

.confirm-btn {
  background: linear-gradient(135deg, #1989fa 0%, #0066cc 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 30rpx;
  font-size: 32rpx;
  font-weight: bold;
  width: 100%;
}

.confirm-btn[disabled] {
  background: #ccc;
}

.confirm-btn[loading] {
  background: #999;
}
