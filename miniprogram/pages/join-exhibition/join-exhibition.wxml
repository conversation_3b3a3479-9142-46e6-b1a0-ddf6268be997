<!--pages/join-exhibition/join-exhibition.wxml-->
<view class="page">
  <view class="content">
    <view
      wx:for="{{exhibitions}}"
      wx:key="id"
      class="exhibition-card"
    >
      <view class="exhibition-header" bind:tap="goToExhibitionDetail" data-id="{{item.id}}">
        <view class="exhibition-info">
          <view class="exhibition-name">{{item.name}}</view>
          <view class="exhibition-venue">{{item.venue ? item.venue.name : ''}}</view>
        </view>
      </view>

      <view class="exhibition-details" bind:tap="goToExhibitionDetail" data-id="{{item.id}}">
        <view class="detail-item">
          <text class="icon">📍</text>
          <text>{{item.city}}</text>
        </view>
        <view class="detail-item">
          <text class="icon">📅</text>
          <text>{{item.startDate}} - {{item.endDate}}</text>
        </view>
        <view class="detail-item" wx:if="{{item.category}}">
          <text class="icon">🏷️</text>
          <text>{{item.category.name}}</text>
        </view>
        <view class="detail-item" wx:if="{{item.description}}">
          <text class="icon">📝</text>
          <text>{{item.description}}</text>
        </view>
      </view>

      <view class="exhibition-actions">
        <button
          class="join-btn"
          bind:tap="joinExhibition"
          data-id="{{item.id}}"
          data-name="{{item.name}}"
          disabled="{{item.hasJoined}}"
        >
          {{item.hasJoined ? '已报名' : '立即报名'}}
        </button>
      </view>
    </view>

    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading">
      <text>加载中...</text>
    </view>

    <!-- 空状态 -->
    <view wx:if="{{!loading && exhibitions.length === 0}}" class="empty-state">
      <text class="empty-icon">🏢</text>
      <view class="empty-text">暂无可参加的展会</view>
      <view class="empty-desc">请稍后再来看看</view>
    </view>

    <!-- 没有更多 -->
    <view wx:if="{{finished && exhibitions.length > 0}}" class="no-more">
      <text>没有更多了</text>
    </view>
  </view>
</view>