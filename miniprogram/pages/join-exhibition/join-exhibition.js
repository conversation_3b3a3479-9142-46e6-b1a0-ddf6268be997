// pages/join-exhibition/join-exhibition.js
import {
  getAvailableExhibitions,
  joinExhibition,
  showError,
  showSuccess,
  showLoading,
  hideLoading
} from '../../utils/api.js'

Page({
  data: {
    exhibitions: [],
    loading: false,
    finished: false,
    refreshing: false,
    page: 1,
    pageSize: 10,
    filters: {
      city: '',
      category: '',
      status: 0 // 0-即将开始，1-进行中
    }
  },

  onLoad() {
    this.loadExhibitions(true)
  },

  onShow() {
    this.loadExhibitions(true)
  },

  onPullDownRefresh() {
    this.onRefresh()
  },

  onReachBottom() {
    this.loadMore()
  },

  // 加载展会列表
  async loadExhibitions(isRefresh = false) {
    if (this.data.loading) return

    try {
      if (isRefresh) {
        this.setData({
          page: 1,
          finished: false,
          loading: true,
          refreshing: true
        })
      } else {
        this.setData({ loading: true })
      }

      const params = {
        page: isRefresh ? 1 : this.data.page,
        pageSize: this.data.pageSize,
        ...this.data.filters
      }

      const result = await getAvailableExhibitions(params)

      if (result.success) {
        const { list, totalPages } = result.data

        // 为每个展会添加默认的hasJoined状态
        const exhibitionsWithStatus = list.map(exhibition => ({
          ...exhibition,
          hasJoined: false // 默认未报名，后续可以通过API检查
        }))

        if (isRefresh) {
          this.setData({
            exhibitions: exhibitionsWithStatus
          })
        } else {
          this.setData({
            exhibitions: [...this.data.exhibitions, ...exhibitionsWithStatus]
          })
        }

        if (params.page >= totalPages) {
          this.setData({ finished: true })
        } else {
          this.setData({
            page: this.data.page + 1
          })
        }
      } else {
        showError(result.message || '获取数据失败')
      }
    } catch (error) {
      console.error('获取展会列表失败:', error)
      showError(error)
    } finally {
      this.setData({
        loading: false,
        refreshing: false
      })
      wx.stopPullDownRefresh()
    }
  },

  // 下拉刷新
  async onRefresh() {
    await this.loadExhibitions(true)
  },

  // 上拉加载更多
  async loadMore() {
    if (this.data.finished || this.data.loading) return
    await this.loadExhibitions()
  },

  // 参加展会
  async joinExhibition(e) {
    const { id, name } = e.currentTarget.dataset

    try {
      const result = await wx.showModal({
        title: '确认参展',
        content: `确定要参加"${name}"展会吗？`,
        confirmText: '确定参加',
        confirmColor: '#07c160'
      })

      if (result.confirm) {
        showLoading('报名中...')

        const joinResult = await joinExhibition(id)

        if (joinResult.success) {
          showSuccess('报名成功')
          // 刷新列表
          this.loadExhibitions(true)
        } else {
          showError(joinResult.message || '报名失败')
        }
      }
    } catch (error) {
      console.error('参加展会失败:', error)
      showError(error)
    } finally {
      hideLoading()
    }
  },

  // 跳转到展会详情
  goToExhibitionDetail(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/exhibition-detail/exhibition-detail?id=${id}`
    })
  }
})