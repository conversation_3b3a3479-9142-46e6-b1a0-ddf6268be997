<view class="container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 公司详情 -->
  <view wx:elif="{{companyInfo}}" class="company-detail">
    <!-- 公司头部信息 -->
    <view class="company-header">
      <view class="company-basic">
        <view class="company-name">{{companyInfo.name}}</view>
        <view class="company-type">{{companyTypeName}}</view>
      </view>
      <view class="company-actions">
        <text
          class="favorite-icon {{isFavorited ? 'favorited' : ''}}"
          bind:tap="toggleFavorite"
        >
          {{isFavorited ? '⭐' : '☆'}}
        </text>
      </view>
    </view>

    <!-- 公司详细信息 -->
    <view class="company-info">
      <!-- 基本信息 -->
      <view class="info-section">
        <view class="section-title">基本信息</view>

        <view class="info-item" wx:if="{{companyInfo.city}}">
          <text class="info-label">所在城市</text>
          <text class="info-value">{{companyInfo.city}}</text>
        </view>

        <view class="info-item" wx:if="{{companyInfo.level}}">
          <text class="info-label">{{type === 'factory' ? '工厂等级' : '设计等级'}}</text>
          <view class="info-value">
            <text class="stars">
              <text wx:for="{{companyInfo.level}}" wx:key="*this">★</text>
            </text>
            <text class="level-text">{{companyInfo.level}}星</text>
          </view>
        </view>

        <view class="info-item" wx:if="{{companyInfo.type}}">
          <text class="info-label">公司类型</text>
          <text class="info-value">{{companyInfo.type === 'comprehensive' ? '综合商' : (type === 'factory' ? '工厂' : '设计公司')}}</text>
        </view>
      </view>

      <!-- 联系信息 -->
      <view class="info-section" wx:if="{{companyInfo.contactPerson || companyInfo.contactPhone}}">
        <view class="section-title">联系信息</view>

        <view class="info-item" wx:if="{{companyInfo.contactPerson}}">
          <text class="info-label">联系人</text>
          <text class="info-value">{{companyInfo.contactPerson}}</text>
        </view>

        <view class="info-item" wx:if="{{companyInfo.contactPhone}}">
          <text class="info-label">联系电话</text>
          <text class="info-value phone" bind:tap="makeCall">{{companyInfo.contactPhone}}</text>
        </view>
      </view>


    </view>

    <!-- 底部操作按钮 -->
    <view class="bottom-actions">
      <button
        class="contact-btn"
        bind:tap="makeCall"
        disabled="{{!companyInfo.contactPhone}}"
      >
        <text class="btn-icon">📞</text>
        <text class="btn-text">联系{{type === 'factory' ? '工厂' : '设计公司'}}</text>
      </button>
    </view>
  </view>

  <!-- 错误状态 -->
  <view wx:else class="error-container">
    <text class="error-text">加载失败，请重试</text>
    <button class="retry-btn" bind:tap="fetchCompanyDetail">重试</button>
  </view>
</view>