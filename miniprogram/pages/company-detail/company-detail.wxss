/* pages/company-detail/company-detail.wxss */
.container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 120rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

/* 公司详情 */
.company-detail {
  padding: 20rpx;
}

/* 公司头部 */
.company-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  background: white;
  padding: 30rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.company-basic {
  flex: 1;
}

.company-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  line-height: 1.4;
}

.company-type {
  font-size: 24rpx;
  color: #666;
  background: #f0f0f0;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  display: inline-block;
}

.company-actions {
  margin-left: 20rpx;
}

.favorite-icon {
  font-size: 48rpx;
  color: #ddd;
  transition: color 0.3s;
}

.favorite-icon.favorited {
  color: #ffd700;
}

/* 公司信息 */
.company-info {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.info-section {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-section:last-child {
  border-bottom: none;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  min-width: 160rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  text-align: right;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.info-value.phone {
  color: #07c160;
  text-decoration: underline;
}

.stars {
  color: #ffd700;
  margin-right: 10rpx;
}

.level-text {
  color: #333;
}

/* 底部操作 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #eee;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.contact-btn {
  width: 100%;
  height: 88rpx;
  background: #07c160;
  color: white;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.contact-btn:disabled {
  background: #ccc;
  color: #999;
}

.contact-btn:not(:disabled):active {
  background: #06ad56;
}

.btn-icon {
  margin-right: 10rpx;
  font-size: 28rpx;
}

.btn-text {
  font-size: 32rpx;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 400rpx;
  padding: 40rpx;
}

.error-text {
  color: #999;
  font-size: 28rpx;
  margin-bottom: 30rpx;
}

.retry-btn {
  background: #07c160;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

/* 公司详情 */
.company-detail {
  padding: 20rpx;
}

/* 公司头部 */
.company-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  background: white;
  padding: 30rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.company-basic {
  flex: 1;
}

.company-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  line-height: 1.4;
}

.company-type {
  font-size: 24rpx;
  color: #666;
  background: #f0f0f0;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  display: inline-block;
}

.company-actions {
  margin-left: 20rpx;
}

.favorite-icon {
  font-size: 48rpx;
  color: #ddd;
  transition: color 0.3s;
}

.favorite-icon.favorited {
  color: #ffd700;
}

/* 公司信息 */
.company-info {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.info-section {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-section:last-child {
  border-bottom: none;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  min-width: 160rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  text-align: right;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.info-value.phone {
  color: #07c160;
  text-decoration: underline;
}

.stars {
  color: #ffd700;
  margin-right: 10rpx;
}

.level-text {
  color: #333;
}

/* 底部操作 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #eee;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.contact-btn {
  width: 100%;
  height: 88rpx;
  background: #07c160;
  color: white;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.contact-btn:disabled {
  background: #ccc;
  color: #999;
}

.contact-btn:not(:disabled):active {
  background: #06ad56;
}

.btn-icon {
  margin-right: 10rpx;
  font-size: 28rpx;
}

.btn-text {
  font-size: 32rpx;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 400rpx;
  padding: 40rpx;
}

.error-text {
  color: #999;
  font-size: 28rpx;
  margin-bottom: 30rpx;
}

.retry-btn {
  background: #07c160;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}