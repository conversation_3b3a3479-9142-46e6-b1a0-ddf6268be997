// pages/company-detail/company-detail.js
import {
  getCompanyDetail,
  addFavorite,
  removeFavorite,
  checkFavoriteStatus,
  showLoading,
  hideLoading,
  showSuccess,
  showError
} from '../../utils/api.js'

Page({
  data: {
    loading: true,
    companyInfo: null,
    type: '', // 'factory' 或 'design'
    companyId: '',
    isFavorited: false,
    favoriteId: null,
    companyTypeName: '公司'
  },

  onLoad(options) {
    console.log('company-detail onLoad, options:', options)
    const { type, id } = options

    if (!type || !id) {
      console.error('参数错误:', { type, id })
      showError('参数错误')
      wx.navigateBack()
      return
    }

    console.log('设置页面数据:', { type, id })
    this.setData({
      type,
      companyId: id
    })

    this.fetchCompanyDetail()
  },

  // 获取公司详情
  async fetchCompanyDetail() {
    try {
      console.log('开始获取公司详情:', { type: this.data.type, id: this.data.companyId })
      this.setData({ loading: true })

      const result = await getCompanyDetail(this.data.type, this.data.companyId)
      console.log('获取公司详情结果:', result)

      if (result.success) {
        console.log('公司详情数据:', result.data)
        this.setData({
          companyInfo: result.data,
          companyTypeName: this.getCompanyTypeName(result.data)
        })

        // 检查收藏状态
        await this.checkFavoriteStatus()
      } else {
        console.error('获取公司详情失败:', result.message)
        showError(result.message || '获取公司详情失败')
      }
    } catch (error) {
      console.error('获取公司详情异常:', error)
      showError('获取公司详情失败')
    } finally {
      this.setData({ loading: false })
    }
  },

  // 检查收藏状态
  async checkFavoriteStatus() {
    try {
      const favoriteType = this.data.type === 'factory' ? 3 : 2
      const result = await checkFavoriteStatus(favoriteType, this.data.companyId)

      if (result.success) {
        this.setData({
          isFavorited: result.data.isFavorited,
          favoriteId: result.data.favoriteId
        })
      }
    } catch (error) {
      console.error('检查收藏状态失败:', error)
    }
  },

  // 切换收藏状态
  async toggleFavorite() {
    try {
      if (this.data.isFavorited) {
        // 取消收藏
        const result = await removeFavorite(this.data.favoriteId)
        if (result.success) {
          this.setData({
            isFavorited: false,
            favoriteId: null
          })
          showSuccess('取消收藏成功')
        }
      } else {
        // 添加收藏
        const favoriteType = this.data.type === 'factory' ? 3 : 2
        const result = await addFavorite(favoriteType, this.data.companyId)
        if (result.success) {
          this.setData({
            isFavorited: true,
            favoriteId: result.data.favoriteId
          })
          showSuccess('收藏成功')
        }
      }
    } catch (error) {
      console.error('收藏操作失败:', error)
      showError('操作失败，请重试')
    }
  },

  // 拨打电话
  makeCall() {
    const phone = this.data.companyInfo && this.data.companyInfo.contactPhone
    if (phone) {
      wx.makePhoneCall({
        phoneNumber: phone,
        fail: (error) => {
          console.error('拨打电话失败:', error)
          showError('拨打电话失败')
        }
      })
    } else {
      showError('暂无联系电话')
    }
  },

  // 获取公司类型名称
  getCompanyTypeName(companyInfo) {
    if (!companyInfo) return '公司'

    if (this.data.type === 'factory') {
      return companyInfo.type === 'comprehensive' ? '综合商' : '工厂'
    } else if (this.data.type === 'design') {
      return companyInfo.type === 'comprehensive' ? '综合商' : '设计公司'
    }
    return '公司'
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.fetchCompanyDetail().finally(() => {
      wx.stopPullDownRefresh()
    })
  }
})