<!--pages/exhibition-detail/exhibition-detail.wxml-->
<view class="page">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 展会详情 -->
  <view wx:elif="{{exhibition}}" class="content">
    <!-- 展会基本信息 -->
    <view class="exhibition-info">
      <view class="exhibition-name">{{exhibition.name}}</view>
      <view class="exhibition-meta">
        <view class="meta-item">
          <text class="meta-icon">📅</text>
          <text class="meta-text">{{exhibition.startDate}} - {{exhibition.endDate}}</text>
        </view>
        <view class="meta-item">
          <text class="meta-icon">📍</text>
          <text class="meta-text">{{exhibition.city}}</text>
        </view>
      </view>
    </view>

    <!-- 参展信息 -->
    <view class="participation-info">
      <view class="section-title">参展状态</view>
      <view wx:if="{{isParticipated}}" class="participation-card participated">
        <view class="participation-type">
          <text class="type-icon">{{participationTypeIcon}}</text>
          <text class="type-text">以{{participationTypeName}}身份参展</text>
        </view>
        <view class="participation-time">
          参展时间：{{participationTime}}
        </view>
      </view>
      <view wx:else class="participation-card not-participated">
        <view class="not-participated-text">
          <text class="status-icon">⚪</text>
          <text class="status-text">未参展</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <button
        wx:if="{{!isParticipated && (exhibition.status === 0 || exhibition.status === 1)}}"
        class="join-btn"
        loading="{{joining}}"
        bind:tap="handleJoinExhibition"
      >
        参加展会
      </button>

      <button
        wx:elif="{{isParticipated && exhibition.status === 0}}"
        class="cancel-btn"
        loading="{{canceling}}"
        bind:tap="handleCancelExhibition"
      >
        取消参展
      </button>

      <view wx:elif="{{exhibition.status === 2}}" class="finished-tip">
        展会已结束
      </view>

      <view wx:elif="{{exhibition.status === 3}}" class="canceled-tip">
        展会已取消
      </view>
    </view>
  </view>

  <!-- 错误状态 -->
  <view wx:else class="error-container">
    <view class="error-icon">😕</view>
    <view class="error-text">展会信息加载失败</view>
    <button class="retry-btn" bind:tap="loadExhibitionDetail">重新加载</button>
  </view>
</view>