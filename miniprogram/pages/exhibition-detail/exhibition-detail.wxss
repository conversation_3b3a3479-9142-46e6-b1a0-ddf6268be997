/* pages/exhibition-detail/exhibition-detail.wxss */
.page {
  min-height: 100vh;
  background: #f5f5f5;
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 40rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

.error-icon {
  font-size: 100rpx;
  margin-bottom: 30rpx;
}

.error-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.retry-btn {
  background: #1989fa;
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

.content {
  padding: 30rpx;
}

.exhibition-info {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
}

.exhibition-name {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  line-height: 1.3;
}

.exhibition-meta {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.meta-item {
  display: flex;
  align-items: center;
}

.meta-icon {
  font-size: 28rpx;
  margin-right: 20rpx;
  width: 40rpx;
}

.meta-text {
  font-size: 28rpx;
  color: #666;
  flex: 1;
}

.participation-info {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.description-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.participation-card {
  border-radius: 15rpx;
  padding: 25rpx;
}

.participation-card.participated {
  background: #e8f8e8;
  border: 2rpx solid #52c41a;
}

.participation-card.not-participated {
  background: #f8f9fa;
  border: 2rpx solid #e0e0e0;
}

.not-participated-text {
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
  color: #999;
}

.status-text {
  font-size: 28rpx;
  color: #999;
}

.participation-type {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.type-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
}

.type-text {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.participation-time {
  font-size: 24rpx;
  color: #999;
}

.action-section {
  padding: 20rpx 0 40rpx;
}

.join-btn {
  background: linear-gradient(135deg, #1989fa 0%, #0066cc 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 30rpx;
  font-size: 32rpx;
  font-weight: bold;
  width: 100%;
}

.cancel-btn {
  background: linear-gradient(135deg, #ff4757 0%, #cc3333 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 30rpx;
  font-size: 32rpx;
  font-weight: bold;
  width: 100%;
}

.finished-tip, .canceled-tip {
  text-align: center;
  padding: 30rpx;
  font-size: 28rpx;
  color: #999;
  background: #f8f9fa;
  border-radius: 50rpx;
}