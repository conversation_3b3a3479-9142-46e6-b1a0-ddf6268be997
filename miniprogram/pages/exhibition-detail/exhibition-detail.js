// pages/exhibition-detail/exhibition-detail.js
import {
  getExhibitions,
  manageExhibitionParticipation,
  showError,
  showSuccess,
  showLoading,
  hideLoading
} from '../../utils/api.js'

Page({
  data: {
    exhibitionId: '',
    exhibition: null,
    loading: true,
    joining: false,
    canceling: false,
    isParticipated: false,
    participantCount: 0,
    participationInfo: null
  },



  onLoad(options) {
    const { id } = options
    if (!id) {
      showError('展会ID不能为空')
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    this.setData({ exhibitionId: id })
    this.loadExhibitionDetail()
  },

  // 加载展会详情
  async loadExhibitionDetail() {
    try {
      this.setData({ loading: true })

      // 直接调用云函数获取单个展会详情
      const result = await wx.cloud.callFunction({
        name: 'getExhibitions',
        data: {
          type: 'single',
          id: this.data.exhibitionId
        }
      })

      if (result.result.success && result.result.data) {
        const exhibition = result.result.data

        // 格式化日期（云函数已经转换了字段名）
        exhibition.startDate = this.formatDate(exhibition.startDate)
        exhibition.endDate = this.formatDate(exhibition.endDate)

        // 计算状态文本
        const statusMap = {
          0: '未开始',
          1: '进行中',
          2: '已结束',
          3: '已取消'
        }
        const statusText = statusMap[exhibition.status] || '未知'

        this.setData({
          exhibition: exhibition,
          participantCount: exhibition.participant_count || 0,
          statusText: statusText
        })

        // 设置页面标题
        wx.setNavigationBarTitle({
          title: exhibition.name
        })

        // 检查参展状态
        await this.checkParticipationStatus()
      } else {
        showError(result.result.message || '展会不存在')
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      }
    } catch (error) {
      console.error('加载展会详情失败:', error)
      showError('加载失败，请重试')
    } finally {
      this.setData({ loading: false })
    }
  },

  // 检查参展状态
  async checkParticipationStatus() {
    try {
      const result = await manageExhibitionParticipation({
        action: 'check',
        exhibitionId: this.data.exhibitionId
      })

      if (result.success) {
        const participationInfo = result.data.participationInfo
        let participationTypeIcon = '🏢'
        let participationTypeName = '参展商'
        let participationTime = ''

        if (participationInfo) {
          // 计算参展类型图标和名称
          const iconMap = {
            1: '🏢', // 参展商
            2: '🎨', // 设计公司
            3: '🏭', // 工厂
            4: '🔧'  // 综合商
          }
          const nameMap = {
            1: '参展商',
            2: '设计公司',
            3: '工厂',
            4: '综合商'
          }

          participationTypeIcon = iconMap[participationInfo.participant_type] || '🏢'
          participationTypeName = nameMap[participationInfo.participant_type] || '参展商'

          // 计算参展时间
          if (participationInfo.created_at) {
            const date = new Date(participationInfo.created_at)
            participationTime = date.toLocaleDateString()
          }
        }

        this.setData({
          isParticipated: result.data.isParticipated,
          participationInfo: participationInfo,
          participationTypeIcon: participationTypeIcon,
          participationTypeName: participationTypeName,
          participationTime: participationTime
        })
      }
    } catch (error) {
      console.error('检查参展状态失败:', error)
    }
  },

  // 格式化日期
  formatDate(dateString) {
    if (!dateString) return ''
    const date = new Date(dateString)
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
  },

  // 参加展会
  async handleJoinExhibition() {
    try {
      this.setData({ joining: true })
      showLoading('参加中...')

      const result = await manageExhibitionParticipation({
        action: 'join',
        exhibitionId: this.data.exhibitionId
      })

      if (result.success) {
        showSuccess('参加成功')

        // 刷新参展状态
        await this.checkParticipationStatus()

        // 更新参展人数
        this.setData({
          participantCount: this.data.participantCount + 1
        })
      } else {
        showError(result.message || '参加失败')
      }
    } catch (error) {
      console.error('参加展会失败:', error)
      showError('参加失败，请重试')
    } finally {
      this.setData({ joining: false })
      hideLoading()
    }
  },

  // 取消参展
  async handleCancelExhibition() {
    wx.showModal({
      title: '确认取消',
      content: '确定要取消参展吗？',
      success: async (res) => {
        if (res.confirm) {
          await this.performCancelExhibition()
        }
      }
    })
  },

  // 执行取消参展
  async performCancelExhibition() {
    try {
      this.setData({ canceling: true })
      showLoading('取消中...')

      const result = await manageExhibitionParticipation({
        action: 'leave',
        exhibitionId: this.data.exhibitionId
      })

      if (result.success) {
        showSuccess('取消成功')

        // 刷新参展状态
        await this.checkParticipationStatus()

        // 更新参展人数
        this.setData({
          participantCount: Math.max(0, this.data.participantCount - 1)
        })
      } else {
        showError(result.message || '取消失败')
      }
    } catch (error) {
      console.error('取消参展失败:', error)
      showError('取消失败，请重试')
    } finally {
      this.setData({ canceling: false })
      hideLoading()
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadExhibitionDetail().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 分享
  onShareAppMessage() {
    return {
      title: this.data.exhibition?.name || '展会详情',
      path: `/pages/exhibition-detail/exhibition-detail?id=${this.data.exhibitionId}`
    }
  }
})