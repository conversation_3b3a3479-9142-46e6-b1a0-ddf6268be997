<!--pages/profile/profile.wxml-->
<view class="page">
  <!-- 已登录状态 -->
  <view wx:if="{{isLoggedIn && userInfo}}">
    <!-- 用户信息区域 -->
    <view class="user-section">
      <view class="user-info">
        <view class="avatar-container">
          <image class="avatar" wx:if="{{userInfo.avatar}}" src="{{userInfo.avatar}}" mode="aspectFill"></image>
          <view class="avatar avatar-placeholder" wx:else>
            <text>👤</text>
          </view>
        </view>
        <view class="user-details">
          <view class="nickname">{{userInfo.nickname || '未设置昵称'}}</view>
          <view class="user-type">{{userTypeText}}</view>
        </view>
        <view class="edit-icon" bind:tap="goToMyInfo">
          <text>✏️</text>
        </view>
      </view>

      <!-- 统计数据 -->
      <view class="stats-section">
        <view class="stat-item" bind:tap="goToMyExhibitions">
          <view class="stat-number">{{stats.exhibitionCount}}</view>
          <view class="stat-label">我的展会</view>
        </view>
        <view class="stat-item" bind:tap="goToMyFavorites">
          <view class="stat-number">{{stats.favoriteCount}}</view>
          <view class="stat-label">我的收藏</view>
        </view>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="menu-group">
        <view class="menu-item" bind:tap="goToMyInfo">
          <view class="menu-icon">👤</view>
          <view class="menu-text">个人信息</view>
          <view class="menu-arrow">></view>
        </view>

        <view class="menu-item" bind:tap="goToChangeUserType">
          <view class="menu-icon">🎯</view>
          <view class="menu-text">修改身份</view>
          <view class="menu-arrow">></view>
        </view>

        <view class="menu-item" bind:tap="goToMyExhibitions">
          <view class="menu-icon">🏢</view>
          <view class="menu-text">我的展会</view>
          <view class="menu-arrow">></view>
        </view>

        <view class="menu-item" bind:tap="goToMyFavorites">
          <view class="menu-icon">⭐</view>
          <view class="menu-text">我的收藏</view>
          <view class="menu-arrow">></view>
        </view>

        <view class="menu-item" bind:tap="goToJoinExhibition">
          <view class="menu-icon">➕</view>
          <view class="menu-text">参加展会</view>
          <view class="menu-arrow">></view>
        </view>
      </view>

      <view class="menu-group">
        <view class="menu-item" bind:tap="showAboutUs">
          <view class="menu-icon">ℹ️</view>
          <view class="menu-text">关于我们</view>
          <view class="menu-arrow">></view>
        </view>

        <view class="menu-item logout-item" bind:tap="handleLogout">
          <view class="menu-icon">🚪</view>
          <view class="menu-text">退出登录</view>
          <view class="menu-arrow">></view>
        </view>
      </view>
    </view>
  </view>

  <!-- 未登录状态 -->
  <view wx:else class="login-prompt">
    <view class="login-icon">🔐</view>
    <view class="login-text">请先登录</view>
    <view class="login-desc">登录后可查看个人信息和使用更多功能</view>
    <button class="login-btn" bind:tap="handleLogin">立即登录</button>
  </view>

  <!-- 关于我们弹窗 -->
  <view class="about-modal" wx:if="{{showAboutModal}}" bind:tap="closeAboutModal">
    <view class="about-content" catch:tap="">
      <view class="about-header">
        <text class="about-title">关于我们</text>
        <text class="about-close" bind:tap="closeAboutModal">×</text>
      </view>
      <view class="about-body">
        <view class="about-item">
          <text class="about-label">联系地址：</text>
          <text class="about-value">上海</text>
        </view>
        <view class="about-item">
          <text class="about-label">联系方式：</text>
          <text class="about-value">021-1234567</text>
        </view>
      </view>
      <view class="about-footer">
        <button class="about-btn" bind:tap="closeAboutModal">知道了</button>
      </view>
    </view>
  </view>
</view>