/* pages/profile/profile.wxss */
.page {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 100rpx;
}

/* 用户信息区域 */
.user-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 40rpx 30rpx;
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.avatar-container {
  margin-right: 30rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
}

.avatar image {
  width: 100%;
  height: 100%;
}

.avatar-placeholder {
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  color: #999;
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.user-details {
  flex: 1;
}

.nickname {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.user-type {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  background: rgba(255, 255, 255, 0.25);
  padding: 6rpx 16rpx;
  border-radius: 16rpx;
  display: inline-block;
  margin-top: 8rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.edit-icon {
  font-size: 40rpx;
  padding: 10rpx;
}

/* 统计数据 */
.stats-section {
  display: flex;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  padding: 30rpx;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-number {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 功能菜单 */
.menu-section {
  padding: 30rpx;
}

.menu-group {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: #f8f9fa;
}

.menu-icon {
  font-size: 40rpx;
  margin-right: 30rpx;
  width: 60rpx;
  text-align: center;
}

.menu-text {
  flex: 1;
  font-size: 30rpx;
  color: #333;
}

.menu-arrow {
  font-size: 24rpx;
  color: #999;
}

.logout-item {
  background-color: #fff5f5;
}

.logout-item .menu-text {
  color: #ee0a24;
}

.logout-item .menu-icon {
  color: #ee0a24;
}

/* 登录提示 */
.login-prompt {
  text-align: center;
  padding: 100rpx 40rpx;
  background: white;
  margin: 20rpx;
  border-radius: 20rpx;
}

.login-icon {
  font-size: 100rpx;
  margin-bottom: 30rpx;
}

.login-text {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.login-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 50rpx;
  line-height: 1.5;
}

.login-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 60rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.login-btn:active {
  opacity: 0.8;
}

/* 关于我们弹窗样式 */
.about-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.about-content {
  width: 90%;
  max-width: 600rpx;
  background: white;
  border-radius: 24rpx;
  animation: scaleIn 0.3s ease-out;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.2);
}

@keyframes scaleIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.about-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 40rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.about-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.about-close {
  font-size: 48rpx;
  color: #999;
  line-height: 1;
  padding: 10rpx;
}

.about-close:active {
  color: #666;
}

.about-body {
  padding: 40rpx;
}

.about-item {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.about-item:last-child {
  margin-bottom: 0;
}

.about-label {
  font-size: 32rpx;
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}

.about-value {
  font-size: 32rpx;
  color: #333;
  flex: 1;
}

.about-footer {
  padding: 20rpx 40rpx 40rpx;
  text-align: center;
}

.about-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #1989fa 0%, #1976d2 100%);
  color: white;
  border: none;
  border-radius: 40rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.about-btn:active {
  opacity: 0.8;
}
