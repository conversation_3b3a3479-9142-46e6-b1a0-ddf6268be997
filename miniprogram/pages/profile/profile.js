// pages/profile/profile.js
import {
  login,
  getUserProfile,
  logout,
  getMyExhibitions,
  getFavoriteList,
  showError,
  showSuccess,
  showLoading,
  hideLoading
} from '../../utils/api.js'

Page({
  data: {
    userInfo: null,
    isLoggedIn: false, // 添加登录状态标识
    userTypeText: '未设置身份', // 用户身份类型文本
    showAboutModal: false, // 控制关于我们弹窗显示
    stats: {
      exhibitionCount: 0,
      favoriteCount: 0
    }
  },

  onLoad() {
    this.checkLoginStatus()
  },

  onShow() {
    this.checkLoginStatus()
  },

  // 检查登录状态
  checkLoginStatus() {
    const isLoggedIn = wx.getStorageSync('isLoggedIn')
    if (isLoggedIn) {
      this.setData({ isLoggedIn: true })
      this.loadUserProfile()
      this.loadStats()
    } else {
      this.setData({
        isLoggedIn: false,
        userInfo: null,
        userTypeText: '未设置身份',
        stats: {
          exhibitionCount: 0,
          favoriteCount: 0
        }
      })
    }
  },

  // 加载用户信息
  async loadUserProfile() {
    try {
      // 先检查本地是否有缓存的用户信息
      const cachedUserInfo = wx.getStorageSync('userInfo')
      if (cachedUserInfo) {
        this.setData({
          userInfo: cachedUserInfo,
          userTypeText: this.getUserTypeText(cachedUserInfo.userType)
        })
        return
      }

      // 如果没有缓存，从服务器获取
      const result = await getUserProfile()

      if (result.success) {
        // 缓存用户信息
        wx.setStorageSync('userInfo', result.data.userInfo)
        this.setData({
          userInfo: result.data.userInfo,
          userTypeText: this.getUserTypeText(result.data.userInfo.userType)
        })
      } else {
        // 如果获取失败，清空登录状态
        wx.removeStorageSync('isLoggedIn')
        this.setData({
          isLoggedIn: false,
          userInfo: null,
          userTypeText: '未设置身份'
        })
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 失败时清空登录状态
      wx.removeStorageSync('isLoggedIn')
      this.setData({
        isLoggedIn: false,
        userInfo: null,
        userTypeText: '未设置身份'
      })
    }
  },

  // 加载统计数据
  async loadStats() {
    try {
      // 获取我的展会数量
      const exhibitionResult = await getMyExhibitions({ page: 1, pageSize: 1 })

      // 获取我的收藏数量
      const favoriteResult = await getFavoriteList({ page: 1, pageSize: 1 })

      this.setData({
        stats: {
          exhibitionCount: exhibitionResult.success ? exhibitionResult.data.total : 0,
          favoriteCount: favoriteResult.success ? favoriteResult.data.total : 0
        }
      })
    } catch (error) {
      console.error('获取统计数据失败:', error)
      // 设置默认统计数据
      this.setData({
        stats: {
          exhibitionCount: 0,
          favoriteCount: 0
        }
      })
    }
  },

  // 跳转到个人信息编辑页
  goToMyInfo() {
    wx.navigateTo({
      url: '/pages/my-info/my-info'
    })
  },

  // 跳转到我的展会
  goToMyExhibitions() {
    wx.navigateTo({
      url: '/pages/my-exhibitions/my-exhibitions'
    })
  },

  // 跳转到我的收藏
  goToMyFavorites() {
    wx.navigateTo({
      url: '/pages/my-favorites/my-favorites'
    })
  },

  // 跳转到修改身份
  goToChangeUserType() {
    wx.navigateTo({
      url: '/pages/change-user-type/change-user-type'
    })
  },

  // 跳转到参加展会
  goToJoinExhibition() {
    wx.navigateTo({
      url: '/pages/join-exhibition/join-exhibition'
    })
  },

  // 显示关于我们
  showAboutUs() {
    this.setData({
      showAboutModal: true
    })
  },

  // 关闭关于我们弹窗
  closeAboutModal() {
    this.setData({
      showAboutModal: false
    })
  },

  // 获取用户类型文本
  getUserTypeText(userType) {
    const typeMap = {
      1: '参展商',
      2: '设计公司',
      3: '工厂',
      4: '综合商'
    }
    return typeMap[userType] || '未设置身份'
  },

  // 退出登录
  handleLogout() {
    wx.showModal({
      title: '退出登录',
      content: '确定要退出登录吗？退出后需要重新授权登录。',
      success: (res) => {
        if (res.confirm) {
          this.performLogout()
        }
      }
    })
  },

  // 执行退出登录
  async performLogout() {
    try {
      // 清除登录状态标识
      wx.removeStorageSync('isLoggedIn')
      wx.removeStorageSync('userInfo')

      // 清除页面数据
      this.setData({
        isLoggedIn: false,
        userInfo: null,
        userTypeText: '未设置身份',
        stats: {
          exhibitionCount: 0,
          favoriteCount: 0
        }
      })

      // 显示成功提示
      wx.showToast({
        title: '已退出登录',
        icon: 'success'
      })

      // 跳转到首页并重新登录
      setTimeout(() => {
        wx.reLaunch({
          url: '/pages/home/<USER>'
        })
      }, 1500)

    } catch (error) {
      console.error('退出登录失败:', error)
      // 即使出错，也执行本地退出
      wx.removeStorageSync('isLoggedIn')
      wx.removeStorageSync('userInfo')
      this.setData({
        isLoggedIn: false,
        userInfo: null,
        userTypeText: '未设置身份',
        stats: {
          exhibitionCount: 0,
          favoriteCount: 0
        }
      })

      wx.showToast({
        title: '已退出登录',
        icon: 'success'
      })
    }
  },

  // 处理登录
  async handleLogin() {
    try {
      showLoading('登录中...')

      // 调用登录API
      const result = await login()

      if (result.success) {
        // 设置登录状态
        wx.setStorageSync('isLoggedIn', true)
        wx.setStorageSync('userInfo', result.data.userInfo)

        // 检查是否需要选择身份
        if (result.data.needSelectUserType) {
          showSuccess('登录成功，请选择您的身份')
          setTimeout(() => {
            wx.navigateTo({
              url: '/pages/select-user-type/select-user-type?required=true'
            })
          }, 1500)
        } else {
          showSuccess('登录成功')

          // 更新页面状态和数据
          this.setData({
            isLoggedIn: true,
            userTypeText: this.getUserTypeText(result.data.userInfo.userType)
          })
          await this.loadUserProfile()
          await this.loadStats()
        }
      } else {
        showError(result.message || '登录失败')
      }
    } catch (error) {
      console.error('登录失败:', error)
      showError('登录失败，请重试')
    } finally {
      hideLoading()
    }
  }
})