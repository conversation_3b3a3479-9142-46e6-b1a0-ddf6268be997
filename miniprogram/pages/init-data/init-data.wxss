/* pages/init-data/init-data.wxss */
.page {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 30rpx;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.content {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
}

.section {
  margin-bottom: 40rpx;
}

.section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.info {
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.init-btn {
  width: 100%;
  background: #1989fa;
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 30rpx;
  margin-bottom: 20rpx;
}

.description {
  text-align: center;
  color: #666;
  font-size: 26rpx;
}

.city-list {
  max-height: 400rpx;
  overflow-y: auto;
}

.city-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.city-item:last-child {
  border-bottom: none;
}

.city-name {
  font-size: 28rpx;
  color: #333;
}

.city-province {
  font-size: 24rpx;
  color: #666;
}

.actions {
  margin-top: 40rpx;
  text-align: center;
}

.home-btn {
  width: 200rpx;
  background: #666;
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 15rpx;
  font-size: 28rpx;
}
