<!--pages/init-data/init-data.wxml-->
<view class="page">
  <view class="header">
    <text class="title">数据初始化</text>
  </view>

  <view class="content">
    <view class="section">
      <view class="section-title">城市数据管理</view>
      
      <view class="info">
        <text>当前城市数量：{{cities.length}}</text>
      </view>
      
      <button class="init-btn" bind:tap="initCities">
        初始化默认城市数据
      </button>
      
      <view class="description">
        <text>将添加北京、上海、广州、深圳等15个主要城市</text>
      </view>
    </view>

    <view class="section" wx:if="{{cities.length > 0}}">
      <view class="section-title">当前城市列表</view>
      
      <view class="city-list">
        <view 
          wx:for="{{cities}}" 
          wx:key="id" 
          class="city-item"
        >
          <text class="city-name">{{item.name}}</text>
          <text class="city-province" wx:if="{{item.province}}">{{item.province}}</text>
        </view>
      </view>
    </view>

    <view class="actions">
      <button class="home-btn" bind:tap="goHome">
        返回首页
      </button>
    </view>
  </view>
</view>
