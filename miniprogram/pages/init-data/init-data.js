// pages/init-data/init-data.js
import {
  initDefaultCities,
  getCityList,
  showError,
  showSuccess,
  showLoading,
  hideLoading
} from '../../utils/api.js'
import { initCities, getCities } from '../../utils/cityManager.js'

Page({
  data: {
    cities: [],
    loading: false
  },

  onLoad() {
    this.loadCities()
  },

  // 加载城市列表
  async loadCities() {
    try {
      showLoading('加载中...')
      const cities = await getCities(true) // 强制刷新

      this.setData({
        cities: cities
      })
    } catch (error) {
      console.error('加载城市列表失败:', error)
    } finally {
      hideLoading()
    }
  },

  // 初始化默认城市数据
  async initCities() {
    try {
      const result = await wx.showModal({
        title: '确认初始化',
        content: '这将清空现有城市数据并添加默认城市，确定继续吗？'
      })

      if (result.confirm) {
        showLoading('初始化中...')

        const initResult = await initCities()

        if (initResult.success) {
          showSuccess(initResult.message)
          // 重新加载城市列表
          this.loadCities()
        } else {
          showError(initResult.message || '初始化失败')
        }
      }
    } catch (error) {
      console.error('初始化城市数据失败:', error)
      showError(error)
    } finally {
      hideLoading()
    }
  },

  // 返回首页
  goHome() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    })
  }
})
