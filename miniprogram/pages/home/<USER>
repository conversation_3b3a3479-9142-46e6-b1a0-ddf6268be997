/* pages/home/<USER>/
.page {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 100rpx;
}

/* Banner区域 */
.banner-section {
  background: linear-gradient(135deg, #1989fa 0%, #1976d2 100%);
  padding: 40rpx 30rpx 30rpx;
  color: white;
}

.logo-area {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
}

.logo-icon {
  font-size: 60rpx;
  margin-right: 20rpx;
}

.app-title {
  font-size: 48rpx;
  font-weight: bold;
}

/* 搜索区域 */
.search-section {
  background: white;
  margin: -20rpx 30rpx 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.search-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 30rpx;
}

.search-form {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.form-row {
  display: flex;
  gap: 20rpx;
}

/* picker 组件需要设置 flex: 1 才能平分宽度 */
.form-row picker {
  flex: 1;
}

.form-item-wrapper {
  position: relative;
  flex: 1;
}

.form-item {
  display: flex;
  align-items: center;
  padding: 15rpx 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid transparent;
  width: 100%;
  height: 60rpx;
  min-height: 60rpx;
  max-height: 60rpx;
  box-sizing: border-box; /* 确保padding不会导致超出 */
}

.form-item:active {
  border-color: #1989fa;
}

.label {
  font-size: 26rpx;
  color: #666;
  width: 70rpx;
  flex-shrink: 0;
  text-align: center;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.value {
  flex: 1;
  font-size: 26rpx;
  color: #333;
  margin-left: 15rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 0; /* 确保flex子元素可以收缩 */
  line-height: 1;
  display: flex;
  align-items: center;
}

.value.placeholder {
  color: #999;
}

.arrow {
  color: #999;
  font-size: 22rpx;
  flex-shrink: 0;
  margin-left: 8rpx;
  line-height: 1;
  display: flex;
  align-items: center;
}

.clear-btn {
  position: absolute;
  right: 10rpx;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
  font-size: 32rpx;
  padding: 8rpx;
  line-height: 1;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  min-width: 32rpx;
  text-align: center;
  z-index: 10;
}

.clear-btn:active {
  color: #666;
  background: rgba(255, 255, 255, 1);
}

.search-btn {
  background: linear-gradient(135deg, #1989fa 0%, #1976d2 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  height: 80rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-top: 20rpx;
}

/* 区块样式 */
.section {
  margin: 30rpx;
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.section-more {
  font-size: 26rpx;
  color: #1989fa;
}

/* 展会列表 */
.exhibitions-scroll {
  white-space: nowrap;
}

.exhibitions-list {
  display: flex;
  gap: 20rpx;
}

.exhibition-card {
  display: inline-block;
  width: 300rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  vertical-align: top;
}

.exhibition-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.exhibition-info {
  margin-bottom: 15rpx;
}

.info-item {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 5rpx;
}

.exhibition-status {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  text-align: center;
  color: white;
}

.exhibition-status.upcoming {
  background: #1989fa;
}

.exhibition-status.ongoing {
  background: #07c160;
}

.exhibition-status.ended {
  background: #969799;
}

/* 公司列表 */
.companies-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.company-card {
  padding: 30rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border: 2rpx solid transparent;
}

.company-card:active {
  border-color: #1989fa;
}

.company-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.company-info {
  flex: 1;
}

.company-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.company-type {
  font-size: 24rpx;
  color: #1989fa;
  background: rgba(25, 137, 250, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  display: inline-block;
}

.company-actions {
  margin-left: 20rpx;
}

.favorite-icon {
  font-size: 40rpx;
  color: #ddd;
}

.favorite-icon.favorited {
  color: #ffd21e;
}

.company-details {
  margin-bottom: 20rpx;
}

.detail-item {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.detail-item .icon {
  margin-right: 10rpx;
  font-size: 24rpx;
}

.company-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.rating {
  display: flex;
  align-items: center;
}

.stars {
  color: #ffd21e;
  font-size: 24rpx;
  margin-right: 8rpx;
}

.level {
  font-size: 24rpx;
  color: #666;
}

.status {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  color: white;
}

.status.available {
  background: #07c160;
}

.status.full {
  background: #ee0a24;
}

/* 选择器样式 */
.picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.picker-content {
  width: 95%;
  max-width: 750rpx;
  background: white;
  border-radius: 24rpx;
  max-height: 70vh;
  animation: scaleIn 0.3s ease-out;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.2);
}

@keyframes scaleIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: #fafafa;
  border-radius: 24rpx 24rpx 0 0;
}

.picker-cancel {
  font-size: 32rpx;
  color: #666;
  padding: 16rpx 24rpx;
  border-radius: 20rpx;
  background: transparent;
}

.picker-cancel:active {
  background: #f0f0f0;
}

.picker-confirm {
  font-size: 32rpx;
  color: white;
  background: linear-gradient(135deg, #1989fa 0%, #1976d2 100%);
  padding: 16rpx 32rpx;
  border-radius: 24rpx;
  font-weight: bold;
}

.picker-confirm:active {
  opacity: 0.8;
}

.picker-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.picker-view {
  height: 400rpx;
  padding: 0 20rpx;
}

.picker-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 96rpx;
  font-size: 34rpx;
  color: #333;
  border-radius: 16rpx;
  margin: 8rpx 0;
  transition: all 0.2s ease;
}

.picker-item:active {
  background: #f0f8ff;
  color: #1989fa;
}

.date-picker {
  padding: 30rpx;
  text-align: center;
}

.city-picker {
  padding: 30rpx;
  text-align: center;
  width: 100%;
}

.city-picker picker {
  width: 100%;
}

.picker-placeholder {
  font-size: 30rpx;
  color: #1989fa;
  padding: 20rpx;
}
