<!--pages/home/<USER>
<view class="page">
  <!-- Banner区域 -->
  <view class="banner-section">
    <view class="logo-area">
      <text class="logo-icon">🏢</text>
      <text class="app-title">馆拼拼</text>
    </view>
  </view>

  <!-- 搜索区域 -->
  <view class="search-section">
    <view class="search-title">找展会，找合作</view>
    
    <view class="search-form">
      <view class="form-row">
        <view class="form-item-wrapper">
          <picker
            mode="selector"
            range="{{cityColumns}}"
            range-key="text"
            value="{{selectedCityIndex}}"
            bind:change="onCityChange"
          >
            <view class="form-item">
              <text class="label">城市</text>
              <text class="value {{searchForm.cityName ? '' : 'placeholder'}}">
                {{searchForm.cityName}}
              </text>
              <text class="arrow">></text>
            </view>
          </picker>
          <text wx:if="{{searchForm.cityName}}" class="clear-btn" bind:tap="clearCity">×</text>
        </view>
        
        <view class="form-item-wrapper">
          <picker
            mode="date"
            value="{{searchForm.dateValue}}"
            start="{{today}}"
            bind:change="onDateChange"
          >
            <view class="form-item">
              <text class="label">日期</text>
              <text class="value {{searchForm.dateName ? '' : 'placeholder'}}">
                {{searchForm.dateName}}
              </text>
              <text class="arrow">></text>
            </view>
          </picker>
          <text wx:if="{{searchForm.dateName}}" class="clear-btn" bind:tap="clearDate">×</text>
        </view>
      </view>
      
      <view class="form-row">
        <view class="form-item-wrapper">
          <picker
            mode="selector"
            range="{{venueColumns}}"
            range-key="text"
            value="{{selectedVenueIndex}}"
            bind:change="onVenueChange"
          >
            <view class="form-item">
              <text class="label">场馆</text>
              <text class="value {{searchForm.venueName ? '' : 'placeholder'}}">
                {{searchForm.venueName}}
              </text>
              <text class="arrow">></text>
            </view>
          </picker>
          <text wx:if="{{searchForm.venueName}}" class="clear-btn" bind:tap="clearVenue">×</text>
        </view>
        
        <view class="form-item-wrapper">
          <picker
            mode="selector"
            range="{{typeColumns}}"
            range-key="text"
            value="{{selectedTypeIndex}}"
            bind:change="onTypeChange"
          >
            <view class="form-item">
              <text class="label">类型</text>
              <text class="value {{searchForm.typeName ? '' : 'placeholder'}}">
                {{searchForm.typeName}}
              </text>
              <text class="arrow">></text>
            </view>
          </picker>
          <text wx:if="{{searchForm.typeName}}" class="clear-btn" bind:tap="clearType">×</text>
        </view>
      </view>
      
      <button class="search-btn" bind:tap="handleSearch">开始搜索</button>
    </view>
  </view>

  <!-- 近期展会 -->
  <view class="section" wx:if="{{recentExhibitions.length > 0}}">
    <view class="section-header">
      <text class="section-title">近期展会</text>
      <text class="section-more" bind:tap="goToMyExhibitions">参加展会</text>
    </view>
    
    <scroll-view class="exhibitions-scroll" scroll-x="true">
      <view class="exhibitions-list">
        <view 
          wx:for="{{recentExhibitions}}" 
          wx:key="id" 
          class="exhibition-card"
          bind:tap="goToExhibitionDetail"
          data-id="{{item.id}}"
        >
          <view class="exhibition-name">{{item.name}}</view>
          <view class="exhibition-info">
            <text class="info-item">📍 {{item.city}}</text>
            <text class="info-item">📅 {{item.startDate}}</text>
          </view>
          <view class="exhibition-status {{item.status === 0 ? 'upcoming' : item.status === 1 ? 'ongoing' : 'ended'}}">
            {{item.status === 0 ? '即将开始' : item.status === 1 ? '进行中' : '已结束'}}
          </view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 推荐工厂 -->
  <view class="section" wx:if="{{recommendFactories.length > 0}}">
    <view class="section-header">
      <text class="section-title">推荐工厂</text>
      <text class="section-more" bind:tap="goToFindFactory">查看更多</text>
    </view>
    
    <view class="companies-list">
      <view 
        wx:for="{{recommendFactories}}" 
        wx:key="id" 
        class="company-card"
        bind:tap="goToCompanyDetail"
        data-type="factory"
        data-id="{{item.id}}"
      >
        <view class="company-header">
          <view class="company-info">
            <view class="company-name">{{item.name}}</view>
            <view class="company-type">工厂</view>
          </view>
          <view class="company-actions">
            <text
              class="favorite-icon {{item.isFavorited ? 'favorited' : ''}}"
              bind:tap="toggleFavorite"
              data-item="{{item}}"
              data-type="factory"
            >
              {{item.isFavorited ? '⭐' : '☆'}}
            </text>
          </view>
        </view>
        
        <view class="company-details">
          <view class="detail-item" wx:if="{{item.city}}">
            <text class="icon">📍</text>
            <text>{{item.city}}</text>
          </view>
          <view class="detail-item" wx:if="{{item.contactPerson}}">
            <text class="icon">👤</text>
            <text>{{item.contactPerson}}</text>
          </view>
          <view class="detail-item" wx:if="{{item.specialties}}">
            <text class="icon">🔧</text>
            <text>{{item.specialties}}</text>
          </view>
        </view>
        
        <view class="company-footer">
          <view class="rating">
            <text class="stars">
              <text wx:for="{{item.level || 1}}" wx:key="*this">★</text>
            </text>
            <text class="level">{{item.level || 1}}星工厂</text>
          </view>
          <view class="status {{item.isFull ? 'full' : 'available'}}">
            {{item.isFull ? '已满额' : '可预约'}}
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 推荐设计公司 -->
  <view class="section" wx:if="{{recommendDesignCompanies.length > 0}}">
    <view class="section-header">
      <text class="section-title">推荐设计</text>
      <text class="section-more" bind:tap="goToFindDesign">查看更多</text>
    </view>
    
    <view class="companies-list">
      <view 
        wx:for="{{recommendDesignCompanies}}" 
        wx:key="id" 
        class="company-card"
        bind:tap="goToCompanyDetail"
        data-type="design"
        data-id="{{item.id}}"
      >
        <view class="company-header">
          <view class="company-info">
            <view class="company-name">{{item.name}}</view>
            <view class="company-type">设计公司</view>
          </view>
          <view class="company-actions">
            <text
              class="favorite-icon {{item.isFavorited ? 'favorited' : ''}}"
              bind:tap="toggleFavorite"
              data-item="{{item}}"
              data-type="design"
            >
              {{item.isFavorited ? '⭐' : '☆'}}
            </text>
          </view>
        </view>
        
        <view class="company-details">
          <view class="detail-item" wx:if="{{item.city}}">
            <text class="icon">📍</text>
            <text>{{item.city}}</text>
          </view>
          <view class="detail-item" wx:if="{{item.contactPerson}}">
            <text class="icon">👤</text>
            <text>{{item.contactPerson}}</text>
          </view>
        </view>
        
        <view class="company-footer">
          <view class="rating">
            <text class="stars">
              <text wx:for="{{item.level || 1}}" wx:key="*this">★</text>
            </text>
            <text class="level">{{item.level || 1}}星设计</text>
          </view>
          <view class="status {{item.isFull ? 'full' : 'available'}}">
            {{item.isFull ? '已满额' : '可预约'}}
          </view>
        </view>
      </view>
    </view>
  </view>






</view>
