// pages/home/<USER>
import { 
  getRecentExhibitions,
  searchFactories, 
  searchDesignCompanies,
  getBaseData,
  addFavorite,
  removeFavorite,
  checkFavoriteStatus,
  showError,
  showSuccess,
  showLoading,
  hideLoading
} from '../../utils/api.js'
import { getCityColumns, preloadCities } from '../../utils/cityManager.js'

Page({
  data: {
    // 搜索表单
    searchForm: {
      cityName: '',
      cityValue: '',
      dateName: '',
      dateValue: '',
      venueName: '',
      venueValue: '',
      typeName: '',
      typeValue: ''
    },
    
    // 选择器数据
    cityColumns: [],
    venueColumns: [],
    typeColumns: [
      { text: '找设计', value: 'design' },
      { text: '找工厂', value: 'factory' }
    ],
    
    // 选择器显示状态
    showCityPicker: false,
    showDatePicker: false,
    showVenuePicker: false,
    showTypePicker: false,
    
    // 数据列表
    recentExhibitions: [],
    recommendFactories: [],
    recommendDesignCompanies: [],

    // 加载状态
    loading: false,

    // 选择器索引
    selectedCityIndex: 0,
    selectedVenueIndex: 0,
    selectedTypeIndex: 0
  },

  onLoad() {
    // 优先加载基础数据，延迟加载推荐内容
    this.initBaseData()
    // 预加载城市数据
    preloadCities()
    // 获取所有场馆数据
    this.fetchVenues('')
  },

  onShow() {
    // 只在首次显示或数据为空时加载推荐数据
    if (this.data.recentExhibitions.length === 0) {
      this.fetchRecentExhibitions()
    }
    if (this.data.recommendFactories.length === 0) {
      this.fetchRecommendFactories()
    }
    if (this.data.recommendDesignCompanies.length === 0) {
      this.fetchRecommendDesignCompanies()
    }
  },

  onReady() {
    // 页面渲染完成后再加载推荐数据
    setTimeout(() => {
      this.loadRecommendData()
    }, 500)
  },

  // 初始化基础数据（仅加载必需的数据）
  async initBaseData() {
    try {
      // 使用新的城市管理器获取城市数据
      const cityColumns = await getCityColumns()
      this.setData({
        cityColumns: cityColumns
      })
    } catch (error) {
      console.error('初始化城市数据失败:', error)
      // 使用默认城市数据
      this.setData({
        cityColumns: [
          { text: '北京', value: '北京' },
          { text: '上海', value: '上海' },
          { text: '广州', value: '广州' },
          { text: '深圳', value: '深圳' },
          { text: '杭州', value: '杭州' }
        ]
      })
    }
  },

  // 加载推荐数据（延迟加载）
  async loadRecommendData() {
    try {
      // 并行加载推荐数据，但不阻塞页面渲染
      Promise.all([
        this.fetchRecentExhibitions(),
        this.fetchRecommendFactories(),
        this.fetchRecommendDesignCompanies()
      ]).catch(error => {
        console.error('加载推荐数据失败:', error)
      })
    } catch (error) {
      console.error('加载推荐数据失败:', error)
    }
  },

  // 获取近期展会
  async fetchRecentExhibitions() {
    try {
      const result = await getRecentExhibitions({ limit: 5 })
      if (result.success) {
        this.setData({
          recentExhibitions: result.data
        })
      }
    } catch (error) {
      console.error('获取近期展会失败:', error)
    }
  },

  // 获取推荐工厂
  async fetchRecommendFactories() {
    try {
      const result = await searchFactories({ 
        page: 1, 
        pageSize: 5,
        level: 5 // 优先显示高级别工厂
      })
      if (result.success) {
        this.setData({
          recommendFactories: result.data.list
        })
      }
    } catch (error) {
      console.error('获取推荐工厂失败:', error)
    }
  },

  // 获取推荐设计公司
  async fetchRecommendDesignCompanies() {
    try {
      const result = await searchDesignCompanies({ 
        page: 1, 
        pageSize: 5,
        level: 5 // 优先显示高级别设计公司
      })
      if (result.success) {
        this.setData({
          recommendDesignCompanies: result.data.list
        })
      }
    } catch (error) {
      console.error('获取推荐设计公司失败:', error)
    }
  },

  // 选择器变化事件
  onCityChange(e) {
    const index = e.detail.value
    const selectedCity = this.data.cityColumns[index]

    if (selectedCity) {
      this.setData({
        'searchForm.cityName': selectedCity.text,
        'searchForm.cityValue': selectedCity.value,
        selectedCityIndex: index,
        // 清空后续选择
        'searchForm.dateName': '',
        'searchForm.dateValue': '',
        'searchForm.venueName': '',
        'searchForm.venueValue': ''
      })

      // 根据城市获取场馆
      this.fetchVenues(selectedCity.value)
    }
  },

  onVenueChange(e) {
    const index = e.detail.value
    const selectedVenue = this.data.venueColumns[index]

    if (selectedVenue) {
      this.setData({
        'searchForm.venueName': selectedVenue.text,
        'searchForm.venueValue': selectedVenue.value,
        selectedVenueIndex: index
      })
    }
  },

  onTypeChange(e) {
    const index = e.detail.value
    const selectedType = this.data.typeColumns[index]

    if (selectedType) {
      this.setData({
        'searchForm.typeName': selectedType.text,
        'searchForm.typeValue': selectedType.value,
        selectedTypeIndex: index
      })
    }
  },

  // 选择器确认事件
  onCityConfirm() {
    // 原生picker组件会自动处理选择，这里只需要关闭弹窗
    this.setData({
      showCityPicker: false
    })
  },

  onDateChange(e) {
    const { value } = e.detail
    const date = new Date(value)

    this.setData({
      'searchForm.dateName': this.formatDate(date),
      'searchForm.dateValue': value,
      // 清空场馆选择
      'searchForm.venueName': '',
      'searchForm.venueValue': ''
    })
  },





  // 获取场馆列表
  async fetchVenues(city) {
    try {
      const result = await getBaseData({ type: 'venues' })
      if (result.success) {
        // 如果没有选择城市，显示所有场馆；否则按城市过滤
        const filteredVenues = city ?
          result.data.filter(venue => venue.city === city) :
          result.data
        this.setData({
          venueColumns: filteredVenues.map(venue => ({
            text: venue.name,
            value: venue.id
          }))
        })
      }
    } catch (error) {
      console.error('获取场馆失败:', error)
    }
  },

  // 搜索处理
  handleSearch() {
    const { searchForm } = this.data

    // 验证必填项：城市和日期必填，场馆可选
    if (!searchForm.cityValue) {
      wx.showToast({ title: '请选择城市', icon: 'none' })
      return
    }
    if (!searchForm.dateValue) {
      wx.showToast({ title: '请选择日期', icon: 'none' })
      return
    }
    if (!searchForm.typeValue) {
      wx.showToast({ title: '请选择找什么', icon: 'none' })
      return
    }

    // 构建搜索参数
    const searchParams = {
      city: searchForm.cityValue,
      date: searchForm.dateValue
    }

    // 场馆是可选的
    if (searchForm.venueValue) {
      searchParams.venueId = searchForm.venueValue
    }

    // 将搜索参数存储到全局，供目标页面使用
    const app = getApp()
    app.globalData.searchParams = {
      ...searchParams,
      cityName: searchForm.cityName,
      dateName: searchForm.dateName,
      venueName: searchForm.venueName || ''
    }

    // 根据类型跳转到对应页面
    if (searchForm.typeValue === 'design') {
      wx.switchTab({
        url: '/pages/find-design/find-design'
      })
    } else if (searchForm.typeValue === 'factory') {
      wx.switchTab({
        url: '/pages/find-factory/find-factory'
      })
    }
  },







  // 收藏切换
  async toggleFavorite(e) {
    const { item, type } = e.currentTarget.dataset
    
    try {
      if (item.isFavorited) {
        // 取消收藏
        await removeFavorite(item.favoriteId)
        showSuccess('取消收藏成功')
      } else {
        // 添加收藏
        const favoriteType = type === 'factory' ? 3 : 2
        const result = await addFavorite(favoriteType, item.id)
        if (result.success) {
          item.favoriteId = result.data.favoriteId
          showSuccess('收藏成功')
        }
      }
      
      // 更新收藏状态
      item.isFavorited = !item.isFavorited
      this.setData({
        recommendFactories: this.data.recommendFactories,
        recommendDesignCompanies: this.data.recommendDesignCompanies
      })
      
    } catch (error) {
      showError(error)
    }
  },

  // 跳转方法
  goToExhibitionDetail(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/exhibition-detail/exhibition-detail?id=${id}`
    })
  },

  goToCompanyDetail(e) {
    const { type, id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/company-detail/company-detail?type=${type}&id=${id}`
    })
  },

  goToFindFactory() {
    wx.switchTab({
      url: '/pages/find-factory/find-factory'
    })
  },

  goToFindDesign() {
    wx.switchTab({
      url: '/pages/find-design/find-design'
    })
  },

  goToMyExhibitions() {
    wx.navigateTo({
      url: '/pages/join-exhibition/join-exhibition'
    })
  },

  // 工具方法
  formatDate(date) {
    return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
  },

  formatDateISO(date) {
    return date.toISOString().split('T')[0]
  },

  buildQueryString(params) {
    return Object.keys(params)
      .map(key => `${key}=${encodeURIComponent(params[key])}`)
      .join('&')
  },

  // 清除按钮事件
  clearCity(e) {
    this.setData({
      'searchForm.cityName': '',
      'searchForm.cityValue': '',
      selectedCityIndex: 0,
      // 清空后续选择
      'searchForm.dateName': '',
      'searchForm.dateValue': '',
      'searchForm.venueName': '',
      'searchForm.venueValue': '',
      selectedVenueIndex: 0
    })
    // 重新加载所有场馆
    this.fetchVenues('')
  },

  clearDate(e) {
    this.setData({
      'searchForm.dateName': '',
      'searchForm.dateValue': '',
      // 清空后续选择
      'searchForm.venueName': '',
      'searchForm.venueValue': '',
      selectedVenueIndex: 0
    })
  },

  clearVenue(e) {
    this.setData({
      'searchForm.venueName': '',
      'searchForm.venueValue': '',
      selectedVenueIndex: 0
    })
  },

  clearType(e) {
    this.setData({
      'searchForm.typeName': '',
      'searchForm.typeValue': '',
      selectedTypeIndex: 0
    })
  },

  // 选择器取消事件
  onPickerCancel() {
    this.setData({
      showCityPicker: false,
      showDatePicker: false,
      showVenuePicker: false,
      showTypePicker: false
    })
  }
})
