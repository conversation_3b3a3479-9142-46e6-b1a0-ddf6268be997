/* pages/my-info/my-info.wxss */

/* 强制样式生效 */
page {
  background-color: #f5f5f5;
}

.page {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.content {
  background: white !important;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  margin: 20rpx;
}

.user-info {
  text-align: center;
  margin-bottom: 40rpx;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-bottom: 20rpx;
  border: 4rpx solid #e0e0e0;
  overflow: hidden;
}

.avatar image {
  width: 100%;
  height: 100%;
}

.avatar-placeholder {
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  color: #999;
}

.openid {
  font-size: 24rpx;
  color: #666;
  background: #f0f0f0;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.form-section {
  margin-bottom: 40rpx;
}

.form-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #eee;
}

.form-item:last-child {
  border-bottom: none;
}

.label {
  width: 120rpx;
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  padding: 10rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  margin-left: 20rpx;
}

.value {
  flex: 1;
  font-size: 28rpx;
  color: #666;
  margin-left: 20rpx;
}

.actions {
  margin-bottom: 40rpx;
}

.edit-btn {
  width: 100%;
  background: #1989fa;
  color: white;
  border: none;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 32rpx;
}

.edit-actions {
  display: flex;
  gap: 20rpx;
}

.cancel-btn {
  flex: 1;
  background: #f0f0f0;
  color: #333;
  border: none;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 28rpx;
}

.save-btn {
  flex: 1;
  background: #07c160;
  color: white;
  border: none;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 28rpx;
}

.other-info {
  border-top: 1rpx solid #eee;
  padding-top: 30rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item .label {
  width: auto;
  font-size: 26rpx;
  color: #666;
  font-weight: normal;
}

.info-item .value {
  font-size: 26rpx;
  color: #333;
  margin-left: 0;
}

.loading {
  text-align: center;
  padding: 100rpx;
  color: #999;
}