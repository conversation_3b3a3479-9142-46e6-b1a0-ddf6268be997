<!--pages/my-info/my-info.wxml-->
<wxs module="timeUtil" src="../../utils/time.wxs"></wxs>
<view class="page">
  <view class="content">
    <!-- 用户基本信息 -->
    <view class="user-info">
      <view class="avatar-section">
        <view class="avatar" wx:if="{{userInfo.avatar}}">
          <image src="{{userInfo.avatar}}" mode="aspectFill"></image>
        </view>
        <view class="avatar avatar-placeholder" wx:else>
          <text>👤</text>
        </view>
        <text class="openid">{{userInfo.openid ? userInfo.openid.slice(-8) : 'unknown'}}</text>
      </view>
    </view>

    <!-- 表单区域 -->
    <view class="form-section">
      <view class="form-item">
        <text class="label">昵称</text>
        <input
          wx:if="{{editing}}"
          class="input"
          value="{{formData.nickname}}"
          placeholder="请输入昵称"
          bindinput="onNicknameInput"
        />
        <text wx:else class="value">{{userInfo.nickname || '未设置'}}</text>
      </view>

      <view class="form-item">
        <text class="label">手机号</text>
        <input
          wx:if="{{editing}}"
          class="input"
          value="{{formData.phone}}"
          placeholder="请输入手机号"
          type="number"
          bindinput="onPhoneInput"
        />
        <text wx:else class="value">{{userInfo.phone || '未设置'}}</text>
      </view>

      <view class="form-item">
        <text class="label">邮箱</text>
        <input
          wx:if="{{editing}}"
          class="input"
          value="{{formData.email}}"
          placeholder="请输入邮箱"
          bindinput="onEmailInput"
        />
        <text wx:else class="value">{{userInfo.email || '未设置'}}</text>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="actions">
      <button wx:if="{{!editing}}" class="edit-btn" bind:tap="toggleEdit">编辑信息</button>
      <view wx:else class="edit-actions">
        <button class="cancel-btn" bind:tap="cancelEdit">取消</button>
        <button class="save-btn" bind:tap="saveProfile">保存</button>
      </view>
    </view>

    <!-- 其他信息 -->
    <view class="other-info">
      <view class="info-item">
        <text class="label">登录次数：</text>
        <text class="value">{{userInfo.loginCount || 0}}</text>
      </view>
      <view class="info-item">
        <text class="label">最后登录：</text>
        <text class="value">{{timeUtil.formatTime(userInfo.lastLoginAt, 'datetime')}}</text>
      </view>
      <view class="info-item">
        <text class="label">注册时间：</text>
        <text class="value">{{timeUtil.formatTime(userInfo.createdAt, 'datetime')}}</text>
      </view>
    </view>
  </view>
</view>