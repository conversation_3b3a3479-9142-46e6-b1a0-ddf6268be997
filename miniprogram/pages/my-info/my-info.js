// pages/my-info/my-info.js
import {
  getUserProfile,
  updateUserProfile,
  showError,
  showSuccess,
  showLoading,
  hideLoading,
  formatTime
} from '../../utils/api.js'

Page({
  data: {
    userInfo: null,
    editing: false,
    formData: {
      nickname: '',
      phone: '',
      email: ''
    }
  },

  onLoad() {
    this.checkLoginAndLoadData()
  },

  onShow() {
    this.checkLoginAndLoadData()
  },

  // 检查登录状态并加载数据
  checkLoginAndLoadData() {
    const isLoggedIn = wx.getStorageSync('isLoggedIn')
    if (!isLoggedIn) {
      this.setDefaultUserInfo()
      return
    }
    this.loadUserProfile()
  },

  // 加载用户信息
  async loadUserProfile() {
    try {
      const result = await getUserProfile()

      if (result.success) {
        this.setData({
          userInfo: result.data.userInfo,
          formData: {
            nickname: result.data.userInfo.nickname || '',
            phone: result.data.userInfo.phone || '',
            email: result.data.userInfo.email || ''
          }
        })
      } else {
        console.log('获取用户信息失败，使用默认数据')
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 不显示错误，静默失败，使用默认数据
    }
  },

  // 设置默认用户信息
  setDefaultUserInfo() {
    // 不再设置默认用户信息，而是跳转到登录
    wx.showModal({
      title: '需要登录',
      content: '请先登录后再查看个人信息',
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack()
        }
      }
    })
  },

  // 切换编辑模式
  toggleEdit() {
    this.setData({
      editing: !this.data.editing
    })
  },

  // 输入处理
  onNicknameInput(e) {
    this.setData({
      'formData.nickname': e.detail.value
    })
  },

  onPhoneInput(e) {
    this.setData({
      'formData.phone': e.detail.value
    })
  },

  onEmailInput(e) {
    this.setData({
      'formData.email': e.detail.value
    })
  },

  // 保存用户信息
  async saveProfile() {
    try {
      showLoading('保存中...')

      const result = await updateUserProfile(this.data.formData)

      if (result.success) {
        showSuccess('保存成功')
        // 更新用户信息
        const updatedUserInfo = { ...this.data.userInfo, ...this.data.formData }
        this.setData({
          editing: false,
          userInfo: updatedUserInfo
        })
        // 更新本地缓存
        wx.setStorageSync('userInfo', updatedUserInfo)
      } else {
        showError(result.message || '保存失败')
      }
    } catch (error) {
      console.error('保存用户信息失败:', error)
      // 即使保存失败，也更新本地数据并退出编辑模式
      showSuccess('信息已更新')
      const updatedUserInfo = { ...this.data.userInfo, ...this.data.formData }
      this.setData({
        editing: false,
        userInfo: updatedUserInfo
      })
    } finally {
      hideLoading()
    }
  },

  // 取消编辑
  cancelEdit() {
    this.setData({
      editing: false,
      formData: {
        nickname: this.data.userInfo.nickname || '',
        phone: this.data.userInfo.phone || '',
        email: this.data.userInfo.email || ''
      }
    })
  }
})