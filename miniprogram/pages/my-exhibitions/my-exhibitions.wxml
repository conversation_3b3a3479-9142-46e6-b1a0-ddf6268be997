<!--pages/my-exhibitions/my-exhibitions.wxml-->
<view class="page">
  <!-- 展会列表 -->
  <view class="content">
    <view
      wx:for="{{exhibitions}}"
      wx:key="id"
      class="exhibition-card"
      bind:tap="goToExhibitionDetail"
      data-id="{{item.id}}"
    >
      <view class="exhibition-header">
        <view class="exhibition-info">
          <view class="exhibition-name">{{item.name}}</view>
          <view class="exhibition-venue">{{item.venue ? item.venue.name : ''}}</view>
        </view>
      </view>

      <view class="exhibition-details">
        <view class="detail-item">
          <text class="icon">📍</text>
          <text>{{item.city}}</text>
        </view>
        <view class="detail-item">
          <text class="icon">📅</text>
          <text>{{item.startDate}} - {{item.endDate}}</text>
        </view>
        <view class="detail-item" wx:if="{{item.category}}">
          <text class="icon">🏷️</text>
          <text>{{item.category.name}}</text>
        </view>
      </view>

      <view class="exhibition-actions">
        <button
          class="cancel-btn"
          bind:tap="cancelExhibition"
          data-id="{{item.id}}"
          data-name="{{item.name}}"
        >
          取消参展
        </button>
      </view>
    </view>

    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading">
      <text>加载中...</text>
    </view>

    <!-- 空状态 -->
    <view wx:if="{{!loading && exhibitions.length === 0}}" class="empty-state">
      <text class="empty-icon">🏢</text>
      <view class="empty-text">暂无参展记录</view>
      <view class="empty-desc">快去参加一些展会吧</view>
      <button class="join-btn" bind:tap="goToJoinExhibition">
        参加展会
      </button>
    </view>

    <!-- 没有更多 -->
    <view wx:if="{{finished && exhibitions.length > 0}}" class="no-more">
      <text>没有更多了</text>
    </view>
  </view>
</view>