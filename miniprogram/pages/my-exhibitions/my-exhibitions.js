// pages/my-exhibitions/my-exhibitions.js
import {
  getMyExhibitions,
  cancelExhibition,
  showError,
  showSuccess,
  showLoading,
  hideLoading
} from '../../utils/api.js'

Page({
  data: {
    exhibitions: [],
    loading: false,
    finished: false,
    refreshing: false,
    page: 1,
    pageSize: 10
  },

  onLoad() {
    this.loadExhibitions(true)
  },

  onShow() {
    // 每次显示时刷新数据
    this.loadExhibitions(true)
  },

  onPullDownRefresh() {
    this.onRefresh()
  },

  onReachBottom() {
    this.loadMore()
  },

  // 加载展会列表
  async loadExhibitions(isRefresh = false) {
    if (this.data.loading) return

    try {
      if (isRefresh) {
        this.setData({
          page: 1,
          finished: false,
          loading: true,
          refreshing: true
        })
      } else {
        this.setData({ loading: true })
      }

      const params = {
        page: isRefresh ? 1 : this.data.page,
        pageSize: this.data.pageSize
      }

      const result = await getMyExhibitions(params)

      if (result.success) {
        const { list, totalPages } = result.data

        if (isRefresh) {
          this.setData({
            exhibitions: list
          })
        } else {
          this.setData({
            exhibitions: [...this.data.exhibitions, ...list]
          })
        }

        // 检查是否还有更多数据
        if (params.page >= totalPages) {
          this.setData({ finished: true })
        } else {
          this.setData({
            page: this.data.page + 1
          })
        }
      } else {
        // 如果获取失败，设置空数组，不显示错误
        this.setData({
          exhibitions: [],
          finished: true
        })
      }
    } catch (error) {
      console.error('获取我的展会失败:', error)
      // 设置空数组，不显示错误，确保页面能正常显示
      this.setData({
        exhibitions: [],
        finished: true
      })
    } finally {
      this.setData({
        loading: false,
        refreshing: false
      })
      wx.stopPullDownRefresh()
    }
  },

  // 下拉刷新
  async onRefresh() {
    await this.loadExhibitions(true)
  },

  // 上拉加载更多
  async loadMore() {
    if (this.data.finished || this.data.loading) return
    await this.loadExhibitions()
  },

  // 取消参展
  async cancelExhibition(e) {
    const { id, name } = e.currentTarget.dataset

    try {
      const result = await wx.showModal({
        title: '确认取消',
        content: `确定要取消参加"${name}"展会吗？`,
        confirmText: '确定取消',
        confirmColor: '#ee0a24'
      })

      if (result.confirm) {
        showLoading('取消中...')

        const cancelResult = await cancelExhibition(id)

        if (cancelResult.success) {
          showSuccess('取消成功')
          // 刷新列表
          this.loadExhibitions(true)
        } else {
          showError(cancelResult.message || '取消失败')
        }
      }
    } catch (error) {
      console.error('取消参展失败:', error)
      showError(error)
    } finally {
      hideLoading()
    }
  },

  // 跳转到展会详情
  goToExhibitionDetail(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/exhibition-detail/exhibition-detail?id=${id}`
    })
  },

  // 跳转到参加展会页面
  goToJoinExhibition() {
    wx.navigateTo({
      url: '/pages/join-exhibition/join-exhibition'
    })
  }
})