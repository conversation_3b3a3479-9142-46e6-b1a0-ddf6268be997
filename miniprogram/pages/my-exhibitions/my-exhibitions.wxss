/* pages/my-exhibitions/my-exhibitions.wxss */

/* 强制样式生效 */
page {
  background-color: #f5f5f5;
}

.page {
  background-color: #f5f5f5 !important;
  min-height: 100vh;
}

.content {
  padding: 20rpx;
  background: transparent;
}

.exhibition-card {
  background: white !important;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid #f0f0f0;
}

.exhibition-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.exhibition-info {
  flex: 1;
}

.exhibition-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.exhibition-venue {
  font-size: 26rpx;
  color: #666;
}

.exhibition-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: white;
}

.exhibition-status.upcoming {
  background: #1989fa;
}

.exhibition-status.ongoing {
  background: #07c160;
}

.exhibition-status.ended {
  background: #969799;
}

.exhibition-details {
  margin-bottom: 20rpx;
}

.detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
  font-size: 26rpx;
  color: #666;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.icon {
  margin-right: 10rpx;
  font-size: 24rpx;
}

.exhibition-actions {
  text-align: right;
  border-top: 1rpx solid #f0f0f0;
  padding-top: 20rpx;
}

.cancel-btn {
  background: #ee0a24;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
}

.loading {
  text-align: center;
  padding: 40rpx;
  color: #999;
}

.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
  background: white !important;
  margin: 20rpx;
  border-radius: 20rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  display: block;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.join-btn {
  background: #1989fa;
  color: white;
  border: none;
  border-radius: 10rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

.no-more {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 24rpx;
}