// pages/complete-profile/complete-profile.js
import {
  selectUserType,
  showError,
  showSuccess,
  showLoading,
  hideLoading
} from '../../utils/api.js'

Page({
  data: {
    userType: null,
    loading: false,
    showCityPicker: false,
    selectedCityIndex: 0,
    form: {
      companyName: '',
      contactPerson: '',
      contactPhone: '',
      city: '',
      specialties: ''
    },
    cityOptions: [
      '北京', '上海', '广州', '深圳', '杭州', '南京', '苏州', '成都', 
      '重庆', '武汉', '西安', '天津', '青岛', '大连', '厦门', '宁波',
      '无锡', '佛山', '东莞', '泉州', '温州', '嘉兴', '台州', '金华',
      '绍兴', '湖州', '丽水', '衢州', '舟山', '其他'
    ]
  },

  onLoad(options) {
    const userType = parseInt(options.userType)
    this.setData({ userType })
    
    // 设置导航栏标题
    const typeNames = {
      2: '设计公司信息',
      3: '工厂信息', 
      4: '综合商信息'
    }
    wx.setNavigationBarTitle({
      title: typeNames[userType] || '完善信息'
    })
  },

  // 输入处理
  onInput(e) {
    const { field } = e.currentTarget.dataset
    const { value } = e.detail
    
    this.setData({
      [`form.${field}`]: value
    })
  },

  // 显示城市选择器
  showCityPicker() {
    this.setData({ showCityPicker: true })
  },

  // 隐藏城市选择器
  hideCityPicker() {
    this.setData({ showCityPicker: false })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 空函数，阻止点击事件冒泡
  },

  // 选择城市
  selectCity(e) {
    const { index, city } = e.currentTarget.dataset
    this.setData({
      selectedCityIndex: index,
      'form.city': city
    })
  },

  // 确认城市选择
  confirmCity() {
    this.setData({ showCityPicker: false })
  },

  // 表单验证
  validateForm() {
    const { form, userType } = this.data
    
    if (!form.companyName.trim()) {
      showError('请输入公司名称')
      return false
    }
    
    if (!form.contactPerson.trim()) {
      showError('请输入联系人姓名')
      return false
    }
    
    if (!form.contactPhone.trim()) {
      showError('请输入联系电话')
      return false
    }
    
    // 简单的手机号验证
    const phoneRegex = /^1[3-9]\d{9}$/
    if (!phoneRegex.test(form.contactPhone)) {
      showError('请输入正确的手机号')
      return false
    }
    
    if (!form.city) {
      showError('请选择城市')
      return false
    }
    
    // 工厂和综合商需要填写专业特长
    if ((userType === 3 || userType === 4) && !form.specialties.trim()) {
      showError('请输入专业特长')
      return false
    }
    
    return true
  },

  // 提交表单
  async handleSubmit() {
    if (!this.validateForm()) {
      return
    }

    try {
      this.setData({ loading: true })
      showLoading('提交中...')

      const { form, userType } = this.data
      
      // 构建公司信息
      const companyInfo = {
        name: form.companyName.trim(),
        contactPerson: form.contactPerson.trim(),
        contactPhone: form.contactPhone.trim(),
        city: form.city,
        level: 1 // 默认等级
      }
      
      // 工厂和综合商需要专业特长
      if (userType === 3 || userType === 4) {
        companyInfo.specialties = form.specialties.trim()
      }

      const result = await selectUserType(userType, companyInfo)

      if (result.success) {
        // 更新本地用户信息
        wx.setStorageSync('userInfo', result.data.userInfo)
        
        showSuccess('注册完成')

        // 跳转到首页
        setTimeout(() => {
          wx.reLaunch({
            url: '/pages/home/<USER>'
          })
        }, 1500)
      } else {
        showError(result.message || '提交失败')
      }
    } catch (error) {
      console.error('提交失败:', error)
      showError('提交失败，请重试')
    } finally {
      this.setData({ loading: false })
      hideLoading()
    }
  }
})
