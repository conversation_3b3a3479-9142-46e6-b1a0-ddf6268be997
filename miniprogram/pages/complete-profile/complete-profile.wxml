<!--pages/complete-profile/complete-profile.wxml-->
<view class="page">
  <!-- 导航栏 -->
  <view class="nav-bar">
    <view class="nav-title">完善信息</view>
  </view>

  <view class="content">
    <!-- 表单 -->
    <view class="form-section">
      <view class="section-title">基本信息</view>
      
      <view class="form-item">
        <view class="label">公司名称 <text class="required">*</text></view>
        <input
          class="input"
          placeholder="请输入公司名称"
          value="{{form.companyName}}"
          bind:input="onInput"
          data-field="companyName"
        />
      </view>
      
      <view class="form-item">
        <view class="label">联系人 <text class="required">*</text></view>
        <input
          class="input"
          placeholder="请输入联系人姓名"
          value="{{form.contactPerson}}"
          bind:input="onInput"
          data-field="contactPerson"
        />
      </view>
      
      <view class="form-item">
        <view class="label">联系电话 <text class="required">*</text></view>
        <input
          class="input"
          placeholder="请输入联系电话"
          type="number"
          value="{{form.contactPhone}}"
          bind:input="onInput"
          data-field="contactPhone"
        />
      </view>
      
      <view class="form-item">
        <view class="label">所在城市 <text class="required">*</text></view>
        <view class="picker-field" bind:tap="showCityPicker">
          <text class="{{form.city ? 'selected' : 'placeholder'}}">
            {{form.city || '请选择城市'}}
          </text>
          <text class="arrow">></text>
        </view>
      </view>
      
      <!-- 工厂和综合商特有字段 -->
      <view wx:if="{{userType === 3 || userType === 4}}" class="form-item">
        <view class="label">专业特长 <text class="required">*</text></view>
        <textarea
          class="textarea"
          placeholder="请输入专业特长，如：钢结构、木结构、特装搭建等"
          value="{{form.specialties}}"
          bind:input="onInput"
          data-field="specialties"
        />
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <button
        class="submit-btn"
        loading="{{loading}}"
        bind:tap="handleSubmit"
      >
        完成注册
      </button>
    </view>
  </view>

  <!-- 城市选择器 -->
  <view wx:if="{{showCityPicker}}" class="picker-overlay" bind:tap="hideCityPicker">
    <view class="picker-container" catch:tap="stopPropagation">
      <view class="picker-header">
        <view class="picker-cancel" bind:tap="hideCityPicker">取消</view>
        <view class="picker-title">选择城市</view>
        <view class="picker-confirm" bind:tap="confirmCity">确定</view>
      </view>
      <scroll-view class="city-list" scroll-y>
        <view
          wx:for="{{cityOptions}}"
          wx:key="*this"
          class="city-item {{selectedCityIndex === index ? 'selected' : ''}}"
          bind:tap="selectCity"
          data-index="{{index}}"
          data-city="{{item}}"
        >
          {{item}}
        </view>
      </scroll-view>
    </view>
  </view>
</view>
