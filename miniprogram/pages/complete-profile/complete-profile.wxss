/* pages/complete-profile/complete-profile.wxss */
.page {
  min-height: 100vh;
  background: #f5f5f5;
}

.nav-bar {
  background: white;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #eee;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  color: #333;
}

.content {
  padding: 30rpx;
}

.form-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 40rpx;
}

.form-item {
  margin-bottom: 40rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.required {
  color: #ff4757;
}

.input, .textarea {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #eee;
  border-radius: 10rpx;
  font-size: 28rpx;
  background: #fafafa;
}

.input:focus, .textarea:focus {
  border-color: #1989fa;
  background: white;
}

.textarea {
  height: 120rpx;
  resize: none;
}

.picker-field {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  border: 2rpx solid #eee;
  border-radius: 10rpx;
  background: #fafafa;
}

.placeholder {
  color: #999;
}

.selected {
  color: #333;
}

.arrow {
  color: #999;
  font-size: 24rpx;
}

.submit-section {
  padding: 20rpx 0;
}

.submit-btn {
  background: linear-gradient(135deg, #1989fa 0%, #0066cc 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 30rpx;
  font-size: 32rpx;
  font-weight: bold;
  width: 100%;
}

.submit-btn[loading] {
  background: #999;
}

/* 城市选择器样式 */
.picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

.picker-container {
  width: 100%;
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.picker-cancel, .picker-confirm {
  font-size: 28rpx;
  color: #1989fa;
}

.picker-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.city-list {
  flex: 1;
  max-height: 60vh;
}

.city-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  font-size: 28rpx;
  color: #333;
}

.city-item.selected {
  background: #f0f8ff;
  color: #1989fa;
}

.city-item:last-child {
  border-bottom: none;
}
