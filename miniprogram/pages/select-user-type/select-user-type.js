// pages/select-user-type/select-user-type.js
import {
  selectUserType,
  showError,
  showSuccess,
  showLoading,
  hideLoading
} from '../../utils/api.js'

Page({
  data: {
    selectedType: '',
    loading: false,
    userTypeOptions: [
      {
        value: 1,
        title: '参展商',
        description: '我需要参加展会，寻找设计公司和工厂合作',
        icon: '🏢'
      },
      {
        value: 2,
        title: '设计公司',
        description: '我是设计公司，为参展商提供展台设计服务',
        icon: '🎨'
      },
      {
        value: 3,
        title: '工厂',
        description: '我是搭建工厂，为参展商提供展台搭建服务',
        icon: '🏭'
      },
      {
        value: 4,
        title: '综合商',
        description: '我既提供设计服务，也提供搭建服务',
        icon: '🔧'
      }
    ]
  },

  onLoad(options) {
    // 检查是否是强制选择身份（新用户）
    if (options.required === 'true') {
      wx.setNavigationBarTitle({
        title: '选择身份'
      })
    }
  },

  // 选择类型
  selectType(e) {
    const { type } = e.currentTarget.dataset
    this.setData({
      selectedType: type
    })
  },

  // 确认选择
  async handleConfirm() {
    if (!this.data.selectedType) {
      showError('请选择您的身份')
      return
    }

    try {
      this.setData({ loading: true })
      showLoading('设置中...')

      const result = await selectUserType(this.data.selectedType)

      if (result.success) {
        // 更新本地用户信息
        wx.setStorageSync('userInfo', result.data.userInfo)
        
        showSuccess('身份设置成功')

        // 根据身份类型决定下一步
        if (this.data.selectedType === 1) {
          // 参展商直接跳转到首页
          setTimeout(() => {
            wx.reLaunch({
              url: '/pages/home/<USER>'
            })
          }, 1500)
        } else {
          // 设计公司、工厂、综合商需要完善公司信息
          setTimeout(() => {
            wx.navigateTo({
              url: `/pages/complete-profile/complete-profile?userType=${this.data.selectedType}`
            })
          }, 1500)
        }
      } else {
        showError(result.message || '设置失败')
      }
    } catch (error) {
      console.error('选择身份失败:', error)
      showError('设置失败，请重试')
    } finally {
      this.setData({ loading: false })
      hideLoading()
    }
  }
})
