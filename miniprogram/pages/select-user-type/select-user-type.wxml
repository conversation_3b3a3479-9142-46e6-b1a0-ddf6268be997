<!--pages/select-user-type/select-user-type.wxml-->
<view class="page">
  <!-- 标题说明 -->
  <view class="header-section">
    <view class="logo">🎯</view>
    <view class="title">请选择您的身份</view>
    <view class="desc">选择最符合您需求的身份类型，以便为您提供更精准的服务</view>
  </view>

  <!-- 身份选择 -->
  <view class="type-options">
    <view
      wx:for="{{userTypeOptions}}"
      wx:key="value"
      class="type-option {{selectedType === item.value ? 'active' : ''}}"
      bind:tap="selectType"
      data-type="{{item.value}}"
    >
      <view class="option-icon">{{item.icon}}</view>
      <view class="option-content">
        <view class="option-title">{{item.title}}</view>
        <view class="option-desc">{{item.description}}</view>
      </view>
      <view class="option-check">
        <text class="check-icon">{{selectedType === item.value ? '✓' : '○'}}</text>
      </view>
    </view>
  </view>

  <!-- 确认按钮 -->
  <view class="action-section">
    <button
      class="confirm-btn"
      disabled="{{!selectedType}}"
      loading="{{loading}}"
      bind:tap="handleConfirm"
    >
      确认选择
    </button>
  </view>
</view>
