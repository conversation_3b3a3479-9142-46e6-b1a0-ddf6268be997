/* pages/select-user-type/select-user-type.wxss */
.page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 30rpx;
  display: flex;
  flex-direction: column;
}

.header-section {
  text-align: center;
  margin-bottom: 80rpx;
  color: white;
}

.logo {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.desc {
  font-size: 28rpx;
  opacity: 0.9;
  line-height: 1.5;
}

.type-options {
  flex: 1;
  margin-bottom: 60rpx;
}

.type-option {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.type-option.active {
  background: #f0f8ff;
  border: 4rpx solid #1989fa;
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 40rpx rgba(25, 137, 250, 0.2);
}

.option-icon {
  font-size: 60rpx;
  margin-right: 30rpx;
  width: 80rpx;
  text-align: center;
}

.option-content {
  flex: 1;
}

.option-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.option-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.option-check {
  margin-left: 20rpx;
}

.check-icon {
  font-size: 40rpx;
  color: #1989fa;
  font-weight: bold;
}

.type-option:not(.active) .check-icon {
  color: #ddd;
}

.action-section {
  padding-bottom: 40rpx;
}

.confirm-btn {
  background: linear-gradient(135deg, #1989fa 0%, #0066cc 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 30rpx;
  font-size: 36rpx;
  font-weight: bold;
  width: 100%;
  box-shadow: 0 8rpx 30rpx rgba(25, 137, 250, 0.3);
}

.confirm-btn[disabled] {
  background: #ccc;
  box-shadow: none;
}

.confirm-btn[loading] {
  background: #999;
}
