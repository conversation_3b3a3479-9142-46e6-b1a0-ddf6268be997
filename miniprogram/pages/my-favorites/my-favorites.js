// pages/my-favorites/my-favorites.js
import {
  getFavoriteList,
  removeFavorite,
  showError,
  showSuccess,
  showLoading,
  hideLoading,
  formatTime
} from '../../utils/api.js'

Page({
  data: {
    favorites: [],
    loading: false,
    finished: false,
    refreshing: false,
    page: 1,
    pageSize: 10
  },

  onLoad() {
    this.loadFavorites(true)
  },

  onShow() {
    this.loadFavorites(true)
  },

  onPullDownRefresh() {
    this.onRefresh()
  },

  onReachBottom() {
    this.loadMore()
  },

  // 加载收藏列表
  async loadFavorites(isRefresh = false) {
    if (this.data.loading) return

    try {
      if (isRefresh) {
        this.setData({
          page: 1,
          finished: false,
          loading: true,
          refreshing: true
        })
      } else {
        this.setData({ loading: true })
      }

      const params = {
        page: isRefresh ? 1 : this.data.page,
        pageSize: this.data.pageSize
      }

      const result = await getFavoriteList(params)

      if (result.success) {
        const { list, totalPages } = result.data

        if (isRefresh) {
          this.setData({
            favorites: list
          })
        } else {
          this.setData({
            favorites: [...this.data.favorites, ...list]
          })
        }

        if (params.page >= totalPages) {
          this.setData({ finished: true })
        } else {
          this.setData({
            page: this.data.page + 1
          })
        }
      } else {
        showError(result.message || '获取数据失败')
      }
    } catch (error) {
      console.error('获取收藏列表失败:', error)
      showError(error)
    } finally {
      this.setData({
        loading: false,
        refreshing: false
      })
      wx.stopPullDownRefresh()
    }
  },

  // 下拉刷新
  async onRefresh() {
    await this.loadFavorites(true)
  },

  // 上拉加载更多
  async loadMore() {
    if (this.data.finished || this.data.loading) return
    await this.loadFavorites()
  },

  // 取消收藏
  async removeFavorite(e) {
    const { id, name } = e.currentTarget.dataset

    try {
      const result = await wx.showModal({
        title: '确认取消',
        content: `确定要取消收藏"${name}"吗？`,
        confirmText: '确定取消',
        confirmColor: '#ee0a24'
      })

      if (result.confirm) {
        showLoading('取消中...')

        const removeResult = await removeFavorite(id)

        if (removeResult.success) {
          showSuccess('取消收藏成功')
          this.loadFavorites(true)
        } else {
          showError(removeResult.message || '取消收藏失败')
        }
      }
    } catch (error) {
      console.error('取消收藏失败:', error)
      showError(error)
    } finally {
      hideLoading()
    }
  },

  // 跳转到详情页
  goToDetail(e) {
    const { id, type } = e.currentTarget.dataset

    if (type === 'company') {
      wx.navigateTo({
        url: `/pages/company-detail/company-detail?id=${id}`
      })
    } else if (type === 'exhibition') {
      wx.navigateTo({
        url: `/pages/exhibition-detail/exhibition-detail?id=${id}`
      })
    }
  }
})