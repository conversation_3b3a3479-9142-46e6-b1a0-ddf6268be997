/* pages/my-favorites/my-favorites.wxss */
.page {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.content {
  padding: 20rpx;
}

.favorite-card {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.item-info {
  flex: 1;
}

.item-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.item-type {
  display: inline-block;
  padding: 4rpx 12rpx;
  background: #f0f0f0;
  color: #666;
  font-size: 22rpx;
  border-radius: 12rpx;
}

.remove-btn {
  background: #ee0a24;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
}

.item-details {
  margin-bottom: 20rpx;
}

.detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
  font-size: 26rpx;
  color: #666;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.icon {
  margin-right: 10rpx;
  font-size: 24rpx;
}

.favorite-time {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 15rpx;
  font-size: 24rpx;
  color: #999;
}

.loading {
  text-align: center;
  padding: 40rpx;
  color: #999;
}

.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  display: block;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #666;
}

.no-more {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 24rpx;
}