<!--pages/my-favorites/my-favorites.wxml-->
<wxs module="timeUtil" src="../../utils/time.wxs"></wxs>
<view class="page">
  <view class="content">
    <view
      wx:for="{{favorites}}"
      wx:key="id"
      class="favorite-card"
      bind:tap="goToDetail"
      data-id="{{item.id}}"
      data-type="{{item.type}}"
    >
      <view class="card-header">
        <view class="item-info">
          <view class="item-name">{{item.name}}</view>
          <view class="item-type">{{item.type === 'company' ? '企业' : '展会'}}</view>
        </view>
        <button
          class="remove-btn"
          bind:tap="removeFavorite"
          data-id="{{item.id}}"
          data-name="{{item.name}}"
        >
          取消收藏
        </button>
      </view>

      <view class="item-details">
        <view class="detail-item" wx:if="{{item.city}}">
          <text class="icon">📍</text>
          <text>{{item.city}}</text>
        </view>
        <view class="detail-item" wx:if="{{item.address}}">
          <text class="icon">🏢</text>
          <text>{{item.address}}</text>
        </view>
        <view class="detail-item" wx:if="{{item.startDate}}">
          <text class="icon">📅</text>
          <text>{{item.startDate}} - {{item.endDate}}</text>
        </view>
        <view class="detail-item" wx:if="{{item.category}}">
          <text class="icon">🏷️</text>
          <text>{{item.category}}</text>
        </view>
      </view>

      <view class="favorite-time">
        <text>收藏时间：{{timeUtil.formatTime(item.createdAt, 'datetime')}}</text>
      </view>
    </view>


    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading">
      <text>加载中...</text>
    </view>

    <!-- 空状态 -->
    <view wx:if="{{!loading && favorites.length === 0}}" class="empty-state">
      <text class="empty-icon">❤️</text>
      <view class="empty-text">暂无收藏</view>
      <view class="empty-desc">快去收藏一些感兴趣的内容吧</view>
    </view>

    <!-- 没有更多 -->
    <view wx:if="{{finished && favorites.length > 0}}" class="no-more">
      <text>没有更多了</text>
    </view>
  </view>
</view>