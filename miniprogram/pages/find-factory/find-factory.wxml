<!--pages/find-factory/find-factory.wxml-->
<view class="page">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">找工厂</text>
  </view>

  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-row">
      <view class="search-field-wrapper">
        <picker
          mode="selector"
          range="{{cityColumns}}"
          range-key="text"
          value="{{selectedCityIndex}}"
          bind:change="onCityChange"
        >
          <view class="search-field">
            <text class="label">城市</text>
            <text class="value {{searchForm.cityName ? '' : 'placeholder'}}">
              {{searchForm.cityName}}
            </text>
            <text class="arrow">></text>
          </view>
        </picker>
        <text wx:if="{{searchForm.cityName}}" class="clear-btn" bind:tap="clearCity">×</text>
      </view>

      <view class="search-field-wrapper">
        <picker
          mode="date"
          value="{{searchForm.dateValue}}"
          start="{{today}}"
          bind:change="onDateChange"
        >
          <view class="search-field">
            <text class="label">日期</text>
            <text class="value {{searchForm.dateName ? '' : 'placeholder'}}">
              {{searchForm.dateName}}
            </text>
            <text class="arrow">></text>
          </view>
        </picker>
        <text wx:if="{{searchForm.dateName}}" class="clear-btn" bind:tap="clearDate">×</text>
      </view>
    </view>

    <view class="search-row">
      <view class="search-field-wrapper">
        <picker
          mode="selector"
          range="{{venueColumns}}"
          range-key="text"
          value="{{selectedVenueIndex}}"
          bind:change="onVenueChange"
        >
          <view class="search-field">
            <text class="label">场馆</text>
            <text class="value {{searchForm.venueName ? '' : 'placeholder'}}">
              {{searchForm.venueName}}
            </text>
            <text class="arrow">></text>
          </view>
        </picker>
        <text wx:if="{{searchForm.venueName}}" class="clear-btn" bind:tap="clearVenue">×</text>
      </view>
    </view>
    
    <view class="search-row">
      <input 
        class="keyword-input"
        value="{{searchForm.keyword}}"
        placeholder="输入关键字"
        bindinput="onKeywordInput"
        bindconfirm="handleSearch"
      />
      <button class="search-button" bind:tap="handleSearch">搜索</button>
    </view>
  </view>

  <!-- 工厂列表 -->
  <view class="content">
    <view 
      wx:for="{{factories}}" 
      wx:key="id" 
      class="company-card"
      bind:tap="goToDetail"
      data-id="{{item.id}}"
    >
      <view class="company-header">
        <view class="company-info">
          <view class="company-name">{{item.name}}</view>
          <view class="company-type">
            {{item.type === 'comprehensive' ? '综合商' : '工厂'}}
          </view>
        </view>
        <view class="company-actions">
          <!-- 展会名称 -->
          <view class="exhibition-name" wx:if="{{item.exhibitionName}}">
            {{item.exhibitionName}}
          </view>
          <text
            class="favorite-icon {{item.isFavorited ? 'favorited' : ''}}"
            bind:tap="toggleFavorite"
            data-item="{{item}}"
          >
            {{item.isFavorited ? '⭐' : '☆'}}
          </text>
        </view>
      </view>
      
      <view class="company-details">
        <view class="detail-item" wx:if="{{item.city}}">
          <text class="icon">📍</text>
          <text>{{item.city}}</text>
        </view>
        <view class="detail-item" wx:if="{{item.contactPerson}}">
          <text class="icon">👤</text>
          <text>{{item.contactPerson}}</text>
        </view>
        <view class="detail-item" wx:if="{{item.contactPhone}}">
          <text class="icon">📞</text>
          <text>{{item.contactPhone}}</text>
        </view>
        
        <!-- 特长标签 -->
        <view class="company-tags" wx:if="{{item.specialties}}">
          <text 
            wx:for="{{item.specialties.split(',')}}" 
            wx:key="*this"
            wx:for-item="tag"
            class="tag"
          >
            {{tag.trim()}}
          </text>
        </view>
      </view>
      
      <view class="company-footer">
        <view class="rating">
          <text class="stars">
            <text wx:for="{{item.level || 1}}" wx:key="*this">★</text>
          </text>
          <text class="level">{{item.level || 1}}星工厂</text>
        </view>
        <view class="status {{item.isFull ? 'full' : 'available'}}">
          {{item.isFull ? '已满额' : '可预约'}}
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading">
      <text>加载中...</text>
    </view>

    <!-- 空状态 -->
    <view wx:if="{{!loading && factories.length === 0}}" class="empty-state">
      <text class="empty-icon">🔍</text>
      <view class="empty-text">
        {{searchForm.cityValue && searchForm.dateValue ? '暂无工厂' : '请选择城市和日期'}}
      </view>
      <view class="empty-desc">
        {{searchForm.cityValue && searchForm.dateValue ? '试试调整搜索条件' : '根据展会信息查找参展的工厂'}}
      </view>
    </view>

    <!-- 没有更多 -->
    <view wx:if="{{finished && factories.length > 0}}" class="no-more">
      <text>没有更多了</text>
    </view>
  </view>






</view>
