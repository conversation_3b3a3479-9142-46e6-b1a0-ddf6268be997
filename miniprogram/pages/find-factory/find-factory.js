// pages/find-factory/find-factory.js
import { 
  searchFactories,
  getBaseData,
  addFavorite,
  removeFavorite,
  checkFavoriteStatus,
  showError,
  showSuccess,
  showLoading,
  hideLoading
} from '../../utils/api.js'
import { getCityColumns } from '../../utils/cityManager.js'

Page({
  data: {
    // 搜索表单
    searchForm: {
      keyword: '',
      cityName: '',
      cityValue: '',
      dateName: '',
      dateValue: '',
      venueName: '',
      venueValue: '',
      page: 1,
      pageSize: 10
    },
    
    // 选择器数据
    cityColumns: [],
    venueColumns: [],
    
    // 选择器显示状态
    showCityPicker: false,
    showDatePicker: false,
    showVenuePicker: false,
    
    // 数据列表
    factories: [],
    
    // 加载状态
    loading: false,
    finished: false,
    refreshing: false
  },

  onLoad(options) {
    this.initData()
    this.handleRouteParams(options)
    // 获取所有场馆数据
    this.fetchAllVenues()
  },

  onShow() {
    // 检查收藏状态
    this.checkAllFavoriteStatus()

    // 检查是否有来自首页的搜索参数
    const app = getApp()
    if (app.globalData.searchParams) {
      this.handleGlobalSearchParams(app.globalData.searchParams)
      // 清除全局参数，避免重复使用
      app.globalData.searchParams = null
    }
  },

  onPullDownRefresh() {
    this.onRefresh()
  },

  onReachBottom() {
    this.loadMore()
  },

  // 初始化数据
  async initData() {
    try {
      // 使用城市管理器获取城市数据
      const cityColumns = await getCityColumns()
      this.setData({
        cityColumns: cityColumns
      })
    } catch (error) {
      console.error('初始化城市数据失败:', error)
      // 使用默认城市数据
      this.setData({
        cityColumns: [
          { text: '北京', value: '北京' },
          { text: '上海', value: '上海' },
          { text: '广州', value: '广州' },
          { text: '深圳', value: '深圳' },
          { text: '杭州', value: '杭州' }
        ]
      })
    }
  },

  // 处理路由参数
  handleRouteParams(options) {
    let shouldSearch = false
    const updates = {}

    if (options.city) {
      const city = this.data.cityColumns.find(c => c.value === options.city)
      if (city) {
        updates['searchForm.cityName'] = city.text
        updates['searchForm.cityValue'] = city.value
        this.fetchVenues(city.value)
        shouldSearch = true
      }
    }

    if (options.date) {
      const date = new Date(options.date)
      updates['searchForm.dateName'] = this.formatDate(date)
      updates['searchForm.dateValue'] = options.date
      shouldSearch = true
    }

    if (options.venueId) {
      // 场馆信息需要等API返回后再设置
      setTimeout(() => {
        const venue = this.data.venueColumns.find(v => v.value == options.venueId)
        if (venue) {
          this.setData({
            'searchForm.venueName': venue.text,
            'searchForm.venueValue': venue.value
          })
          
          // 如果有完整的搜索条件，执行搜索
          if (this.data.searchForm.cityValue && this.data.searchForm.dateValue) {
            this.fetchFactories(true)
          }
        }
      }, 1000)
      shouldSearch = true
    }

    if (Object.keys(updates).length > 0) {
      this.setData(updates)
    }

    // 只有在有完整搜索参数时才执行搜索
    if (shouldSearch && this.data.searchForm.cityValue && this.data.searchForm.dateValue && !options.venueId) {
      this.fetchFactories(true)
    } else if (this.data.searchForm.cityValue && this.data.searchForm.dateValue) {
      // 有城市和日期参数时才加载数据
      console.log('有搜索条件，加载工厂数据')
      this.fetchFactories(true)
    } else {
      // 没有必要的搜索参数，显示提示
      console.log('缺少必要搜索条件，等待用户输入')
      this.setData({
        factories: [],
        finished: true
      })
    }
  },

  // 获取工厂列表
  async fetchFactories(isRefresh = false) {
    if (this.data.loading) return

    // 验证必填参数
    if (!this.data.searchForm.cityValue || !this.data.searchForm.dateValue) {
      if (isRefresh) {
        wx.showToast({
          title: '请选择城市和日期',
          icon: 'none'
        })
        this.setData({
          factories: [],
          finished: true,
          loading: false
        })
      }
      return
    }

    try {
      if (isRefresh) {
        this.setData({
          'searchForm.page': 1,
          finished: false,
          loading: true
        })
      } else {
        this.setData({ loading: true })
      }

      const params = {
        keyword: this.data.searchForm.keyword,
        city: this.data.searchForm.cityValue,
        date: this.data.searchForm.dateValue,
        venueId: this.data.searchForm.venueValue,
        page: this.data.searchForm.page,
        pageSize: this.data.searchForm.pageSize
      }

      console.log('搜索工厂参数:', params)
      const response = await searchFactories(params)
      console.log('搜索工厂响应:', response)

      if (response.success) {
        const { list, totalPages } = response.data
        console.log('工厂列表数据:', list, '总页数:', totalPages)

        // 先设置默认收藏状态，然后异步检查
        list.forEach(factory => {
          factory.isFavorited = false
          factory.favoriteId = null
        })

        // 异步检查收藏状态，不阻塞页面渲染
        setTimeout(() => {
          this.checkFavoriteStatusAsync(list)
        }, 100)

        if (isRefresh) {
          console.log('刷新模式，设置新数据:', list)
          this.setData({
            factories: list
          })
        } else {
          console.log('追加模式，当前数据:', this.data.factories, '新数据:', list)
          this.setData({
            factories: [...this.data.factories, ...list]
          })
        }

        console.log('数据设置完成，当前factories:', this.data.factories)

        // 检查是否还有更多数据
        if (this.data.searchForm.page >= totalPages) {
          this.setData({ finished: true })
        } else {
          this.setData({
            'searchForm.page': this.data.searchForm.page + 1
          })
        }
      } else {
        // 搜索失败时，如果是刷新模式，清空列表
        if (isRefresh) {
          this.setData({
            factories: [],
            finished: true
          })
        }
      }
    } catch (error) {
      console.error('获取工厂列表失败:', error)
      showError(error)
      
      if (error.message && error.message.includes('429')) {
        this.setData({ finished: true })
      }
    } finally {
      this.setData({ 
        loading: false,
        refreshing: false
      })
      wx.stopPullDownRefresh()
    }
  },

  // 下拉刷新
  async onRefresh() {
    this.setData({ refreshing: true })
    await this.fetchFactories(true)
  },

  // 上拉加载更多
  async loadMore() {
    if (this.data.finished || this.data.loading) return
    await this.fetchFactories()
  },

  // 获取所有场馆列表
  async fetchAllVenues() {
    try {
      const result = await getBaseData({ type: 'venues' })
      if (result.success) {
        this.setData({
          venueColumns: result.data.map(venue => ({
            text: venue.name,
            value: venue.id
          }))
        })
      }
    } catch (error) {
      console.error('获取场馆失败:', error)
    }
  },

  // 获取场馆列表
  async fetchVenues(city) {
    try {
      const result = await getBaseData({ type: 'venues' })
      if (result.success) {
        // 如果没有选择城市，显示所有场馆；否则按城市过滤
        const filteredVenues = city ?
          result.data.filter(venue => venue.city === city) :
          result.data
        this.setData({
          venueColumns: filteredVenues.map(venue => ({
            text: venue.name,
            value: venue.id
          }))
        })
      }
    } catch (error) {
      console.error('获取场馆列表失败:', error)
      this.setData({ venueColumns: [] })
    }
  },

  // 选择器变化事件
  onCityChange(e) {
    const index = e.detail.value
    const selectedCity = this.data.cityColumns[index]

    if (selectedCity) {
      this.setData({
        'searchForm.cityName': selectedCity.text,
        'searchForm.cityValue': selectedCity.value,
        selectedCityIndex: index,
        // 清空后续选择
        'searchForm.dateName': '',
        'searchForm.dateValue': '',
        'searchForm.venueName': '',
        'searchForm.venueValue': '',
        selectedVenueIndex: 0
      })

      // 根据选择的城市获取场馆列表
      this.fetchVenues(selectedCity.value)

      // 如果有完整搜索条件则搜索
      if (this.data.searchForm.dateValue && this.data.searchForm.venueValue) {
        this.fetchFactories(true)
      }
    }
  },

  onVenueChange(e) {
    const index = e.detail.value
    const selectedVenue = this.data.venueColumns[index]

    if (selectedVenue) {
      this.setData({
        'searchForm.venueName': selectedVenue.text,
        'searchForm.venueValue': selectedVenue.value,
        selectedVenueIndex: index
      })

      // 有了完整的搜索条件，执行搜索
      if (this.data.searchForm.cityValue && this.data.searchForm.dateValue) {
        this.fetchFactories(true)
      }
    }
  },

  // 选择器确认事件
  onCityConfirm() {
    // 原生picker组件会自动处理选择，这里只需要关闭弹窗
    this.setData({
      showCityPicker: false
    })
  },

  onDateChange(e) {
    const { value } = e.detail
    const date = new Date(value)

    this.setData({
      'searchForm.dateName': this.formatDate(date),
      'searchForm.dateValue': value,
      // 清空场馆选择
      'searchForm.venueName': '',
      'searchForm.venueValue': ''
    })

    // 如果有完整搜索条件则搜索
    if (this.data.searchForm.cityValue && this.data.searchForm.venueValue) {
      this.fetchFactories(true)
    }
  },









  // 关键字输入处理
  onKeywordInput(e) {
    this.setData({
      'searchForm.keyword': e.detail.value
    })
    
    // 延迟搜索，避免频繁请求
    clearTimeout(this.searchTimer)
    this.searchTimer = setTimeout(() => {
      this.fetchFactories(true)
    }, 500)
  },

  // 搜索按钮处理
  handleSearch() {
    this.fetchFactories(true)
  },

  // 切换收藏状态
  async toggleFavorite(e) {
    const { item } = e.currentTarget.dataset
    
    try {
      if (item.isFavorited) {
        await removeFavorite(item.favoriteId)
        item.isFavorited = false
        showSuccess('取消收藏成功')
      } else {
        const result = await addFavorite(3, item.id)
        if (result.success) {
          item.isFavorited = true
          item.favoriteId = result.data.favoriteId
          showSuccess('收藏成功')
        }
      }
      
      // 更新列表
      this.setData({
        factories: this.data.factories
      })
    } catch (error) {
      showError(error)
    }
  },

  // 异步检查收藏状态
  async checkFavoriteStatusAsync(factories) {
    try {
      for (const factory of factories) {
        try {
          const result = await checkFavoriteStatus(3, factory.id)
          if (result.success) {
            factory.isFavorited = result.data.isFavorited
            factory.favoriteId = result.data.favoriteId
          }
        } catch (error) {
          // 忽略单个检查错误
        }
      }
      // 批量更新UI
      this.setData({
        factories: this.data.factories
      })
    } catch (error) {
      console.error('检查收藏状态失败:', error)
    }
  },

  // 检查所有收藏状态
  async checkAllFavoriteStatus() {
    if (this.data.factories.length > 0) {
      this.checkFavoriteStatusAsync(this.data.factories)
    }
  },

  // 跳转到详情页
  goToDetail(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/company-detail/company-detail?type=factory&id=${id}`
    })
  },

  // 处理来自首页的搜索参数
  handleGlobalSearchParams(params) {
    console.log('接收到搜索参数:', params)
    const updates = {}

    if (params.city) {
      const city = this.data.cityColumns.find(c => c.value === params.city)
      if (city) {
        updates['searchForm.cityName'] = params.cityName || city.text
        updates['searchForm.cityValue'] = params.city
        updates.selectedCityIndex = this.data.cityColumns.indexOf(city)
      }
    }

    if (params.date) {
      updates['searchForm.dateName'] = params.dateName
      updates['searchForm.dateValue'] = params.date
    }

    if (params.venueId) {
      // 如果场馆数据还没加载完成，等待一下
      if (this.data.venueColumns.length === 0) {
        setTimeout(() => {
          this.handleGlobalSearchParams(params)
        }, 200)
        return
      }

      const venue = this.data.venueColumns.find(v => v.value === params.venueId)
      if (venue) {
        updates['searchForm.venueName'] = params.venueName || venue.text
        updates['searchForm.venueValue'] = params.venueId
        updates.selectedVenueIndex = this.data.venueColumns.indexOf(venue)
      } else {
        // 如果找不到场馆，直接使用传递的名称和值
        updates['searchForm.venueName'] = params.venueName
        updates['searchForm.venueValue'] = params.venueId
      }
    }

    console.log('更新数据:', updates)
    if (Object.keys(updates).length > 0) {
      this.setData(updates)
      // 执行搜索
      setTimeout(() => {
        this.fetchFactories(true)
      }, 300)
    }
  },

  // 工具方法
  formatDate(date) {
    return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
  },

  // 清除按钮事件
  clearCity(e) {
    this.setData({
      'searchForm.cityName': '',
      'searchForm.cityValue': '',
      selectedCityIndex: 0,
      // 清空后续选择
      'searchForm.dateName': '',
      'searchForm.dateValue': '',
      'searchForm.venueName': '',
      'searchForm.venueValue': '',
      selectedVenueIndex: 0
    })
    // 重新加载所有场馆
    this.fetchVenues('')
    // 重新搜索
    this.fetchFactories(true)
  },

  clearDate(e) {
    this.setData({
      'searchForm.dateName': '',
      'searchForm.dateValue': '',
      // 清空后续选择
      'searchForm.venueName': '',
      'searchForm.venueValue': '',
      selectedVenueIndex: 0
    })
    // 重新搜索
    this.fetchFactories(true)
  },

  clearVenue(e) {
    this.setData({
      'searchForm.venueName': '',
      'searchForm.venueValue': '',
      selectedVenueIndex: 0
    })
    // 重新搜索
    this.fetchFactories(true)
  },

  // 选择器取消事件
  onPickerCancel() {
    this.setData({
      showCityPicker: false,
      showDatePicker: false,
      showVenuePicker: false
    })
  }
})
