# 微信小程序云开发测试指南

## 🚀 快速开始

### 1. 环境准备
- ✅ 微信开发者工具（最新版本）
- ✅ 微信小程序账号（已配置云开发）
- ✅ 云开发环境ID：`cloud1-3g3fj8sde212780f`

### 2. 项目导入
1. 打开微信开发者工具
2. 导入项目，选择 `miniprogram` 目录
3. 填入AppID：`wxaa87bd18d1cf1d1e`
4. 确认云开发环境已正确配置

## 🔧 云函数部署

### 部署步骤：
1. **右键 `cloudfunctions/login`** → 上传并部署：云端安装依赖
2. **右键 `cloudfunctions/getUserProfile`** → 上传并部署：云端安装依赖
3. **右键 `cloudfunctions/updateUserProfile`** → 上传并部署：云端安装依赖

### 验证部署：
- 在云开发控制台查看云函数列表
- 确认三个云函数都显示"部署成功"

## 📊 数据库配置

### 创建集合：
1. 进入云开发控制台 → 数据库
2. 创建集合：`users`
3. 设置权限：仅创建者可读写

### 数据库权限设置：
```json
{
  "read": "auth.openid == resource.openid",
  "write": "auth.openid == resource.openid"
}
```

## 🧪 功能测试

### 测试1：用户登录
1. **操作**：点击"微信授权登录"按钮
2. **预期结果**：
   - 显示"登录成功"或"注册成功"提示
   - 页面显示用户信息区域
   - 显示"个人信息"和"退出登录"按钮

### 测试2：查看个人信息
1. **前提**：已完成登录
2. **操作**：点击"个人信息"按钮
3. **预期结果**：
   - 跳转到个人信息页面
   - 显示用户基本信息（昵称、手机号、邮箱等）
   - 显示注册时间和登录次数

### 测试3：编辑个人信息
1. **前提**：在个人信息页面
2. **操作**：
   - 点击"编辑信息"按钮
   - 修改昵称、手机号、邮箱
   - 点击"保存"按钮
3. **预期结果**：
   - 显示"保存成功"提示
   - 页面显示更新后的信息
   - 数据库中的记录已更新

### 测试4：数据持久化
1. **操作**：
   - 完成登录和信息编辑
   - 关闭小程序
   - 重新打开小程序
2. **预期结果**：
   - 用户仍处于登录状态
   - 个人信息保持最新状态

### 测试5：退出登录
1. **操作**：
   - 点击"退出登录"按钮
   - 确认退出
2. **预期结果**：
   - 显示"已退出登录"提示
   - 返回到登录页面
   - 本地存储已清除

## 🐛 常见问题排查

### 问题1：云函数调用失败
**症状**：登录时显示"登录失败"
**排查步骤**：
1. 检查云函数是否正确部署
2. 查看开发者工具控制台错误信息
3. 检查云开发环境ID是否正确

### 问题2：数据库权限错误
**症状**：显示"权限不足"错误
**解决方案**：
1. 检查数据库集合权限设置
2. 确认用户已正确登录
3. 重新设置数据库权限规则

### 问题3：页面跳转失败
**症状**：点击按钮无反应
**排查步骤**：
1. 检查页面路径是否正确
2. 查看控制台是否有JavaScript错误
3. 确认页面已在app.json中注册

## 📱 真机测试

### 预览测试：
1. 点击开发者工具的"预览"按钮
2. 用微信扫描二维码
3. 在真机上测试所有功能

### 注意事项：
- 真机测试时网络环境可能影响云函数调用速度
- 确保手机微信版本支持小程序云开发
- 测试时注意观察网络请求是否正常

## 📈 性能监控

### 云函数监控：
1. 进入云开发控制台 → 云函数
2. 查看各函数的调用次数、耗时、错误率
3. 关注异常调用和性能瓶颈

### 数据库监控：
1. 查看数据库读写次数
2. 监控存储空间使用情况
3. 关注慢查询和错误操作

## ✅ 测试检查清单

- [ ] 云函数部署成功
- [ ] 数据库集合创建完成
- [ ] 用户登录功能正常
- [ ] 个人信息页面显示正确
- [ ] 信息编辑功能正常
- [ ] 数据持久化正常
- [ ] 退出登录功能正常
- [ ] 真机测试通过
- [ ] 性能监控正常

## 🎯 下一步

测试通过后，可以：
1. 添加更多业务功能
2. 优化用户体验
3. 准备正式发布

---

**测试环境**：微信开发者工具 + 真机预览
**云开发环境**：cloud1-3g3fj8sde212780f
**更新时间**：2024年12月
