{"libVersion": "3.8.8", "projectname": "miniprogram", "setting": {"urlCheck": true, "coverView": true, "lazyloadPlaceholderEnable": false, "skylineRenderEnable": false, "preloadBackgroundData": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "compileHotReLoad": true}, "condition": {"miniprogram": {"list": [{"name": "初始化数据", "pathName": "pages/init-data/init-data", "query": "", "scene": null, "launchMode": "default"}]}}}