# 搜索逻辑修改测试说明

## 修改内容总结

### 1. 云函数修改 (miniprogram/cloudfunctions/searchCompanies/index.js)

#### 主要变更：
- **必填参数验证**：城市(city)和日期(date)现在是必填参数
- **多展会支持**：支持同一场馆同期多个展会的情况
- **展会名称返回**：确保每个公司都能正确关联到对应的展会名称

#### 搜索逻辑：
1. 验证城市和日期必填
2. 根据城市、日期查找展会（场馆可选）
3. 查找参展信息表中的参展公司
4. 返回结果包含展会名称

### 2. 前端页面修改

#### 找设计页面 (miniprogram/pages/find-design/find-design.js)
- 添加城市和日期必填验证
- 修改初始化逻辑，没有必要参数时不自动加载数据

#### 找工厂页面 (miniprogram/pages/find-factory/find-factory.js)
- 添加城市和日期必填验证
- 修改初始化逻辑，没有必要参数时不自动加载数据

#### 主页搜索 (miniprogram/pages/home/<USER>
- 修改搜索验证：城市和日期必填，场馆可选

### 3. 页面模板修改

#### 空状态提示优化
- 根据是否有搜索条件显示不同的提示信息
- 引导用户输入必要的搜索条件

## 测试要点

### 1. 必填参数验证
- [ ] 只选择城市，不选择日期：应显示"请选择城市和日期"
- [ ] 只选择日期，不选择城市：应显示"请选择城市和日期"
- [ ] 城市和日期都选择：应正常搜索

### 2. 场馆参数（可选）
- [ ] 不选择场馆：应搜索该城市该日期的所有展会
- [ ] 选择场馆：应只搜索该场馆的展会

### 3. 展会名称显示
- [ ] 搜索结果中每个公司卡片右上角应显示展会名称
- [ ] 同一场馆多个展会时，应显示对应的展会名称

### 4. 搜索结果
- [ ] 只返回参展的公司（基于参展信息表）
- [ ] 结果按级别和创建时间排序
- [ ] 分页功能正常

### 5. 用户体验
- [ ] 页面初始状态友好提示
- [ ] 搜索失败时的错误提示
- [ ] 无结果时的空状态提示

## 数据库依赖

确保以下数据表有测试数据：
1. `exhibitions` - 展会信息表
2. `exhibition_participants` - 参展信息表
3. `factories` - 工厂信息表
4. `design_companies` - 设计公司信息表
5. `venues` - 场馆信息表

## 部署说明

1. 上传云函数：`searchCompanies`
2. 更新小程序页面代码
3. 测试各种搜索场景
