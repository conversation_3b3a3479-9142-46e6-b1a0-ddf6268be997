// 云函数：更新用户信息
const cloud = require('wx-server-sdk')

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 更新用户信息云函数
 * @param {Object} event - 云函数入参
 * @param {string} event.nickname - 用户昵称
 * @param {string} event.avatar - 用户头像
 * @param {string} event.phone - 用户手机号
 * @param {string} event.email - 用户邮箱
 * @param {Object} context - 云函数上下文
 */
exports.main = async (event, context) => {
  console.log('更新用户信息云函数被调用，参数:', event)
  
  try {
    // 获取微信上下文信息
    const wxContext = cloud.getWXContext()
    const { OPENID } = wxContext
    
    if (!OPENID) {
      return {
        success: false,
        code: 401,
        message: '用户未登录'
      }
    }
    
    // 提取要更新的字段
    const { nickname, avatar, phone, email, userType } = event
    const updateData = {
      updatedAt: new Date()
    }

    // 只更新提供的字段
    if (nickname !== undefined) updateData.nickname = nickname
    if (avatar !== undefined) updateData.avatar = avatar
    if (phone !== undefined) updateData.phone = phone
    if (email !== undefined) updateData.email = email
    if (userType !== undefined) updateData.user_type = userType
    
    console.log('准备更新的数据:', updateData)
    
    // 更新用户信息
    const userCollection = db.collection('users')
    const updateResult = await userCollection.where({
      _openid: OPENID
    }).update({
      data: updateData
    })
    
    console.log('更新结果:', updateResult)
    
    if (updateResult.stats.updated === 0) {
      return {
        success: false,
        code: 404,
        message: '用户不存在'
      }
    }
    
    // 获取更新后的用户信息
    const { data: users } = await userCollection.where({
      _openid: OPENID
    }).get()
    
    const user = users[0]
    
    const result = {
      success: true,
      code: 200,
      message: '更新用户信息成功',
      data: {
        userInfo: {
          id: user._id,
          openid: user._openid,
          nickname: user.nickname,
          avatar: user.avatar,
          phone: user.phone,
          email: user.email,
          userType: user.user_type,
          status: user.status,
          loginCount: user.loginCount,
          lastLoginAt: user.lastLoginAt,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt
        }
      }
    }
    
    console.log('更新用户信息成功:', result)
    return result
    
  } catch (error) {
    console.error('更新用户信息失败:', error)
    
    return {
      success: false,
      code: 500,
      message: '更新用户信息失败：' + error.message,
      error: error.toString()
    }
  }
}
