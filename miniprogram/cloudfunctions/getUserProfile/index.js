// 云函数：获取用户信息
const cloud = require('wx-server-sdk')

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 获取用户信息云函数
 * @param {Object} event - 云函数入参
 * @param {Object} context - 云函数上下文
 */
exports.main = async (event, context) => {
  console.log('获取用户信息云函数被调用')
  
  try {
    // 获取微信上下文信息
    const wxContext = cloud.getWXContext()
    const { OPENID } = wxContext
    
    if (!OPENID) {
      return {
        success: false,
        code: 401,
        message: '用户未登录'
      }
    }
    
    // 查询用户信息
    const userCollection = db.collection('users')
    const { data: users } = await userCollection.where({
      _openid: OPENID
    }).get()
    
    if (users.length === 0) {
      return {
        success: false,
        code: 404,
        message: '用户不存在'
      }
    }
    
    const user = users[0]
    
    // 返回用户信息（不包含敏感信息）
    const result = {
      success: true,
      code: 200,
      message: '获取用户信息成功',
      data: {
        userInfo: {
          id: user._id,
          openid: user._openid,
          nickname: user.nickname,
          avatar: user.avatar,
          phone: user.phone,
          email: user.email,
          userType: user.user_type,
          status: user.status,
          loginCount: user.loginCount,
          lastLoginAt: user.lastLoginAt,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt
        }
      }
    }
    
    console.log('获取用户信息成功:', result)
    return result
    
  } catch (error) {
    console.error('获取用户信息失败:', error)
    
    return {
      success: false,
      code: 500,
      message: '获取用户信息失败：' + error.message,
      error: error.toString()
    }
  }
}
