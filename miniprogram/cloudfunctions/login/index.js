// 云函数：用户登录
const cloud = require('wx-server-sdk')

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 微信小程序登录云函数
 * @param {Object} event - 云函数入参
 * @param {string} event.code - 微信登录凭证
 * @param {Object} context - 云函数上下文
 */
exports.main = async (event, context) => {
  console.log('登录云函数被调用，参数:', event)
  
  try {
    // 获取微信上下文信息
    const wxContext = cloud.getWXContext()
    const { OPENID, APPID, UNIONID } = wxContext
    
    console.log('微信上下文:', { OPENID, APPID, UNIONID })
    
    if (!OPENID) {
      return {
        success: false,
        code: 400,
        message: '获取用户身份失败'
      }
    }
    
    // 查询用户是否已存在
    const userCollection = db.collection('users')
    const { data: existingUsers } = await userCollection.where({
      _openid: OPENID
    }).get()
    
    let user
    const now = new Date()
    
    if (existingUsers.length === 0) {
      // 创建新用户
      console.log('创建新用户:', OPENID)

      const newUser = {
        _openid: OPENID,
        appid: APPID,
        unionid: UNIONID || null,
        nickname: '',
        avatar: '',
        phone: '',
        email: '',
        user_type: null, // 新用户暂不设置身份，需要用户选择
        status: 'active',
        loginCount: 1,
        lastLoginAt: now,
        createdAt: now,
        updatedAt: now,
        isProfileCompleted: false // 标记用户信息是否完善
      }

      const { _id } = await userCollection.add({
        data: newUser
      })

      user = { _id, ...newUser }
      console.log('新用户创建成功:', user)
      
    } else {
      // 更新现有用户的登录信息
      user = existingUsers[0]
      console.log('用户已存在，更新登录信息:', user._id)
      
      await userCollection.doc(user._id).update({
        data: {
          loginCount: db.command.inc(1),
          lastLoginAt: now,
          updatedAt: now
        }
      })
      
      // 更新本地用户对象
      user.loginCount = (user.loginCount || 0) + 1
      user.lastLoginAt = now
      user.updatedAt = now
    }
    
    // 返回登录成功结果
    const result = {
      success: true,
      code: 200,
      message: '登录成功',
      data: {
        userInfo: {
          id: user._id,
          openid: user._openid,
          nickname: user.nickname,
          avatar: user.avatar,
          phone: user.phone,
          email: user.email,
          userType: user.user_type,
          status: user.status,
          loginCount: user.loginCount,
          lastLoginAt: user.lastLoginAt,
          createdAt: user.createdAt
        },
        token: OPENID, // 在云开发中，可以直接使用 openid 作为身份标识
        isNewUser: existingUsers.length === 0,
        needSelectUserType: existingUsers.length === 0 || !user.user_type, // 是否需要选择身份
        isProfileCompleted: user.isProfileCompleted || false // 用户信息是否完善
      }
    }
    
    console.log('登录成功，返回结果:', result)
    return result
    
  } catch (error) {
    console.error('登录失败:', error)
    
    return {
      success: false,
      code: 500,
      message: '登录失败：' + error.message,
      error: error.toString()
    }
  }
}
