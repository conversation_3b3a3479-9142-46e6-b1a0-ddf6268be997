// 云函数：初始化城市数据
const cloud = require('wx-server-sdk')

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 初始化城市数据云函数
 * @param {Object} event - 云函数入参
 * @param {string} event.action - 操作类型：'init'初始化, 'get'获取
 */
exports.main = async (event, context) => {
  console.log('初始化城市数据云函数被调用，参数:', event)
  
  try {
    const { action = 'get' } = event
    
    switch (action) {
      case 'init':
        return await initCitiesData()
      case 'get':
        return await getCitiesData()
      default:
        return {
          success: false,
          message: '无效的操作类型'
        }
    }
  } catch (error) {
    console.error('城市数据操作失败:', error)
    return {
      success: false,
      message: error.message || '操作失败'
    }
  }
}

/**
 * 初始化城市数据
 */
async function initCitiesData() {
  try {
    const citiesCollection = db.collection('cities')
    
    // 先清空现有数据
    const { data: existingCities } = await citiesCollection.get()
    if (existingCities.length > 0) {
      // 批量删除现有数据
      const deletePromises = existingCities.map(city => 
        citiesCollection.doc(city._id).remove()
      )
      await Promise.all(deletePromises)
    }
    
    // 城市数据
    const citiesData = [
      {
        name: '北京',
        code: 'beijing',
        province: '北京市',
        level: 1, // 一线城市
        sort_order: 1,
        is_hot: 1,
        is_deleted: 0,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '上海',
        code: 'shanghai',
        province: '上海市',
        level: 1,
        sort_order: 2,
        is_hot: 1,
        is_deleted: 0,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '广州',
        code: 'guangzhou',
        province: '广东省',
        level: 1,
        sort_order: 3,
        is_hot: 1,
        is_deleted: 0,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '深圳',
        code: 'shenzhen',
        province: '广东省',
        level: 1,
        sort_order: 4,
        is_hot: 1,
        is_deleted: 0,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '杭州',
        code: 'hangzhou',
        province: '浙江省',
        level: 2, // 新一线城市
        sort_order: 5,
        is_hot: 1,
        is_deleted: 0,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '南京',
        code: 'nanjing',
        province: '江苏省',
        level: 2,
        sort_order: 6,
        is_hot: 1,
        is_deleted: 0,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '苏州',
        code: 'suzhou',
        province: '江苏省',
        level: 2,
        sort_order: 7,
        is_hot: 1,
        is_deleted: 0,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '成都',
        code: 'chengdu',
        province: '四川省',
        level: 2,
        sort_order: 8,
        is_hot: 1,
        is_deleted: 0,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '武汉',
        code: 'wuhan',
        province: '湖北省',
        level: 2,
        sort_order: 9,
        is_hot: 1,
        is_deleted: 0,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '西安',
        code: 'xian',
        province: '陕西省',
        level: 2,
        sort_order: 10,
        is_hot: 1,
        is_deleted: 0,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '重庆',
        code: 'chongqing',
        province: '重庆市',
        level: 2,
        sort_order: 11,
        is_hot: 1,
        is_deleted: 0,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '天津',
        code: 'tianjin',
        province: '天津市',
        level: 2,
        sort_order: 12,
        is_hot: 1,
        is_deleted: 0,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '青岛',
        code: 'qingdao',
        province: '山东省',
        level: 2,
        sort_order: 13,
        is_hot: 0,
        is_deleted: 0,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '大连',
        code: 'dalian',
        province: '辽宁省',
        level: 2,
        sort_order: 14,
        is_hot: 0,
        is_deleted: 0,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '厦门',
        code: 'xiamen',
        province: '福建省',
        level: 2,
        sort_order: 15,
        is_hot: 0,
        is_deleted: 0,
        created_at: new Date(),
        updated_at: new Date()
      }
    ]
    
    // 批量插入城市数据
    const insertPromises = citiesData.map(city => 
      citiesCollection.add({ data: city })
    )
    await Promise.all(insertPromises)
    
    return {
      success: true,
      message: `成功初始化 ${citiesData.length} 个城市数据`,
      data: {
        count: citiesData.length
      }
    }
  } catch (error) {
    console.error('初始化城市数据失败:', error)
    throw error
  }
}

/**
 * 获取城市数据
 */
async function getCitiesData() {
  try {
    const { data: cities } = await db.collection('cities')
      .where({
        is_deleted: 0
      })
      .orderBy('sort_order', 'asc')
      .get()
    
    return {
      success: true,
      data: cities.map(city => ({
        id: city._id,
        name: city.name,
        code: city.code,
        province: city.province,
        level: city.level,
        isHot: city.is_hot === 1
      }))
    }
  } catch (error) {
    console.error('获取城市数据失败:', error)
    throw error
  }
}
