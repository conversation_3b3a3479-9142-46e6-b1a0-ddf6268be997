// 云函数：修改用户身份
const cloud = require('wx-server-sdk')

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 修改用户身份云函数
 * @param {Object} event - 云函数入参
 * @param {number} event.newUserType - 新的用户类型：1参展商 2设计公司 3工厂 4综合商
 * @param {Object} event.companyInfo - 公司信息（当切换到需要公司信息的身份时）
 * @param {Object} context - 云函数上下文
 */
exports.main = async (event, context) => {
  console.log('修改用户身份云函数被调用，参数:', event)
  
  try {
    // 获取微信上下文信息
    const wxContext = cloud.getWXContext()
    const { OPENID } = wxContext
    
    if (!OPENID) {
      return {
        success: false,
        code: 401,
        message: '用户身份验证失败'
      }
    }
    
    const { newUserType, companyInfo } = event
    
    if (!newUserType || ![1, 2, 3, 4].includes(newUserType)) {
      return {
        success: false,
        code: 400,
        message: '用户类型无效'
      }
    }
    
    // 1. 获取当前用户信息
    const userCollection = db.collection('users')
    const { data: users } = await userCollection.where({
      _openid: OPENID
    }).get()
    
    if (users.length === 0) {
      return {
        success: false,
        code: 404,
        message: '用户不存在'
      }
    }
    
    const currentUser = users[0]
    const oldUserType = currentUser.user_type
    
    if (oldUserType === newUserType) {
      return {
        success: false,
        code: 400,
        message: '新身份与当前身份相同'
      }
    }
    
    const now = new Date()
    
    // 2. 处理旧身份的数据（软删除相关公司记录）
    await handleOldUserTypeData(OPENID, oldUserType)
    
    // 3. 更新用户身份
    await userCollection.where({
      _openid: OPENID
    }).update({
      data: {
        user_type: newUserType,
        updatedAt: now
      }
    })
    
    // 4. 创建新身份的公司记录（如果需要）
    let companyId = null
    if (newUserType !== 1 && companyInfo) { // 非参展商需要公司信息
      companyId = await createCompanyRecord(OPENID, newUserType, companyInfo)
    }
    
    // 5. 获取更新后的用户信息
    const { data: updatedUsers } = await userCollection.where({
      _openid: OPENID
    }).get()
    
    const updatedUser = updatedUsers[0]
    
    const result = {
      success: true,
      code: 200,
      message: '身份修改成功',
      data: {
        userInfo: {
          id: updatedUser._id,
          openid: updatedUser._openid,
          nickname: updatedUser.nickname,
          avatar: updatedUser.avatar,
          phone: updatedUser.phone,
          email: updatedUser.email,
          userType: updatedUser.user_type,
          status: updatedUser.status,
          isProfileCompleted: updatedUser.isProfileCompleted,
          loginCount: updatedUser.loginCount,
          lastLoginAt: updatedUser.lastLoginAt,
          createdAt: updatedUser.createdAt,
          updatedAt: updatedUser.updatedAt
        },
        companyId: companyId,
        oldUserType: oldUserType
      }
    }
    
    console.log('身份修改成功:', result)
    return result
    
  } catch (error) {
    console.error('身份修改失败:', error)
    
    return {
      success: false,
      code: 500,
      message: '身份修改失败：' + error.message,
      error: error.toString()
    }
  }
}

/**
 * 处理旧身份的数据（软删除）
 */
async function handleOldUserTypeData(openid, oldUserType) {
  const now = new Date()
  
  try {
    switch (oldUserType) {
      case 2: // 设计公司
        await db.collection('design_companies').where({
          _openid: openid,
          is_deleted: 0
        }).update({
          data: {
            is_deleted: 1,
            updated_at: now
          }
        })
        break
        
      case 3: // 工厂
        await db.collection('factories').where({
          _openid: openid,
          is_deleted: 0
        }).update({
          data: {
            is_deleted: 1,
            updated_at: now
          }
        })
        break
        
      case 4: // 综合商
        // 软删除设计公司记录
        await db.collection('design_companies').where({
          _openid: openid,
          is_deleted: 0
        }).update({
          data: {
            is_deleted: 1,
            updated_at: now
          }
        })
        
        // 软删除工厂记录
        await db.collection('factories').where({
          _openid: openid,
          is_deleted: 0
        }).update({
          data: {
            is_deleted: 1,
            updated_at: now
          }
        })
        break
        
      default:
        // 参展商没有公司记录需要处理
        break
    }
  } catch (error) {
    console.error('处理旧身份数据失败:', error)
    // 不抛出错误，允许继续执行
  }
}

/**
 * 创建公司记录
 */
async function createCompanyRecord(openid, userType, companyInfo) {
  const now = new Date()
  const baseCompanyData = {
    _openid: openid,
    name: companyInfo.name,
    contact_person: companyInfo.contactPerson,
    contact_phone: companyInfo.contactPhone,
    city: companyInfo.city,
    level: companyInfo.level || 1,
    is_deleted: 0,
    created_at: now,
    updated_at: now
  }
  
  try {
    switch (userType) {
      case 2: // 设计公司
        const designCompanyCollection = db.collection('design_companies')
        const designResult = await designCompanyCollection.add({
          data: baseCompanyData
        })
        return designResult._id
        
      case 3: // 工厂
        const factoryCollection = db.collection('factories')
        const factoryData = {
          ...baseCompanyData,
          specialties: companyInfo.specialties || ''
        }
        const factoryResult = await factoryCollection.add({
          data: factoryData
        })
        return factoryResult._id
        
      case 4: // 综合商
        const designCollection = db.collection('design_companies')
        const factoriesCollection = db.collection('factories')
        
        // 创建设计公司记录
        const designCompanyResult = await designCollection.add({
          data: baseCompanyData
        })
        
        // 创建工厂记录
        const factoryCompanyData = {
          ...baseCompanyData,
          specialties: companyInfo.specialties || ''
        }
        const factoryCompanyResult = await factoriesCollection.add({
          data: factoryCompanyData
        })
        
        return {
          designCompanyId: designCompanyResult._id,
          factoryId: factoryCompanyResult._id
        }
        
      default:
        return null
    }
  } catch (error) {
    console.error('创建公司记录失败:', error)
    throw error
  }
}
