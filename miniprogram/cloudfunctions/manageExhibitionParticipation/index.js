// 云函数：管理展会参与（参加/退出展会）
const cloud = require('wx-server-sdk')

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 管理展会参与云函数
 * @param {Object} event - 云函数入参
 * @param {string} event.action - 操作类型：'join'参加, 'leave'退出, 'check'检查状态
 * @param {string} event.exhibitionId - 展会ID
 * @param {number} event.participantType - 参展类型：2设计公司 3工厂 4综合商
 * @param {string} event.participantId - 参展方ID（工厂或设计公司ID）
 */
exports.main = async (event, context) => {
  console.log('管理展会参与云函数被调用，参数:', event)
  
  try {
    const wxContext = cloud.getWXContext()
    const { OPENID } = wxContext
    
    if (!OPENID) {
      return {
        success: false,
        message: '用户身份验证失败'
      }
    }
    
    const { action, exhibitionId, participantType, participantId } = event
    
    if (!action || !exhibitionId) {
      return {
        success: false,
        message: '参数不完整'
      }
    }
    
    switch (action) {
      case 'join':
        return await joinExhibition(OPENID, { exhibitionId, participantType, participantId })
      case 'leave':
        return await leaveExhibition(OPENID, { exhibitionId })
      case 'check':
        return await checkParticipationStatus(OPENID, { exhibitionId })
      default:
        return {
          success: false,
          message: '无效的操作类型'
        }
    }
  } catch (error) {
    console.error('管理展会参与失败:', error)
    return {
      success: false,
      message: error.message || '操作失败'
    }
  }
}

/**
 * 参加展会
 */
async function joinExhibition(openid, params) {
  const { exhibitionId, participantType, participantId } = params

  try {
    // 1. 获取用户信息，确定参展身份
    const { data: users } = await db.collection('users').where({
      _openid: openid
    }).get()

    if (users.length === 0) {
      return {
        success: false,
        message: '用户不存在'
      }
    }

    const user = users[0]
    const userType = user.user_type

    if (!userType) {
      return {
        success: false,
        message: '请先选择您的身份'
      }
    }

    // 2. 验证展会是否存在且可参加
    const { data: exhibitions } = await db.collection('exhibitions').where({
      _id: exhibitionId,
      is_deleted: 0,
      status: db.command.in([0, 1]) // 未开始或进行中
    }).get()

    if (exhibitions.length === 0) {
      return {
        success: false,
        message: '展会不存在或已结束'
      }
    }
    
    // 3. 检查是否已经参加
    const participantsCollection = db.collection('exhibition_participants')
    const { data: existing } = await participantsCollection.where({
      _openid: openid,
      exhibition_id: exhibitionId,
      is_deleted: 0
    }).get()

    if (existing.length > 0) {
      return {
        success: false,
        message: '您已经参加了这个展会'
      }
    }

    // 4. 根据用户身份获取参展方ID
    let actualParticipantId = openid // 默认使用openid
    let actualParticipantType = participantType || userType // 使用传入的类型或用户身份

    // 如果用户是设计公司、工厂或综合商，获取对应的公司ID
    if (userType === 2) { // 设计公司
      const { data: designCompanies } = await db.collection('design_companies').where({
        _openid: openid,
        is_deleted: 0
      }).get()
      if (designCompanies.length > 0) {
        actualParticipantId = designCompanies[0]._id
      }
      actualParticipantType = 2
    } else if (userType === 3) { // 工厂
      const { data: factories } = await db.collection('factories').where({
        _openid: openid,
        is_deleted: 0
      }).get()
      if (factories.length > 0) {
        actualParticipantId = factories[0]._id
      }
      actualParticipantType = 3
    } else if (userType === 4) { // 综合商
      // 综合商可以选择以设计公司或工厂身份参展
      if (participantType === 2) {
        const { data: designCompanies } = await db.collection('design_companies').where({
          _openid: openid,
          is_deleted: 0
        }).get()
        if (designCompanies.length > 0) {
          actualParticipantId = designCompanies[0]._id
        }
      } else if (participantType === 3) {
        const { data: factories } = await db.collection('factories').where({
          _openid: openid,
          is_deleted: 0
        }).get()
        if (factories.length > 0) {
          actualParticipantId = factories[0]._id
        }
      }
      actualParticipantType = participantType || 2 // 默认以设计公司身份参展
    }

    // 5. 创建参展记录
    const now = new Date()
    const participantData = {
      _openid: openid,
      exhibition_id: exhibitionId,
      participant_type: actualParticipantType,
      participant_id: actualParticipantId,
      is_full: 0,
      is_deleted: 0,
      created_at: now,
      updated_at: now
    }
    
    const { _id } = await participantsCollection.add({
      data: participantData
    })

    // 6. 获取展会详情返回
    const exhibition = exhibitions[0]

    return {
      success: true,
      message: '参加展会成功',
      data: {
        participantId: _id,
        participantType: actualParticipantType,
        participantCompanyId: actualParticipantId,
        exhibition: {
          id: exhibition._id,
          name: exhibition.name,
          startDate: exhibition.start_date,
          endDate: exhibition.end_date,
          status: exhibition.status
        }
      }
    }
  } catch (error) {
    console.error('参加展会失败:', error)
    throw error
  }
}

/**
 * 检查参展状态
 */
async function checkParticipationStatus(openid, params) {
  const { exhibitionId } = params

  try {
    // 查找参展记录
    const participantsCollection = db.collection('exhibition_participants')
    const { data: participants } = await participantsCollection.where({
      _openid: openid,
      exhibition_id: exhibitionId,
      is_deleted: 0
    }).get()

    const isParticipated = participants.length > 0
    const participationInfo = isParticipated ? participants[0] : null

    return {
      success: true,
      data: {
        isParticipated: isParticipated,
        participationInfo: participationInfo
      }
    }
  } catch (error) {
    console.error('检查参展状态失败:', error)
    throw error
  }
}

/**
 * 退出展会
 */
async function leaveExhibition(openid, params) {
  const { exhibitionId } = params
  
  try {
    // 1. 查找参展记录
    const participantsCollection = db.collection('exhibition_participants')
    const { data: participants } = await participantsCollection.where({
      _openid: openid,
      exhibition_id: exhibitionId,
      is_deleted: 0
    }).get()
    
    if (participants.length === 0) {
      return {
        success: false,
        message: '您没有参加这个展会'
      }
    }
    
    // 2. 软删除参展记录
    const participant = participants[0]
    await participantsCollection.doc(participant._id).update({
      data: {
        is_deleted: 1,
        updated_at: new Date()
      }
    })
    
    return {
      success: true,
      message: '退出展会成功'
    }
  } catch (error) {
    console.error('退出展会失败:', error)
    throw error
  }
}
