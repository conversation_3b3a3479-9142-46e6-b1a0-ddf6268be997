// 云函数：搜索公司（工厂和设计公司）
const cloud = require('wx-server-sdk')

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

/**
 * 搜索公司云函数
 * @param {Object} event - 云函数入参
 * @param {string} event.type - 搜索类型：'factory'工厂, 'design'设计公司
 * @param {number} event.page - 页码
 * @param {number} event.pageSize - 每页数量
 * @param {string} event.keyword - 搜索关键字
 * @param {string} event.city - 城市筛选
 * @param {number} event.level - 级别筛选
 * @param {string} event.exhibitionId - 展会ID（筛选参展公司）
 */
exports.main = async (event, context) => {
  console.log('搜索公司云函数被调用，参数:', event)

  try {
    const {
      type = 'factory',
      page = 1,
      pageSize = 10,
      keyword,
      city,
      level,
      exhibitionId,
      date,
      venueId
    } = event

    switch (type) {
      case 'factory':
        return await searchFactories({ page, pageSize, keyword, city, level, exhibitionId, date, venueId })
      case 'design':
        return await searchDesignCompanies({ page, pageSize, keyword, city, level, exhibitionId, date, venueId })
      default:
        throw new Error('无效的搜索类型')
    }
  } catch (error) {
    console.error('搜索公司失败:', error)
    return {
      success: false,
      message: error.message || '搜索失败'
    }
  }
}

/**
 * 搜索工厂
 */
async function searchFactories(params) {
  const { page, pageSize, keyword, city, level, exhibitionId, date, venueId } = params

  console.log('🏭 [工厂搜索] 开始搜索，参数:', params)

  // 检查是否为推荐模式（没有城市和日期，只有level参数）
  const isRecommendMode = !city && !date && level

  if (isRecommendMode) {
    console.log('🌟 [工厂搜索] 推荐模式，直接查询高级别工厂')
    return await searchFactoriesRecommend(params)
  }

  // 验证必填参数（非推荐模式）
  if (!city || !date) {
    console.log('❌ [工厂搜索] 参数验证失败:', { city, date })
    return {
      success: false,
      message: '城市和日期为必填参数'
    }
  }

  console.log('✅ [工厂搜索] 参数验证通过')

  // 根据城市、日期和场馆查找展会
  let targetExhibitions = []
  const exhibitionWhere = {
    city: city,
    start_date: _.lte(date),
    end_date: _.gte(date),
    is_deleted: 0
  }

  // 如果指定了场馆，添加场馆条件
  if (venueId) {
    exhibitionWhere.venue_id = venueId
    console.log('🏢 [工厂搜索] 添加场馆条件:', venueId)
  }

  console.log('🔎 [工厂搜索] 展会查询条件:', exhibitionWhere)

  const { data: exhibitions } = await db.collection('exhibitions').where(exhibitionWhere).get()

  console.log('📊 [工厂搜索] 找到展会数量:', exhibitions.length)
  console.log('📋 [工厂搜索] 展会详情:', exhibitions)

  if (exhibitions.length === 0) {
    return {
      success: true,
      data: {
        list: [],
        total: 0,
        page,
        pageSize,
        totalPages: 0
      }
    }
  }

  targetExhibitions = exhibitions

  // 获取所有相关展会的参展工厂
  const exhibitionIds = targetExhibitions.map(e => e._id)
  const { data: participants } = await db.collection('exhibition_participants').where({
    exhibition_id: _.in(exhibitionIds),
    participant_type: _.in([3, 4]), // 工厂或综合商
    is_deleted: 0
  }).get()

  if (participants.length === 0) {
    return {
      success: true,
      data: {
        list: [],
        total: 0,
        page,
        pageSize,
        totalPages: 0
      }
    }
  }

  // 创建参展信息映射（participant_id -> exhibition_id）
  const participantExhibitionMap = new Map()
  participants.forEach(p => {
    participantExhibitionMap.set(p.participant_id, p.exhibition_id)
  })

  const participantFactoryIds = participants.map(p => p.participant_id)

  // 构建工厂查询条件
  const whereConditions = {
    _id: _.in(participantFactoryIds),
    is_deleted: 0
  }

  if (keyword) {
    whereConditions.$or = [
      { name: new RegExp(keyword, 'i') },
      { contact_person: new RegExp(keyword, 'i') },
      { specialties: new RegExp(keyword, 'i') }
    ]
  }

  if (level) {
    whereConditions.level = level
  }

  // 获取总数
  const { total } = await db.collection('factories').where(whereConditions).count()

  // 获取分页数据
  const { data: factories } = await db.collection('factories')
    .where(whereConditions)
    .orderBy('level', 'desc')
    .orderBy('created_at', 'desc')
    .skip((page - 1) * pageSize)
    .limit(pageSize)
    .get()

  // 获取用户信息和展会信息
  const enrichedFactories = await enrichFactoriesData(factories, participantExhibitionMap, targetExhibitions)

  return {
    success: true,
    data: {
      list: enrichedFactories,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    }
  }
}

/**
 * 搜索设计公司
 */
async function searchDesignCompanies(params) {
  const { page, pageSize, keyword, city, level, exhibitionId, date, venueId } = params

  console.log('🔍 [设计公司搜索] 开始搜索，参数:', params)

  // 检查是否为推荐模式（没有城市和日期，只有level参数）
  const isRecommendMode = !city && !date && level

  if (isRecommendMode) {
    console.log('🌟 [设计公司搜索] 推荐模式，直接查询高级别设计公司')
    return await searchDesignCompaniesRecommend(params)
  }

  // 验证必填参数（非推荐模式）
  if (!city || !date) {
    console.log('❌ [设计公司搜索] 参数验证失败:', { city, date })
    return {
      success: false,
      message: '城市和日期为必填参数'
    }
  }

  console.log('✅ [设计公司搜索] 参数验证通过')

  // 根据城市、日期和场馆查找展会
  let targetExhibitions = []
  const exhibitionWhere = {
    city: city,
    start_date: _.lte(date),
    end_date: _.gte(date),
    is_deleted: 0
  }

  // 如果指定了场馆，添加场馆条件
  if (venueId) {
    exhibitionWhere.venue_id = venueId
    console.log('🏢 [设计公司搜索] 添加场馆条件:', venueId)
  }

  console.log('🔎 [设计公司搜索] 展会查询条件:', exhibitionWhere)

  const { data: exhibitions } = await db.collection('exhibitions').where(exhibitionWhere).get()

  console.log('📊 [设计公司搜索] 找到展会数量:', exhibitions.length)
  console.log('📋 [设计公司搜索] 展会详情:', exhibitions)

  if (exhibitions.length === 0) {
    console.log('⚠️ [设计公司搜索] 没有找到符合条件的展会')
    return {
      success: true,
      data: {
        list: [],
        total: 0,
        page,
        pageSize,
        totalPages: 0
      }
    }
  }

  targetExhibitions = exhibitions

  // 获取所有相关展会的参展设计公司
  const exhibitionIds = targetExhibitions.map(e => e._id)
  console.log('🎯 [设计公司搜索] 展会IDs:', exhibitionIds)

  const participantQuery = {
    exhibition_id: _.in(exhibitionIds),
    participant_type: _.in([2, 4]), // 设计公司或综合商
    is_deleted: 0
  }
  console.log('🔍 [设计公司搜索] 参展信息查询条件:', participantQuery)

  const { data: participants } = await db.collection('exhibition_participants').where(participantQuery).get()

  console.log('👥 [设计公司搜索] 找到参展信息数量:', participants.length)
  console.log('📝 [设计公司搜索] 参展信息详情:', participants)

  if (participants.length === 0) {
    console.log('⚠️ [设计公司搜索] 没有找到参展的设计公司')
    return {
      success: true,
      data: {
        list: [],
        total: 0,
        page,
        pageSize,
        totalPages: 0
      }
    }
  }

  // 创建参展信息映射（participant_id -> exhibition_id）
  const participantExhibitionMap = new Map()
  participants.forEach(p => {
    participantExhibitionMap.set(p.participant_id, p.exhibition_id)
  })

  const participantCompanyIds = participants.map(p => p.participant_id)
  console.log('🏢 [设计公司搜索] 参展公司IDs:', participantCompanyIds)

  // 构建设计公司查询条件
  const whereConditions = {
    _id: _.in(participantCompanyIds),
    is_deleted: 0
  }

  if (keyword) {
    whereConditions.$or = [
      { name: new RegExp(keyword, 'i') },
      { contact_person: new RegExp(keyword, 'i') }
    ]
    console.log('🔤 [设计公司搜索] 添加关键字条件:', keyword)
  }

  if (level) {
    whereConditions.level = level
    console.log('⭐ [设计公司搜索] 添加级别条件:', level)
  }

  console.log('🔍 [设计公司搜索] 设计公司查询条件:', whereConditions)

  // 获取总数
  const { total } = await db.collection('design_companies').where(whereConditions).count()
  console.log('📊 [设计公司搜索] 符合条件的设计公司总数:', total)

  // 获取分页数据
  const { data: companies } = await db.collection('design_companies')
    .where(whereConditions)
    .orderBy('level', 'desc')
    .orderBy('created_at', 'desc')
    .skip((page - 1) * pageSize)
    .limit(pageSize)
    .get()

  console.log('🏢 [设计公司搜索] 当前页设计公司数量:', companies.length)
  console.log('📋 [设计公司搜索] 设计公司详情:', companies)

  // 获取用户信息和展会信息
  const enrichedCompanies = await enrichDesignCompaniesData(companies, participantExhibitionMap, targetExhibitions)

  console.log('✨ [设计公司搜索] 最终结果数量:', enrichedCompanies.length)
  console.log('🎉 [设计公司搜索] 搜索完成')

  return {
    success: true,
    data: {
      list: enrichedCompanies,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    }
  }
}

/**
 * 丰富工厂数据
 */
async function enrichFactoriesData(factories, participantExhibitionMap, exhibitions) {
  if (factories.length === 0) return []

  // 获取用户信息
  const userOpenids = [...new Set(factories.map(f => f._openid))]
  const { data: users } = await db.collection('users')
    .where({
      _openid: _.in(userOpenids)
    })
    .get()

  const userMap = new Map()
  users.forEach(user => {
    userMap.set(user._openid, user)
  })

  // 创建展会映射
  const exhibitionMap = new Map()
  exhibitions.forEach(exhibition => {
    exhibitionMap.set(exhibition._id, exhibition)
  })

  return factories.map(factory => {
    const user = userMap.get(factory._openid)
    const exhibitionId = participantExhibitionMap.get(factory._id)
    const exhibition = exhibitionId ? exhibitionMap.get(exhibitionId) : null

    return {
      id: factory._id,
      name: factory.name,
      contactPerson: factory.contact_person,
      contactPhone: factory.contact_phone,
      city: factory.city,
      specialties: factory.specialties,
      level: factory.level,
      type: 'factory',
      isParticipant: !!exhibitionId,
      exhibitionName: exhibition ? exhibition.name : null,
      user: user ? {
        nickname: user.nickname,
        phone: user.phone
      } : null,
      createdAt: factory.created_at
    }
  })
}

/**
 * 丰富设计公司数据
 */
async function enrichDesignCompaniesData(companies, participantExhibitionMap, exhibitions) {
  if (companies.length === 0) return []

  // 获取用户信息
  const userOpenids = [...new Set(companies.map(c => c._openid))]
  const { data: users } = await db.collection('users')
    .where({
      _openid: _.in(userOpenids)
    })
    .get()

  const userMap = new Map()
  users.forEach(user => {
    userMap.set(user._openid, user)
  })

  // 创建展会映射
  const exhibitionMap = new Map()
  exhibitions.forEach(exhibition => {
    exhibitionMap.set(exhibition._id, exhibition)
  })

  return companies.map(company => {
    const user = userMap.get(company._openid)
    const exhibitionId = participantExhibitionMap.get(company._id)
    const exhibition = exhibitionId ? exhibitionMap.get(exhibitionId) : null

    return {
      id: company._id,
      name: company.name,
      contactPerson: company.contact_person,
      contactPhone: company.contact_phone,
      city: company.city,
      level: company.level,
      type: 'design',
      isParticipant: !!exhibitionId,
      exhibitionName: exhibition ? exhibition.name : null,
      user: user ? {
        nickname: user.nickname,
        phone: user.phone
      } : null,
      createdAt: company.created_at
    }
  })
}

/**
 * 推荐模式搜索工厂（不需要城市和日期）
 */
async function searchFactoriesRecommend(params) {
  const { page, pageSize, keyword, level } = params

  console.log('🌟 [推荐工厂] 开始推荐搜索，参数:', params)

  // 构建查询条件
  const whereConditions = {
    is_deleted: 0
  }

  if (keyword) {
    whereConditions.$or = [
      { name: new RegExp(keyword, 'i') },
      { contact_person: new RegExp(keyword, 'i') },
      { specialties: new RegExp(keyword, 'i') }
    ]
    console.log('🔤 [推荐工厂] 添加关键字条件:', keyword)
  }

  if (level) {
    whereConditions.level = level
    console.log('⭐ [推荐工厂] 添加级别条件:', level)
  }

  console.log('🔍 [推荐工厂] 查询条件:', whereConditions)

  // 获取总数
  const { total } = await db.collection('factories').where(whereConditions).count()
  console.log('📊 [推荐工厂] 符合条件的工厂总数:', total)

  // 获取分页数据
  const { data: factories } = await db.collection('factories')
    .where(whereConditions)
    .orderBy('level', 'desc')
    .orderBy('created_at', 'desc')
    .skip((page - 1) * pageSize)
    .limit(pageSize)
    .get()

  console.log('🏢 [推荐工厂] 当前页工厂数量:', factories.length)
  console.log('📋 [推荐工厂] 工厂详情:', factories)

  // 获取用户信息（推荐模式不需要展会信息）
  const enrichedFactories = await enrichFactoriesRecommend(factories)

  console.log('✨ [推荐工厂] 最终结果数量:', enrichedFactories.length)
  console.log('🎉 [推荐工厂] 推荐搜索完成')

  return {
    success: true,
    data: {
      list: enrichedFactories,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    }
  }
}

/**
 * 丰富推荐工厂数据（不包含展会信息）
 */
async function enrichFactoriesRecommend(factories) {
  if (factories.length === 0) return []

  // 获取用户信息
  const userOpenids = [...new Set(factories.map(f => f._openid))]
  const { data: users } = await db.collection('users')
    .where({
      _openid: _.in(userOpenids)
    })
    .get()

  const userMap = new Map()
  users.forEach(user => {
    userMap.set(user._openid, user)
  })

  return factories.map(factory => {
    const user = userMap.get(factory._openid)

    return {
      id: factory._id,
      name: factory.name,
      contactPerson: factory.contact_person,
      contactPhone: factory.contact_phone,
      city: factory.city,
      specialties: factory.specialties,
      level: factory.level,
      type: 'factory',
      isParticipant: false, // 推荐模式不显示参展状态
      exhibitionName: null, // 推荐模式不显示展会名称
      user: user ? {
        nickname: user.nickname,
        phone: user.phone
      } : null,
      createdAt: factory.created_at
    }
  })
}

/**
 * 推荐模式搜索设计公司（不需要城市和日期）
 */
async function searchDesignCompaniesRecommend(params) {
  const { page, pageSize, keyword, level } = params

  console.log('🌟 [推荐设计公司] 开始推荐搜索，参数:', params)

  // 构建查询条件
  const whereConditions = {
    is_deleted: 0
  }

  if (keyword) {
    whereConditions.$or = [
      { name: new RegExp(keyword, 'i') },
      { contact_person: new RegExp(keyword, 'i') }
    ]
    console.log('🔤 [推荐设计公司] 添加关键字条件:', keyword)
  }

  if (level) {
    whereConditions.level = level
    console.log('⭐ [推荐设计公司] 添加级别条件:', level)
  }

  console.log('🔍 [推荐设计公司] 查询条件:', whereConditions)

  // 获取总数
  const { total } = await db.collection('design_companies').where(whereConditions).count()
  console.log('📊 [推荐设计公司] 符合条件的设计公司总数:', total)

  // 获取分页数据
  const { data: companies } = await db.collection('design_companies')
    .where(whereConditions)
    .orderBy('level', 'desc')
    .orderBy('created_at', 'desc')
    .skip((page - 1) * pageSize)
    .limit(pageSize)
    .get()

  console.log('🏢 [推荐设计公司] 当前页设计公司数量:', companies.length)
  console.log('📋 [推荐设计公司] 设计公司详情:', companies)

  // 获取用户信息（推荐模式不需要展会信息）
  const enrichedCompanies = await enrichDesignCompaniesRecommend(companies)

  console.log('✨ [推荐设计公司] 最终结果数量:', enrichedCompanies.length)
  console.log('🎉 [推荐设计公司] 推荐搜索完成')

  return {
    success: true,
    data: {
      list: enrichedCompanies,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    }
  }
}

/**
 * 丰富推荐设计公司数据（不包含展会信息）
 */
async function enrichDesignCompaniesRecommend(companies) {
  if (companies.length === 0) return []

  // 获取用户信息
  const userOpenids = [...new Set(companies.map(c => c._openid))]
  const { data: users } = await db.collection('users')
    .where({
      _openid: _.in(userOpenids)
    })
    .get()

  const userMap = new Map()
  users.forEach(user => {
    userMap.set(user._openid, user)
  })

  return companies.map(company => {
    const user = userMap.get(company._openid)

    return {
      id: company._id,
      name: company.name,
      contactPerson: company.contact_person,
      contactPhone: company.contact_phone,
      city: company.city,
      level: company.level,
      type: 'design',
      isParticipant: false, // 推荐模式不显示参展状态
      exhibitionName: null, // 推荐模式不显示展会名称
      user: user ? {
        nickname: user.nickname,
        phone: user.phone
      } : null,
      createdAt: company.created_at
    }
  })
}
