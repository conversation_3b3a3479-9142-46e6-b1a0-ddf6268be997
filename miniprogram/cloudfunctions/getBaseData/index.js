// 云函数：获取基础数据（展馆、类别等）
const cloud = require('wx-server-sdk')

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 获取基础数据云函数
 * @param {Object} event - 云函数入参
 * @param {string} event.type - 数据类型：'venues'展馆, 'categories'展会类别, 'all'所有基础数据
 */
exports.main = async (event, context) => {
  console.log('获取基础数据云函数被调用，参数:', event)
  
  try {
    const { type = 'all' } = event
    
    switch (type) {
      case 'venues':
        return await getVenues()
      case 'categories':
        return await getCategories()
      case 'cities':
        return await getCities()
      case 'all':
        return await getAllBaseData()
      default:
        throw new Error('无效的数据类型')
    }
  } catch (error) {
    console.error('获取基础数据失败:', error)
    return {
      success: false,
      message: error.message || '获取基础数据失败'
    }
  }
}

/**
 * 获取展馆列表
 */
async function getVenues() {
  try {
    const { data: venues } = await db.collection('venues').where({
      is_deleted: 0
    }).orderBy('city', 'asc').orderBy('name', 'asc').get()
    
    const formattedVenues = venues.map(venue => ({
      id: venue._id,
      name: venue.name,
      city: venue.city,
      address: venue.address
    }))
    
    return {
      success: true,
      data: formattedVenues
    }
  } catch (error) {
    console.error('获取展馆列表失败:', error)
    throw error
  }
}

/**
 * 获取展会类别列表
 */
async function getCategories() {
  try {
    const { data: categories } = await db.collection('exhibition_categories').where({
      is_deleted: 0
    }).orderBy('name', 'asc').get()
    
    const formattedCategories = categories.map(category => ({
      id: category._id,
      name: category.name
    }))
    
    return {
      success: true,
      data: formattedCategories
    }
  } catch (error) {
    console.error('获取展会类别列表失败:', error)
    throw error
  }
}

/**
 * 获取城市列表
 */
async function getCities() {
  try {
    // 优先从城市表获取
    const citiesResult = await db.collection('cities')
      .where({ status: 1 })
      .orderBy('sort', 'asc')
      .orderBy('name', 'asc')
      .get()

    if (citiesResult.data.length > 0) {
      const formattedCities = citiesResult.data.map(city => ({
        id: city._id,
        name: city.name,
        value: city.name,
        province: city.province
      }))

      return {
        success: true,
        data: formattedCities
      }
    }

    // 如果城市表为空，从展馆数据中提取城市
    const venues = await db.collection('venues').get()
    const cities = [...new Set(venues.data.map(venue => venue.city))]

    const formattedCities = cities.map(city => ({
      name: city,
      value: city
    }))

    return {
      success: true,
      data: formattedCities
    }
  } catch (error) {
    console.error('获取城市列表失败:', error)
    // 返回默认城市列表
    const defaultCities = [
      { name: '北京', value: '北京' },
      { name: '上海', value: '上海' },
      { name: '广州', value: '广州' },
      { name: '深圳', value: '深圳' },
      { name: '杭州', value: '杭州' }
    ]

    return {
      success: true,
      data: defaultCities
    }
  }
}

/**
 * 获取所有基础数据
 */
async function getAllBaseData() {
  try {
    // 并行获取所有基础数据
    const [venuesResult, categoriesResult, citiesResult] = await Promise.all([
      getVenues(),
      getCategories(),
      getCities()
    ])

    return {
      success: true,
      data: {
        venues: venuesResult.data,
        categories: categoriesResult.data,
        cities: citiesResult.data
      }
    }
  } catch (error) {
    console.error('获取所有基础数据失败:', error)
    throw error
  }
}
