// 云函数：用户身份选择
const cloud = require('wx-server-sdk')

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 用户身份选择云函数
 * @param {Object} event - 云函数入参
 * @param {number} event.userType - 用户类型：1参展商 2设计公司 3工厂 4综合商
 * @param {Object} event.companyInfo - 公司信息（可选）
 * @param {Object} context - 云函数上下文
 */
exports.main = async (event, context) => {
  console.log('用户身份选择云函数被调用，参数:', event)
  
  try {
    // 获取微信上下文信息
    const wxContext = cloud.getWXContext()
    const { OPENID } = wxContext
    
    if (!OPENID) {
      return {
        success: false,
        code: 401,
        message: '用户身份验证失败'
      }
    }
    
    const { userType, companyInfo } = event
    
    if (!userType || ![1, 2, 3, 4].includes(userType)) {
      return {
        success: false,
        code: 400,
        message: '用户类型无效'
      }
    }
    
    const now = new Date()
    
    // 1. 更新用户基本信息
    const userCollection = db.collection('users')
    const updateResult = await userCollection.where({
      _openid: OPENID
    }).update({
      data: {
        user_type: userType,
        isProfileCompleted: !!companyInfo, // 如果提供了公司信息，标记为已完善
        updatedAt: now
      }
    })
    
    if (updateResult.stats.updated === 0) {
      return {
        success: false,
        code: 404,
        message: '用户不存在'
      }
    }
    
    // 2. 如果提供了公司信息，创建对应的公司记录
    let companyId = null
    if (companyInfo) {
      companyId = await createCompanyRecord(OPENID, userType, companyInfo)
    }
    
    // 3. 获取更新后的用户信息
    const { data: users } = await userCollection.where({
      _openid: OPENID
    }).get()
    
    const user = users[0]
    
    const result = {
      success: true,
      code: 200,
      message: '身份选择成功',
      data: {
        userInfo: {
          id: user._id,
          openid: user._openid,
          nickname: user.nickname,
          avatar: user.avatar,
          phone: user.phone,
          email: user.email,
          userType: user.user_type,
          status: user.status,
          isProfileCompleted: user.isProfileCompleted,
          loginCount: user.loginCount,
          lastLoginAt: user.lastLoginAt,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt
        },
        companyId: companyId
      }
    }
    
    console.log('身份选择成功:', result)
    return result
    
  } catch (error) {
    console.error('身份选择失败:', error)
    
    return {
      success: false,
      code: 500,
      message: '身份选择失败：' + error.message,
      error: error.toString()
    }
  }
}

/**
 * 创建公司记录
 */
async function createCompanyRecord(openid, userType, companyInfo) {
  const now = new Date()
  const baseCompanyData = {
    _openid: openid,
    name: companyInfo.name,
    contact_person: companyInfo.contactPerson,
    contact_phone: companyInfo.contactPhone,
    city: companyInfo.city,
    level: companyInfo.level || 1,
    is_deleted: 0,
    created_at: now,
    updated_at: now
  }
  
  try {
    switch (userType) {
      case 2: // 设计公司
        const designCompanyCollection = db.collection('design_companies')
        const designResult = await designCompanyCollection.add({
          data: baseCompanyData
        })
        return designResult._id
        
      case 3: // 工厂
        const factoryCollection = db.collection('factories')
        const factoryData = {
          ...baseCompanyData,
          specialties: companyInfo.specialties || ''
        }
        const factoryResult = await factoryCollection.add({
          data: factoryData
        })
        return factoryResult._id
        
      case 4: // 综合商（同时创建设计公司和工厂记录）
        const designCollection = db.collection('design_companies')
        const factoriesCollection = db.collection('factories')
        
        // 创建设计公司记录
        const designCompanyResult = await designCollection.add({
          data: baseCompanyData
        })
        
        // 创建工厂记录
        const factoryCompanyData = {
          ...baseCompanyData,
          specialties: companyInfo.specialties || ''
        }
        const factoryCompanyResult = await factoriesCollection.add({
          data: factoryCompanyData
        })
        
        return {
          designCompanyId: designCompanyResult._id,
          factoryId: factoryCompanyResult._id
        }
        
      default:
        // 参展商不需要创建公司记录
        return null
    }
  } catch (error) {
    console.error('创建公司记录失败:', error)
    throw error
  }
}
