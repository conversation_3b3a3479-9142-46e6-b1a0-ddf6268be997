// 云函数：初始化基础数据
const cloud = require('wx-server-sdk')

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 初始化基础数据云函数
 * 创建展会类别、展馆、展会等基础数据
 */
exports.main = async (event, context) => {
  console.log('初始化基础数据云函数被调用')
  
  try {
    // 1. 初始化展会类别
    await initExhibitionCategories()
    
    // 2. 初始化展馆
    await initVenues()
    
    // 3. 初始化展会
    await initExhibitions()
    
    // 4. 初始化测试数据（工厂、设计公司等）
    await initTestData()
    
    return {
      success: true,
      message: '基础数据初始化完成'
    }
  } catch (error) {
    console.error('初始化基础数据失败:', error)
    return {
      success: false,
      message: error.message || '初始化基础数据失败'
    }
  }
}

/**
 * 初始化展会类别
 */
async function initExhibitionCategories() {
  const categoriesCollection = db.collection('exhibition_categories')
  
  const categories = [
    { name: '服装展' },
    { name: '水展' },
    { name: '汽车展' },
    { name: '科技展' },
    { name: '箱包展' },
    { name: '家具展' },
    { name: '建材展' },
    { name: '食品展' },
    { name: '医疗展' },
    { name: '教育展' }
  ]
  
  for (const category of categories) {
    try {
      // 检查是否已存在
      const { data: existing } = await categoriesCollection.where({
        name: category.name
      }).get()
      
      if (existing.length === 0) {
        await categoriesCollection.add({
          data: {
            ...category,
            is_deleted: 0,
            created_at: new Date(),
            updated_at: new Date()
          }
        })
        console.log(`创建展会类别: ${category.name}`)
      }
    } catch (error) {
      console.error(`创建展会类别失败: ${category.name}`, error)
    }
  }
}

/**
 * 初始化展馆
 */
async function initVenues() {
  const venuesCollection = db.collection('venues')
  
  const venues = [
    { city: '上海', name: '新国际博览中心', address: '上海市浦东新区龙阳路2345号' },
    { city: '上海', name: '国家会展中心', address: '上海市青浦区崧泽大道333号' },
    { city: '北京', name: '中国国际展览中心', address: '北京市朝阳区北三环东路6号' },
    { city: '深圳', name: '深圳会展中心', address: '深圳市福田区福华三路111号' },
    { city: '广州', name: '中国进出口商品交易会展馆', address: '广州市海珠区阅江中路380号' },
    { city: '杭州', name: '杭州国际博览中心', address: '杭州市萧山区奔竞大道353号' },
    { city: '成都', name: '中国西部国际博览城', address: '成都市天府新区福州路东段88号' },
    { city: '武汉', name: '武汉国际博览中心', address: '武汉市汉阳区鹦鹉大道619号' }
  ]
  
  for (const venue of venues) {
    try {
      // 检查是否已存在
      const { data: existing } = await venuesCollection.where({
        name: venue.name,
        city: venue.city
      }).get()
      
      if (existing.length === 0) {
        await venuesCollection.add({
          data: {
            ...venue,
            is_deleted: 0,
            created_at: new Date(),
            updated_at: new Date()
          }
        })
        console.log(`创建展馆: ${venue.city} - ${venue.name}`)
      }
    } catch (error) {
      console.error(`创建展馆失败: ${venue.name}`, error)
    }
  }
}

/**
 * 初始化展会
 */
async function initExhibitions() {
  const exhibitionsCollection = db.collection('exhibitions')
  
  // 获取展馆和类别数据用于关联
  const { data: venues } = await db.collection('venues').get()
  const { data: categories } = await db.collection('exhibition_categories').get()
  
  // 创建映射
  const venueMap = new Map()
  venues.forEach((venue, index) => {
    venueMap.set(index + 1, venue._id) // 原始ID映射到云数据库ID
  })
  
  const categoryMap = new Map()
  categories.forEach((category, index) => {
    categoryMap.set(index + 1, category._id) // 原始ID映射到云数据库ID
  })
  
  const exhibitions = [
    {
      venue_id: venueMap.get(1),
      name: '第4届上海网络安全博览会',
      start_date: '2025-06-05',
      end_date: '2025-06-07',
      category_id: categoryMap.get(4),
      status: 0,
      city: '上海'
    },
    {
      venue_id: venueMap.get(1),
      name: 'GPOWER2025动力展',
      start_date: '2025-06-11',
      end_date: '2025-06-13',
      category_id: categoryMap.get(3),
      status: 0,
      city: '上海'
    },
    {
      venue_id: venueMap.get(1),
      name: '2025润滑油聚焦产业周论坛推介会',
      start_date: '2025-06-02',
      end_date: '2025-06-03',
      category_id: categoryMap.get(2),
      status: 0,
      city: '上海'
    },
    {
      venue_id: venueMap.get(2),
      name: '上海世环会 节能环保低碳舒适系统展',
      start_date: '2025-06-04',
      end_date: '2025-06-06',
      category_id: categoryMap.get(2),
      status: 0,
      city: '上海'
    },
    {
      venue_id: venueMap.get(1),
      name: '2025设计上海展',
      start_date: '2025-06-04',
      end_date: '2025-06-07',
      category_id: categoryMap.get(6),
      status: 0,
      city: '上海'
    },
    {
      venue_id: venueMap.get(3),
      name: '2025北京国际汽车展览会',
      start_date: '2025-08-25',
      end_date: '2025-08-28',
      category_id: categoryMap.get(3),
      status: 0,
      city: '北京'
    },
    {
      venue_id: venueMap.get(4),
      name: '2025深圳国际服装供应链博览会',
      start_date: '2025-07-10',
      end_date: '2025-07-12',
      category_id: categoryMap.get(1),
      status: 0,
      city: '深圳'
    },
    {
      venue_id: venueMap.get(5),
      name: '2025广州国际家具博览会',
      start_date: '2025-09-18',
      end_date: '2025-09-21',
      category_id: categoryMap.get(6),
      status: 0,
      city: '广州'
    }
  ]
  
  for (const exhibition of exhibitions) {
    try {
      // 检查是否已存在
      const { data: existing } = await exhibitionsCollection.where({
        name: exhibition.name
      }).get()
      
      if (existing.length === 0) {
        await exhibitionsCollection.add({
          data: {
            ...exhibition,
            is_deleted: 0,
            created_at: new Date(),
            updated_at: new Date()
          }
        })
        console.log(`创建展会: ${exhibition.name}`)
      }
    } catch (error) {
      console.error(`创建展会失败: ${exhibition.name}`, error)
    }
  }
}

/**
 * 初始化测试数据
 */
async function initTestData() {
  // 这里可以添加一些测试用的工厂、设计公司数据
  // 暂时留空，实际数据由用户创建
  console.log('测试数据初始化完成')
}
