// 云函数：管理公司信息（工厂和设计公司）
const cloud = require('wx-server-sdk')

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 管理公司信息云函数
 * @param {Object} event - 云函数入参
 * @param {string} event.action - 操作类型：'create'创建, 'update'更新, 'get'获取详情
 * @param {string} event.type - 公司类型：'factory'工厂, 'design'设计公司
 * @param {string} event.companyId - 公司ID（用于更新和获取）
 * @param {Object} event.data - 公司数据
 */
exports.main = async (event, context) => {
  console.log('管理公司信息云函数被调用，参数:', event)
  
  try {
    const wxContext = cloud.getWXContext()
    const { OPENID } = wxContext
    
    if (!OPENID) {
      return {
        success: false,
        message: '用户身份验证失败'
      }
    }
    
    const { action, type, companyId, data } = event
    
    if (!action || !type) {
      return {
        success: false,
        message: '参数不完整'
      }
    }
    
    switch (action) {
      case 'create':
        return await createCompany(OPENID, { type, data })
      case 'update':
        return await updateCompany(OPENID, { type, companyId, data })
      case 'get':
        return await getCompanyDetail(OPENID, { type, companyId })
      default:
        return {
          success: false,
          message: '无效的操作类型'
        }
    }
  } catch (error) {
    console.error('管理公司信息失败:', error)
    return {
      success: false,
      message: error.message || '操作失败'
    }
  }
}

/**
 * 创建公司
 */
async function createCompany(openid, params) {
  const { type, data } = params
  
  try {
    const now = new Date()
    const collection = type === 'factory' ? 'factories' : 'design_companies'
    
    // 验证必填字段
    if (!data.name) {
      return {
        success: false,
        message: '公司名称不能为空'
      }
    }
    
    // 检查是否已经创建过同类型的公司
    const { data: existing } = await db.collection(collection).where({
      _openid: openid,
      is_deleted: 0
    }).get()
    
    if (existing.length > 0) {
      return {
        success: false,
        message: `您已经创建过${type === 'factory' ? '工厂' : '设计公司'}信息`
      }
    }
    
    // 构建公司数据
    const companyData = {
      _openid: openid,
      name: data.name,
      contact_person: data.contactPerson || '',
      contact_phone: data.contactPhone || '',
      city: data.city || '',
      level: data.level || 1,
      is_deleted: 0,
      created_at: now,
      updated_at: now
    }
    
    // 工厂特有字段
    if (type === 'factory') {
      companyData.specialties = data.specialties || ''
    }
    
    // 创建公司记录
    const { _id } = await db.collection(collection).add({
      data: companyData
    })
    
    return {
      success: true,
      message: `创建${type === 'factory' ? '工厂' : '设计公司'}成功`,
      data: {
        id: _id,
        ...companyData
      }
    }
  } catch (error) {
    console.error('创建公司失败:', error)
    throw error
  }
}

/**
 * 更新公司信息
 */
async function updateCompany(openid, params) {
  const { type, companyId, data } = params
  
  try {
    const collection = type === 'factory' ? 'factories' : 'design_companies'
    
    // 验证公司是否存在且属于当前用户
    const { data: companies } = await db.collection(collection).where({
      _id: companyId,
      _openid: openid,
      is_deleted: 0
    }).get()
    
    if (companies.length === 0) {
      return {
        success: false,
        message: '公司信息不存在或无权限修改'
      }
    }
    
    // 构建更新数据
    const updateData = {
      updated_at: new Date()
    }
    
    if (data.name) updateData.name = data.name
    if (data.contactPerson !== undefined) updateData.contact_person = data.contactPerson
    if (data.contactPhone !== undefined) updateData.contact_phone = data.contactPhone
    if (data.city !== undefined) updateData.city = data.city
    if (data.level !== undefined) updateData.level = data.level
    
    // 工厂特有字段
    if (type === 'factory' && data.specialties !== undefined) {
      updateData.specialties = data.specialties
    }
    
    // 更新公司信息
    await db.collection(collection).doc(companyId).update({
      data: updateData
    })
    
    // 获取更新后的数据
    const { data: updatedCompanies } = await db.collection(collection).where({
      _id: companyId
    }).get()
    
    return {
      success: true,
      message: `更新${type === 'factory' ? '工厂' : '设计公司'}信息成功`,
      data: updatedCompanies[0]
    }
  } catch (error) {
    console.error('更新公司信息失败:', error)
    throw error
  }
}

/**
 * 获取公司详情
 */
async function getCompanyDetail(openid, params) {
  const { type, companyId } = params
  
  try {
    const collection = type === 'factory' ? 'factories' : 'design_companies'
    
    let whereConditions
    if (companyId) {
      // 获取指定公司详情
      whereConditions = {
        _id: companyId,
        is_deleted: 0
      }
    } else {
      // 获取当前用户的公司信息
      whereConditions = {
        _openid: openid,
        is_deleted: 0
      }
    }
    
    const { data: companies } = await db.collection(collection).where(whereConditions).get()
    
    if (companies.length === 0) {
      return {
        success: false,
        message: '公司信息不存在'
      }
    }
    
    const company = companies[0]
    
    // 格式化返回数据
    const companyInfo = {
      id: company._id,
      name: company.name,
      contactPerson: company.contact_person,
      contactPhone: company.contact_phone,
      city: company.city,
      level: company.level,
      type: type,
      createdAt: company.created_at,
      updatedAt: company.updated_at
    }
    
    // 工厂特有字段
    if (type === 'factory') {
      companyInfo.specialties = company.specialties
    }
    
    return {
      success: true,
      data: companyInfo
    }
  } catch (error) {
    console.error('获取公司详情失败:', error)
    throw error
  }
}
