// 云函数：获取展会列表
const cloud = require('wx-server-sdk')

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

/**
 * 获取展会列表云函数
 * @param {Object} event - 云函数入参
 * @param {string} event.type - 查询类型：'my'我的展会, 'available'可参加的展会, 'recent'近期展会
 * @param {number} event.page - 页码
 * @param {number} event.pageSize - 每页数量
 * @param {string} event.keyword - 搜索关键字
 * @param {string} event.city - 城市筛选
 * @param {number} event.limit - 限制数量（用于recent类型）
 */
exports.main = async (event, context) => {
  console.log('获取展会列表云函数被调用，参数:', event)
  
  try {
    const wxContext = cloud.getWXContext()
    const { OPENID } = wxContext
    
    const {
      type = 'available',
      page = 1,
      pageSize = 10,
      keyword,
      city,
      limit = 10,
      id
    } = event

    switch (type) {
      case 'my':
        return await getMyExhibitions(OPENID, { page, pageSize, keyword })
      case 'available':
        return await getAvailableExhibitions(OPENID, { page, pageSize, keyword, city })
      case 'recent':
        return await getRecentExhibitions({ limit, city })
      case 'single':
        return await getSingleExhibition(id)
      default:
        throw new Error('无效的查询类型')
    }
  } catch (error) {
    console.error('获取展会列表失败:', error)
    return {
      success: false,
      message: error.message || '获取展会列表失败'
    }
  }
}

/**
 * 获取我的展会列表
 */
async function getMyExhibitions(openid, params) {
  const { page, pageSize, keyword } = params
  
  // 先获取用户参与的展会ID列表
  const participantsCollection = db.collection('exhibition_participants')
  const { data: participants } = await participantsCollection.where({
    _openid: openid,
    is_deleted: 0
  }).get()
  
  if (participants.length === 0) {
    return {
      success: true,
      data: {
        list: [],
        total: 0,
        page,
        pageSize,
        totalPages: 0
      }
    }
  }
  
  const exhibitionIds = participants.map(p => p.exhibition_id)
  
  // 构建查询条件
  const whereConditions = {
    _id: _.in(exhibitionIds),
    is_deleted: 0
  }
  
  if (keyword) {
    whereConditions.name = new RegExp(keyword, 'i')
  }
  
  // 获取总数
  const { total } = await db.collection('exhibitions').where(whereConditions).count()
  
  // 获取分页数据
  const { data: exhibitions } = await db.collection('exhibitions')
    .where(whereConditions)
    .orderBy('start_date', 'asc')
    .skip((page - 1) * pageSize)
    .limit(pageSize)
    .get()
  
  // 获取关联的展馆和类别信息
  const enrichedExhibitions = await enrichExhibitionsData(exhibitions)
  
  return {
    success: true,
    data: {
      list: enrichedExhibitions,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    }
  }
}

/**
 * 获取可参加的展会列表
 */
async function getAvailableExhibitions(openid, params) {
  const { page, pageSize, keyword, city } = params

  // 获取当前日期
  const today = new Date().toISOString().split('T')[0]

  // 构建查询条件 - 只显示今天及以后开始的展会
  const whereConditions = {
    start_date: _.gte(today), // 开始日期大于等于今天
    is_deleted: 0
  }

  if (keyword) {
    whereConditions.name = new RegExp(keyword, 'i')
  }

  if (city) {
    whereConditions.city = city
  }

  // 获取总数
  const { total } = await db.collection('exhibitions').where(whereConditions).count()

  // 获取分页数据
  const { data: exhibitions } = await db.collection('exhibitions')
    .where(whereConditions)
    .orderBy('start_date', 'asc')
    .skip((page - 1) * pageSize)
    .limit(pageSize)
    .get()

  // 获取关联的展馆和类别信息
  const enrichedExhibitions = await enrichExhibitionsData(exhibitions)

  return {
    success: true,
    data: {
      list: enrichedExhibitions,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    }
  }
}

/**
 * 获取近期展会
 */
async function getRecentExhibitions(params) {
  const { limit, city } = params
  
  const whereConditions = {
    start_date: _.gte(new Date().toISOString().split('T')[0]), // 今天及以后
    is_deleted: 0
  }
  
  if (city) {
    whereConditions.city = city
  }
  
  const { data: exhibitions } = await db.collection('exhibitions')
    .where(whereConditions)
    .orderBy('start_date', 'asc')
    .limit(limit)
    .get()
  
  // 获取关联的展馆和类别信息
  const enrichedExhibitions = await enrichExhibitionsData(exhibitions)
  
  return {
    success: true,
    data: enrichedExhibitions
  }
}

/**
 * 获取单个展会详情
 */
async function getSingleExhibition(exhibitionId) {
  if (!exhibitionId) {
    throw new Error('展会ID不能为空')
  }

  try {
    // 获取展会基本信息
    const { data: exhibitions } = await db.collection('exhibitions')
      .where({
        _id: exhibitionId,
        is_deleted: 0
      })
      .get()

    if (exhibitions.length === 0) {
      return {
        success: false,
        message: '展会不存在'
      }
    }

    const exhibition = exhibitions[0]

    // 获取参展人数
    const { total: participantCount } = await db.collection('exhibition_participants')
      .where({
        exhibition_id: exhibitionId,
        is_deleted: 0
      })
      .count()

    // 丰富展会数据
    const enrichedExhibitions = await enrichExhibitionsData([exhibition])
    const enrichedExhibition = enrichedExhibitions[0]

    // 添加参展人数
    enrichedExhibition.participant_count = participantCount

    return {
      success: true,
      data: enrichedExhibition
    }
  } catch (error) {
    console.error('获取单个展会详情失败:', error)
    throw error
  }
}

/**
 * 丰富展会数据（添加展馆和类别信息）
 */
async function enrichExhibitionsData(exhibitions) {
  if (exhibitions.length === 0) return []
  
  // 获取所有需要的展馆和类别ID
  const venueIds = [...new Set(exhibitions.map(e => e.venue_id))]
  const categoryIds = [...new Set(exhibitions.map(e => e.category_id))]
  
  // 批量获取展馆信息
  const { data: venues } = await db.collection('venues')
    .where({
      _id: _.in(venueIds)
    })
    .get()
  
  // 批量获取类别信息
  const { data: categories } = await db.collection('exhibition_categories')
    .where({
      _id: _.in(categoryIds)
    })
    .get()
  
  // 创建映射
  const venueMap = new Map()
  venues.forEach(venue => {
    venueMap.set(venue._id, venue)
  })
  
  const categoryMap = new Map()
  categories.forEach(category => {
    categoryMap.set(category._id, category)
  })
  
  // 丰富展会数据
  return exhibitions.map(exhibition => {
    const venue = venueMap.get(exhibition.venue_id)
    const category = categoryMap.get(exhibition.category_id)
    
    return {
      id: exhibition._id,
      name: exhibition.name,
      startDate: exhibition.start_date,
      endDate: exhibition.end_date,
      status: exhibition.status,
      city: exhibition.city,
      venue: venue ? {
        id: venue._id,
        name: venue.name,
        address: venue.address,
        city: venue.city
      } : null,
      category: category ? {
        id: category._id,
        name: category.name
      } : null,
      createdAt: exhibition.created_at
    }
  })
}
