// 云函数：管理收藏功能
const cloud = require('wx-server-sdk')

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

/**
 * 管理收藏云函数
 * @param {Object} event - 云函数入参
 * @param {string} event.action - 操作类型：'add'添加, 'remove'删除, 'list'获取列表, 'check'检查状态
 * @param {number} event.type - 收藏类型：2设计公司 3工厂 4综合商
 * @param {string} event.targetId - 收藏对象ID
 * @param {string} event.favoriteId - 收藏记录ID（用于删除）
 * @param {number} event.page - 页码（用于列表）
 * @param {number} event.pageSize - 每页数量（用于列表）
 */
exports.main = async (event, context) => {
  console.log('管理收藏云函数被调用，参数:', event)
  
  try {
    const wxContext = cloud.getWXContext()
    const { OPENID } = wxContext
    
    if (!OPENID) {
      return {
        success: false,
        message: '用户身份验证失败'
      }
    }
    
    const { action, type, targetId, favoriteId, page = 1, pageSize = 10 } = event
    
    switch (action) {
      case 'add':
        return await addFavorite(OPENID, { type, targetId })
      case 'remove':
        return await removeFavorite(OPENID, { favoriteId })
      case 'list':
        return await getFavoriteList(OPENID, { type, page, pageSize })
      case 'check':
        return await checkFavoriteStatus(OPENID, { type, targetId })
      default:
        return {
          success: false,
          message: '无效的操作类型'
        }
    }
  } catch (error) {
    console.error('管理收藏失败:', error)
    return {
      success: false,
      message: error.message || '操作失败'
    }
  }
}

/**
 * 添加收藏
 */
async function addFavorite(openid, params) {
  const { type, targetId } = params
  
  try {
    // 验证收藏类型
    if (![2, 3, 4].includes(type)) {
      return {
        success: false,
        message: '无效的收藏类型'
      }
    }
    
    // 验证收藏对象是否存在
    const exists = await checkTargetExists(type, targetId)
    if (!exists) {
      return {
        success: false,
        message: '收藏对象不存在'
      }
    }
    
    // 检查是否已经收藏
    const favoritesCollection = db.collection('user_favorites')
    const { data: existing } = await favoritesCollection.where({
      _openid: openid,
      favorite_type: type,
      favorite_id: targetId,
      is_deleted: 0
    }).get()
    
    if (existing.length > 0) {
      return {
        success: false,
        message: '已经收藏过了'
      }
    }
    
    // 创建收藏记录
    const now = new Date()
    const { _id } = await favoritesCollection.add({
      data: {
        _openid: openid,
        favorite_type: type,
        favorite_id: targetId,
        is_deleted: 0,
        created_at: now
      }
    })
    
    return {
      success: true,
      message: '收藏成功',
      data: {
        favoriteId: _id
      }
    }
  } catch (error) {
    console.error('添加收藏失败:', error)
    throw error
  }
}

/**
 * 取消收藏
 */
async function removeFavorite(openid, params) {
  const { favoriteId } = params
  
  try {
    const favoritesCollection = db.collection('user_favorites')
    
    // 查找收藏记录
    const { data: favorites } = await favoritesCollection.where({
      _id: favoriteId,
      _openid: openid,
      is_deleted: 0
    }).get()
    
    if (favorites.length === 0) {
      return {
        success: false,
        message: '收藏记录不存在'
      }
    }
    
    // 软删除收藏记录
    await favoritesCollection.doc(favoriteId).update({
      data: {
        is_deleted: 1,
        updated_at: new Date()
      }
    })
    
    return {
      success: true,
      message: '取消收藏成功'
    }
  } catch (error) {
    console.error('取消收藏失败:', error)
    throw error
  }
}

/**
 * 获取收藏列表
 */
async function getFavoriteList(openid, params) {
  const { type, page, pageSize } = params
  
  try {
    const whereConditions = {
      _openid: openid,
      is_deleted: 0
    }
    
    if (type) {
      whereConditions.favorite_type = type
    }
    
    // 获取总数
    const { total } = await db.collection('user_favorites').where(whereConditions).count()
    
    // 获取分页数据
    const { data: favorites } = await db.collection('user_favorites')
      .where(whereConditions)
      .orderBy('created_at', 'desc')
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .get()
    
    // 获取收藏的详细信息
    const enrichedFavorites = await enrichFavoritesData(favorites)
    
    return {
      success: true,
      data: {
        list: enrichedFavorites,
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize)
      }
    }
  } catch (error) {
    console.error('获取收藏列表失败:', error)
    throw error
  }
}

/**
 * 检查收藏状态
 */
async function checkFavoriteStatus(openid, params) {
  const { type, targetId } = params
  
  try {
    const { data: favorites } = await db.collection('user_favorites').where({
      _openid: openid,
      favorite_type: type,
      favorite_id: targetId,
      is_deleted: 0
    }).get()
    
    return {
      success: true,
      data: {
        isFavorited: favorites.length > 0,
        favoriteId: favorites.length > 0 ? favorites[0]._id : null
      }
    }
  } catch (error) {
    console.error('检查收藏状态失败:', error)
    return {
      success: true,
      data: {
        isFavorited: false,
        favoriteId: null
      }
    }
  }
}

/**
 * 检查收藏对象是否存在
 */
async function checkTargetExists(type, targetId) {
  try {
    switch (type) {
      case 2: // 设计公司
        const { data: designCompanies } = await db.collection('design_companies').where({
          _id: targetId,
          is_deleted: 0
        }).get()
        return designCompanies.length > 0
        
      case 3: // 工厂
        const { data: factories } = await db.collection('factories').where({
          _id: targetId,
          is_deleted: 0
        }).get()
        return factories.length > 0
        
      case 4: // 综合商
        const { data: designs } = await db.collection('design_companies').where({
          _id: targetId,
          is_deleted: 0
        }).get()
        const { data: factoriesComp } = await db.collection('factories').where({
          _id: targetId,
          is_deleted: 0
        }).get()
        return designs.length > 0 || factoriesComp.length > 0
        
      default:
        return false
    }
  } catch (error) {
    console.error('检查收藏对象存在性失败:', error)
    return false
  }
}

/**
 * 丰富收藏数据
 */
async function enrichFavoritesData(favorites) {
  if (favorites.length === 0) return []
  
  const enrichedFavorites = []
  
  for (const favorite of favorites) {
    const { favorite_type, favorite_id } = favorite
    let detail = null
    
    try {
      switch (favorite_type) {
        case 2: // 设计公司
          const { data: designCompanies } = await db.collection('design_companies').where({
            _id: favorite_id,
            is_deleted: 0
          }).get()
          
          if (designCompanies.length > 0) {
            const company = designCompanies[0]
            detail = {
              id: favorite._id,
              type: 'design',
              favoriteType: favorite_type,
              targetId: favorite_id,
              name: company.name,
              contactPerson: company.contact_person,
              contactPhone: company.contact_phone,
              city: company.city,
              level: company.level,
              createdAt: favorite.created_at
            }
          }
          break
          
        case 3: // 工厂
          const { data: factories } = await db.collection('factories').where({
            _id: favorite_id,
            is_deleted: 0
          }).get()
          
          if (factories.length > 0) {
            const factory = factories[0]
            detail = {
              id: favorite._id,
              type: 'factory',
              favoriteType: favorite_type,
              targetId: favorite_id,
              name: factory.name,
              contactPerson: factory.contact_person,
              contactPhone: factory.contact_phone,
              city: factory.city,
              specialties: factory.specialties,
              level: factory.level,
              createdAt: favorite.created_at
            }
          }
          break
          
        case 4: // 综合商
          // 这里可以根据需要查询设计公司或工厂信息
          detail = {
            id: favorite._id,
            type: 'comprehensive',
            favoriteType: favorite_type,
            targetId: favorite_id,
            name: '综合商',
            createdAt: favorite.created_at
          }
          break
      }
      
      if (detail) {
        enrichedFavorites.push(detail)
      }
    } catch (error) {
      console.error('获取收藏详情失败:', error)
    }
  }
  
  return enrichedFavorites
}
