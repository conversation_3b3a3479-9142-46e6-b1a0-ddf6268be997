/**
 * 时间格式化 WXS 模块
 * 用于在小程序模板中格式化时间显示
 */

/**
 * 格式化时间显示
 * @param {string} time - 时间字符串
 * @param {string} format - 格式类型：'datetime'|'date'|'relative'
 * @returns {string} 格式化后的时间字符串
 */
function formatTime(time, format) {
  if (!time) return '未知'
  
  // 处理时间字符串
  var timeStr = time.toString()
  var date = getDate(timeStr)
  
  if (!date) return '无效时间'
  
  var now = getDate()
  var diff = now.getTime() - date.getTime()
  
  format = format || 'datetime'
  
  if (format === 'date') {
    // 只显示日期：2025-06-15
    return date.getFullYear() + '-' + 
           padZero(date.getMonth() + 1) + '-' + 
           padZero(date.getDate())
  } else if (format === 'datetime') {
    // 显示日期和时间：2025-06-15 12:30
    return date.getFullYear() + '-' + 
           padZero(date.getMonth() + 1) + '-' + 
           padZero(date.getDate()) + ' ' +
           padZero(date.getHours()) + ':' + 
           padZero(date.getMinutes())
  } else if (format === 'relative') {
    // 相对时间：刚刚、5分钟前、2小时前等
    if (diff < 60000) return '刚刚'
    if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前'
    if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前'
    if (diff < 2592000000) return Math.floor(diff / 86400000) + '天前'
    
    return date.getFullYear() + '-' + 
           padZero(date.getMonth() + 1) + '-' + 
           padZero(date.getDate())
  }
  
  // 默认返回 datetime 格式
  return formatTime(time, 'datetime')
}

/**
 * 补零函数
 * @param {number} num - 数字
 * @returns {string} 补零后的字符串
 */
function padZero(num) {
  return num < 10 ? '0' + num : num.toString()
}

module.exports = {
  formatTime: formatTime
}
