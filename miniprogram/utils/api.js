/**
 * 云开发API工具函数
 * 封装云函数调用，提供统一的错误处理和数据格式
 */

/**
 * 格式化时间显示
 * @param {string|Date} time - 时间字符串或Date对象
 * @param {string} format - 格式类型：'datetime'|'date'|'relative'
 * @returns {string} 格式化后的时间字符串
 */
export function formatTime(time, format = 'datetime') {
  if (!time) return '未知'

  const date = new Date(time)
  if (isNaN(date.getTime())) return '无效时间'

  const now = new Date()
  const diff = now - date

  switch (format) {
    case 'date':
      // 只显示日期：2025-06-15
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`

    case 'datetime':
      // 显示日期和时间：2025-06-15 12:30
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`

    case 'relative':
      // 相对时间：刚刚、5分钟前、2小时前等
      if (diff < 60000) return '刚刚'
      if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
      if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
      if (diff < 2592000000) return `${Math.floor(diff / 86400000)}天前`
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`

    default:
      return formatTime(time, 'datetime')
  }
}

/**
 * 调用云函数的通用方法
 * @param {string} name - 云函数名称
 * @param {Object} data - 传递给云函数的参数
 * @returns {Promise<Object>} 云函数返回结果
 */
export async function callCloudFunction(name, data = {}) {
  try {
    console.log(`调用云函数 ${name}，参数:`, data)
    
    const result = await wx.cloud.callFunction({
      name,
      data
    })
    
    console.log(`云函数 ${name} 返回结果:`, result)
    
    if (result.result.success) {
      return result.result
    } else {
      throw new Error(result.result.message || '云函数调用失败')
    }
  } catch (error) {
    console.error(`云函数 ${name} 调用失败:`, error)
    throw error
  }
}

/**
 * 用户登录
 * @returns {Promise<Object>} 登录结果
 */
export async function login() {
  return await callCloudFunction('login')
}

/**
 * 获取用户信息
 * @returns {Promise<Object>} 用户信息
 */
export async function getUserProfile() {
  // 检查登录状态
  if (!wx.getStorageSync('isLoggedIn')) {
    throw new Error('用户未登录')
  }
  return await callCloudFunction('getUserProfile')
}

/**
 * 更新用户信息
 * @param {Object} userInfo - 要更新的用户信息
 * @param {string} userInfo.nickname - 昵称
 * @param {string} userInfo.avatar - 头像
 * @param {string} userInfo.phone - 手机号
 * @param {string} userInfo.email - 邮箱
 * @param {number} userInfo.userType - 用户类型
 * @returns {Promise<Object>} 更新结果
 */
export async function updateUserProfile(userInfo) {
  const result = await callCloudFunction('updateUserProfile', userInfo)

  // 如果更新成功，同时更新本地缓存
  if (result.success && result.data && result.data.userInfo) {
    wx.setStorageSync('userInfo', result.data.userInfo)
  }

  return result
}

/**
 * 选择用户身份
 * @param {number} userType - 用户类型：1参展商 2设计公司 3工厂 4综合商
 * @param {Object} companyInfo - 公司信息（可选）
 * @returns {Promise<Object>} 选择结果
 */
export async function selectUserType(userType, companyInfo = null) {
  return await callCloudFunction('selectUserType', { userType, companyInfo })
}

/**
 * 修改用户身份
 * @param {number} newUserType - 新的用户类型：1参展商 2设计公司 3工厂 4综合商
 * @param {Object} companyInfo - 公司信息（当切换到需要公司信息的身份时）
 * @returns {Promise<Object>} 修改结果
 */
export async function changeUserType(newUserType, companyInfo = null) {
  return await callCloudFunction('changeUserType', { newUserType, companyInfo })
}

/**
 * 显示错误提示
 * @param {string|Error} error - 错误信息
 */
export function showError(error) {
  const message = typeof error === 'string' ? error : error.message || '操作失败'
  wx.showToast({
    title: message,
    icon: 'error',
    duration: 2000
  })
}

/**
 * 显示成功提示
 * @param {string} message - 成功信息
 */
export function showSuccess(message) {
  wx.showToast({
    title: message,
    icon: 'success',
    duration: 2000
  })
}

/**
 * 显示加载提示
 * @param {string} title - 加载文字
 */
export function showLoading(title = '加载中...') {
  wx.showLoading({
    title,
    mask: true
  })
}

/**
 * 隐藏加载提示
 */
export function hideLoading() {
  wx.hideLoading()
}

/**
 * 获取本地存储的用户信息
 * @returns {Object|null} 用户信息
 */
export function getStoredUserInfo() {
  try {
    return wx.getStorageSync('userInfo')
  } catch (error) {
    console.error('获取本地用户信息失败:', error)
    return null
  }
}

/**
 * 获取本地存储的token
 * @returns {string|null} token
 */
export function getStoredToken() {
  try {
    return wx.getStorageSync('token')
  } catch (error) {
    console.error('获取本地token失败:', error)
    return null
  }
}

/**
 * 清除本地存储的用户数据
 */
export function clearStoredUserData() {
  try {
    wx.removeStorageSync('userInfo')
    wx.removeStorageSync('token')
  } catch (error) {
    console.error('清除本地用户数据失败:', error)
  }
}

/**
 * 检查用户是否已登录
 * @returns {boolean} 是否已登录
 */
export function isLoggedIn() {
  const userInfo = getStoredUserInfo()
  const token = getStoredToken()
  return !!(userInfo && token)
}

// ==================== 展会相关API ====================

/**
 * 获取展会列表
 * @param {Object} params 查询参数
 * @param {string} params.type - 查询类型：'my'我的展会, 'available'可参加的展会, 'recent'近期展会
 * @param {number} params.page - 页码
 * @param {number} params.pageSize - 每页数量
 * @param {string} params.keyword - 搜索关键字
 * @param {string} params.city - 城市筛选
 * @param {number} params.limit - 限制数量（用于recent类型）
 * @returns {Promise<Object>} 展会列表
 */
export async function getExhibitions(params) {
  return await callCloudFunction('getExhibitions', params)
}

/**
 * 管理展会参与
 * @param {Object} params 参与参数
 * @param {string} params.action - 操作类型：'join'参加, 'leave'退出
 * @param {string} params.exhibitionId - 展会ID
 * @param {number} params.participantType - 参展类型：2设计公司 3工厂 4综合商
 * @param {string} params.participantId - 参展方ID
 * @returns {Promise<Object>} 操作结果
 */
export async function manageExhibitionParticipation(params) {
  return await callCloudFunction('manageExhibitionParticipation', params)
}

// ==================== 公司搜索相关API ====================

/**
 * 搜索公司
 * @param {Object} params 搜索参数
 * @param {string} params.type - 搜索类型：'factory'工厂, 'design'设计公司
 * @param {number} params.page - 页码
 * @param {number} params.pageSize - 每页数量
 * @param {string} params.keyword - 搜索关键字
 * @param {string} params.city - 城市筛选
 * @param {number} params.level - 级别筛选
 * @param {string} params.exhibitionId - 展会ID（筛选参展公司）
 * @returns {Promise<Object>} 搜索结果
 */
export async function searchCompanies(params) {
  return await callCloudFunction('searchCompanies', params)
}

// ==================== 收藏相关API ====================

/**
 * 管理收藏
 * @param {Object} params 收藏参数
 * @param {string} params.action - 操作类型：'add'添加, 'remove'删除, 'list'获取列表, 'check'检查状态
 * @param {number} params.type - 收藏类型：2设计公司 3工厂 4综合商
 * @param {string} params.targetId - 收藏对象ID
 * @param {string} params.favoriteId - 收藏记录ID（用于删除）
 * @param {number} params.page - 页码（用于列表）
 * @param {number} params.pageSize - 每页数量（用于列表）
 * @returns {Promise<Object>} 操作结果
 */
export async function manageFavorites(params) {
  return await callCloudFunction('manageFavorites', params)
}

// ==================== 公司管理相关API ====================

/**
 * 管理公司信息
 * @param {Object} params 公司参数
 * @param {string} params.action - 操作类型：'create'创建, 'update'更新, 'get'获取详情
 * @param {string} params.type - 公司类型：'factory'工厂, 'design'设计公司
 * @param {string} params.companyId - 公司ID（用于更新和获取）
 * @param {Object} params.data - 公司数据
 * @returns {Promise<Object>} 操作结果
 */
export async function manageCompany(params) {
  return await callCloudFunction('manageCompany', params)
}

// ==================== 基础数据相关API ====================

/**
 * 获取基础数据
 * @param {Object} params 查询参数
 * @param {string} params.type - 数据类型：'venues'展馆, 'categories'展会类别, 'all'所有基础数据
 * @returns {Promise<Object>} 基础数据
 */
export async function getBaseData(params = {}) {
  return await callCloudFunction('getBaseData', params)
}

// ==================== 便捷方法 ====================

/**
 * 获取我的展会列表
 * @param {Object} params 查询参数
 * @returns {Promise<Object>} 我的展会列表
 */
export async function getMyExhibitions(params = {}) {
  return await getExhibitions({ ...params, type: 'my' })
}

/**
 * 获取可参加的展会列表
 * @param {Object} params 查询参数
 * @returns {Promise<Object>} 可参加的展会列表
 */
export async function getAvailableExhibitions(params = {}) {
  return await getExhibitions({ ...params, type: 'available' })
}

/**
 * 获取近期展会
 * @param {Object} params 查询参数
 * @returns {Promise<Object>} 近期展会列表
 */
export async function getRecentExhibitions(params = {}) {
  return await getExhibitions({ ...params, type: 'recent' })
}

/**
 * 参加展会
 * @param {string} exhibitionId 展会ID
 * @param {Object} options 选项
 * @returns {Promise<Object>} 操作结果
 */
export async function joinExhibition(exhibitionId, options = {}) {
  return await manageExhibitionParticipation({
    action: 'join',
    exhibitionId,
    ...options
  })
}

/**
 * 退出展会
 * @param {string} exhibitionId 展会ID
 * @returns {Promise<Object>} 操作结果
 */
export async function leaveExhibition(exhibitionId) {
  return await manageExhibitionParticipation({
    action: 'leave',
    exhibitionId
  })
}

/**
 * 搜索工厂
 * @param {Object} params 搜索参数
 * @returns {Promise<Object>} 工厂列表
 */
export async function searchFactories(params = {}) {
  return await searchCompanies({ ...params, type: 'factory' })
}

/**
 * 搜索设计公司
 * @param {Object} params 搜索参数
 * @returns {Promise<Object>} 设计公司列表
 */
export async function searchDesignCompanies(params = {}) {
  return await searchCompanies({ ...params, type: 'design' })
}

/**
 * 添加收藏
 * @param {number} type 收藏类型
 * @param {string} targetId 收藏对象ID
 * @returns {Promise<Object>} 操作结果
 */
export async function addFavorite(type, targetId) {
  return await manageFavorites({
    action: 'add',
    type,
    targetId
  })
}

/**
 * 取消收藏
 * @param {string} favoriteId 收藏记录ID
 * @returns {Promise<Object>} 操作结果
 */
export async function removeFavorite(favoriteId) {
  return await manageFavorites({
    action: 'remove',
    favoriteId
  })
}

/**
 * 获取收藏列表
 * @param {Object} params 查询参数
 * @returns {Promise<Object>} 收藏列表
 */
export async function getFavoriteList(params = {}) {
  return await manageFavorites({ ...params, action: 'list' })
}

/**
 * 检查收藏状态
 * @param {number} type 收藏类型
 * @param {string} targetId 收藏对象ID
 * @returns {Promise<Object>} 收藏状态
 */
export async function checkFavoriteStatus(type, targetId) {
  return await manageFavorites({
    action: 'check',
    type,
    targetId
  })
}

/**
 * 获取公司详情
 * @param {string} type 公司类型 'factory' 或 'design'
 * @param {string} companyId 公司ID
 * @returns {Promise<Object>} 公司详情
 */
export async function getCompanyDetail(type, companyId) {
  return await manageCompany({
    action: 'get',
    type,
    companyId
  })
}

/**
 * 取消展会参与
 * @param {string} exhibitionId 展会ID
 * @returns {Promise<Object>} 操作结果
 */
export async function cancelExhibition(exhibitionId) {
  return await manageExhibitionParticipation({
    action: 'leave',
    exhibitionId
  })
}

/**
 * 退出登录
 * @returns {Promise<Object>} 操作结果
 */
export async function logout() {
  try {
    // 清除本地存储
    clearStoredUserData()

    // 可以调用云函数进行服务端登出处理
    // return await callCloudFunction('logout')

    return { success: true, message: '退出成功' }
  } catch (error) {
    console.error('退出登录失败:', error)
    throw error
  }
}

/**
 * 城市管理云函数调用
 * @param {Object} params 参数
 * @returns {Promise<Object>} 操作结果
 */
async function manageCity(params) {
  return await callCloudFunction('manageCity', params)
}

/**
 * 获取城市列表
 * @param {Object} params 查询参数
 * @returns {Promise<Object>} 城市列表
 */
export async function getCityList(params = {}) {
  return await manageCity({
    action: 'list',
    ...params
  })
}

/**
 * 初始化默认城市数据
 * @returns {Promise<Object>} 操作结果
 */
export async function initDefaultCities() {
  return await manageCity({
    action: 'init'
  })
}
