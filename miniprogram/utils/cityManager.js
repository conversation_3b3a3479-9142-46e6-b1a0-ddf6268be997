// 城市数据管理工具
import { callCloudFunction } from './api.js'

/**
 * 城市数据管理器
 */
class CityManager {
  constructor() {
    this.cities = []
    this.isLoaded = false
    this.isLoading = false
    this.cacheKey = 'cities_cache'
    this.cacheExpiry = 24 * 60 * 60 * 1000 // 24小时缓存
  }

  /**
   * 获取城市列表
   * @param {boolean} forceRefresh - 是否强制刷新
   * @returns {Promise<Array>} 城市列表
   */
  async getCities(forceRefresh = false) {
    // 如果已经加载且不强制刷新，直接返回缓存数据
    if (this.isLoaded && !forceRefresh) {
      return this.cities
    }

    // 如果正在加载，等待加载完成
    if (this.isLoading) {
      return new Promise((resolve) => {
        const checkLoading = () => {
          if (!this.isLoading) {
            resolve(this.cities)
          } else {
            setTimeout(checkLoading, 100)
          }
        }
        checkLoading()
      })
    }

    try {
      this.isLoading = true

      // 尝试从本地缓存获取
      if (!forceRefresh) {
        const cachedData = this.getCachedCities()
        if (cachedData) {
          this.cities = cachedData
          this.isLoaded = true
          this.isLoading = false
          return this.cities
        }
      }

      // 从服务器获取数据
      const result = await callCloudFunction('initCities', { action: 'get' })
      
      if (result.success) {
        this.cities = result.data
        this.isLoaded = true
        
        // 缓存到本地
        this.setCachedCities(this.cities)
        
        return this.cities
      } else {
        throw new Error(result.message || '获取城市数据失败')
      }
    } catch (error) {
      console.error('获取城市数据失败:', error)
      
      // 如果网络请求失败，尝试使用缓存数据
      const cachedData = this.getCachedCities()
      if (cachedData) {
        this.cities = cachedData
        this.isLoaded = true
        return this.cities
      }
      
      throw error
    } finally {
      this.isLoading = false
    }
  }

  /**
   * 获取城市选择器数据格式
   * @param {boolean} forceRefresh - 是否强制刷新
   * @returns {Promise<Array>} 选择器格式的城市数据
   */
  async getCityColumns(forceRefresh = false) {
    const cities = await this.getCities(forceRefresh)
    return cities.map(city => ({
      text: city.name,
      value: city.name // 使用城市名称作为值
    }))
  }

  /**
   * 获取热门城市
   * @param {boolean} forceRefresh - 是否强制刷新
   * @returns {Promise<Array>} 热门城市列表
   */
  async getHotCities(forceRefresh = false) {
    const cities = await this.getCities(forceRefresh)
    return cities.filter(city => city.isHot)
  }

  /**
   * 根据名称查找城市
   * @param {string} cityName - 城市名称
   * @returns {Object|null} 城市信息
   */
  async findCityByName(cityName) {
    const cities = await this.getCities()
    return cities.find(city => city.name === cityName) || null
  }

  /**
   * 初始化城市数据（管理员功能）
   * @returns {Promise<Object>} 初始化结果
   */
  async initCities() {
    try {
      const result = await callCloudFunction('initCities', { action: 'init' })
      
      if (result.success) {
        // 清除缓存，强制重新加载
        this.clearCache()
        await this.getCities(true)
      }
      
      return result
    } catch (error) {
      console.error('初始化城市数据失败:', error)
      throw error
    }
  }

  /**
   * 获取缓存的城市数据
   * @returns {Array|null} 缓存的城市数据
   */
  getCachedCities() {
    try {
      const cached = wx.getStorageSync(this.cacheKey)
      if (cached) {
        const { data, timestamp } = JSON.parse(cached)
        
        // 检查缓存是否过期
        if (Date.now() - timestamp < this.cacheExpiry) {
          return data
        }
      }
    } catch (error) {
      console.error('读取城市缓存失败:', error)
    }
    return null
  }

  /**
   * 设置城市数据缓存
   * @param {Array} cities - 城市数据
   */
  setCachedCities(cities) {
    try {
      const cacheData = {
        data: cities,
        timestamp: Date.now()
      }
      wx.setStorageSync(this.cacheKey, JSON.stringify(cacheData))
    } catch (error) {
      console.error('设置城市缓存失败:', error)
    }
  }

  /**
   * 清除缓存
   */
  clearCache() {
    try {
      wx.removeStorageSync(this.cacheKey)
      this.cities = []
      this.isLoaded = false
    } catch (error) {
      console.error('清除城市缓存失败:', error)
    }
  }

  /**
   * 预加载城市数据
   * @returns {Promise<void>}
   */
  async preload() {
    try {
      await this.getCities()
      console.log('城市数据预加载完成')
    } catch (error) {
      console.error('城市数据预加载失败:', error)
    }
  }
}

// 创建单例实例
const cityManager = new CityManager()

// 导出实例和类
export default cityManager
export { CityManager }

// 导出便捷方法
export const getCities = (forceRefresh = false) => cityManager.getCities(forceRefresh)
export const getCityColumns = (forceRefresh = false) => cityManager.getCityColumns(forceRefresh)
export const getHotCities = (forceRefresh = false) => cityManager.getHotCities(forceRefresh)
export const findCityByName = (cityName) => cityManager.findCityByName(cityName)
export const initCities = () => cityManager.initCities()
export const preloadCities = () => cityManager.preload()
