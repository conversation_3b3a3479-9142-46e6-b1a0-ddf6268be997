# 云开发迁移测试指南

## 🎯 迁移完成的功能

### ✅ 已完成迁移的功能：

1. **用户管理系统**
   - 用户登录/注册
   - 用户信息管理（包括用户类型）
   - 用户信息更新

2. **展会管理系统**
   - 展会列表查询（可参加的展会、我的展会、近期展会）
   - 展会参与管理（参加/退出展会）
   - 展会搜索功能

3. **公司管理系统**
   - 工厂信息管理（创建、更新、查询）
   - 设计公司信息管理（创建、更新、查询）
   - 公司搜索功能（支持关键字、城市、级别筛选）

4. **收藏功能**
   - 添加/取消收藏
   - 收藏列表查询
   - 收藏状态检查

5. **基础数据管理**
   - 展会类别数据
   - 展馆数据
   - 基础数据初始化

## 🚀 测试步骤

### 第零步：解决组件库问题

由于项目使用了原生组件替代Vant组件库，无需安装额外依赖。如果遇到组件相关错误：

1. **确保使用原生组件版本** - 展会列表页面已更新为原生组件
2. **如需使用Vant组件库**（可选）：
   ```bash
   # 在miniprogram目录下执行
   npm install @vant/weapp
   ```
   然后在微信开发者工具中：工具 → 构建npm

### 第一步：部署云函数

需要部署以下云函数：

1. **initBaseData** - 初始化基础数据
2. **login** - 用户登录（已存在，已更新）
3. **getUserProfile** - 获取用户信息（已存在，已更新）
4. **updateUserProfile** - 更新用户信息（已存在，已更新）
5. **getExhibitions** - 获取展会列表
6. **manageExhibitionParticipation** - 管理展会参与
7. **searchCompanies** - 搜索公司
8. **manageFavorites** - 管理收藏
9. **manageCompany** - 管理公司信息
10. **getBaseData** - 获取基础数据

#### 部署方法：
1. 在微信开发者工具中，右键点击每个云函数文件夹
2. 选择"上传并部署：云端安装依赖"
3. 等待部署完成

### 第二步：创建数据库集合

需要在云开发控制台创建以下集合，并设置权限规则：

#### 1. users（已存在）
权限规则：
```json
{
  "read": "doc._openid == auth.openid",
  "write": "doc._openid == auth.openid"
}
```

#### 2. exhibition_categories
权限规则：
```json
{
  "read": true,
  "write": false
}
```

#### 3. venues
权限规则：
```json
{
  "read": true,
  "write": false
}
```

#### 4. exhibitions
权限规则：
```json
{
  "read": true,
  "write": false
}
```

#### 5. exhibition_participants
权限规则：
```json
{
  "read": "doc._openid == auth.openid",
  "write": "doc._openid == auth.openid"
}
```

#### 6. factories
权限规则：
```json
{
  "read": true,
  "write": "doc._openid == auth.openid"
}
```

#### 7. design_companies
权限规则：
```json
{
  "read": true,
  "write": "doc._openid == auth.openid"
}
```

#### 8. user_favorites
权限规则：
```json
{
  "read": "doc._openid == auth.openid",
  "write": "doc._openid == auth.openid"
}
```

### 第三步：测试功能

#### 1. 基础功能测试
- [ ] 用户登录功能
- [ ] 基础数据初始化（展会类别、展馆、展会）
- [ ] 用户信息查看和编辑

#### 2. 展会功能测试
- [ ] 查看可参加的展会列表
- [ ] 参加展会
- [ ] 查看我的展会列表
- [ ] 退出展会
- [ ] 展会搜索功能

#### 3. 公司功能测试
- [ ] 创建工厂信息
- [ ] 创建设计公司信息
- [ ] 搜索工厂
- [ ] 搜索设计公司
- [ ] 更新公司信息

#### 4. 收藏功能测试
- [ ] 添加收藏（工厂/设计公司）
- [ ] 查看收藏列表
- [ ] 取消收藏
- [ ] 检查收藏状态

## 📊 数据结构对比

### 原MySQL表 → 云数据库集合

| MySQL表 | 云数据库集合 | 主要变化 |
|---------|-------------|----------|
| users | users | 使用_openid替代user_id关联 |
| exhibition_categories | exhibition_categories | 基本一致 |
| venues | venues | 基本一致 |
| exhibitions | exhibitions | 基本一致 |
| factories | factories | 使用_openid替代user_id |
| design_companies | design_companies | 使用_openid替代user_id |
| exhibition_participants | exhibition_participants | 使用_openid替代user_id |
| user_favorites | user_favorites | 使用_openid替代user_id |

## 🔧 API调用示例

### 获取展会列表
```javascript
import { getExhibitions } from '../../utils/api.js'

// 获取可参加的展会
const result = await getExhibitions({
  type: 'available',
  page: 1,
  pageSize: 10,
  keyword: '科技展'
})
```

### 搜索工厂
```javascript
import { searchCompanies } from '../../utils/api.js'

const result = await searchCompanies({
  type: 'factory',
  page: 1,
  pageSize: 10,
  city: '上海',
  level: 5
})
```

### 管理收藏
```javascript
import { manageFavorites } from '../../utils/api.js'

// 添加收藏
await manageFavorites({
  action: 'add',
  type: 3, // 工厂
  targetId: 'factory_id'
})

// 获取收藏列表
const favorites = await manageFavorites({
  action: 'list',
  page: 1,
  pageSize: 10
})
```

## ⚠️ 注意事项

1. **权限设置**：确保所有集合的权限规则正确设置
2. **数据初始化**：首次运行时会自动初始化基础数据
3. **用户类型**：新用户默认为参展商（type=1），可在个人信息中修改
4. **关联查询**：云数据库不支持JOIN，使用多次查询实现关联
5. **分页查询**：所有列表查询都支持分页

## 🎉 迁移优势

1. **无需域名备案** - 直接使用微信云开发域名
2. **自动扩容** - 无需担心并发和性能问题
3. **成本更低** - 按使用量计费
4. **维护简单** - 无需服务器运维
5. **安全性高** - 微信官方认证，数据隔离
