# 馆拼拼后台管理系统 - 测试版本

## 🎯 测试目标

本次测试主要验证后台管理系统的以下功能：
- ✅ 管理员登录认证
- ✅ 数据统计展示
- ✅ 用户信息管理
- ✅ 展会信息管理
- ✅ 公司信息管理
- ✅ 参展信息管理
- ✅ 响应式界面设计

## 🚀 快速开始

### 方式一：一键启动（推荐）

**Linux/macOS:**
```bash
./start-test.sh
```

**Windows:**
```cmd
start-test.bat
```

### 方式二：手动启动

1. **配置数据库**
   ```bash
   # 编辑数据库配置
   vim backend/.env
   # 设置 DB_PASSWORD=你的MySQL密码
   ```

2. **启动后端服务**
   ```bash
   cd backend
   npm install
   npm run migrate  # 创建数据表
   npm run seed     # 插入测试数据
   npm run dev      # 启动服务
   ```

3. **启动后台管理**
   ```bash
   # 新开终端
   cd admin
   npm install
   npm run dev
   ```

4. **测试API（可选）**
   ```bash
   node test-api.js
   ```

## 🔑 登录信息

- **后台管理地址**: http://localhost:5174
- **用户名**: admin
- **密码**: gpp13141234

## 📊 测试数据

系统已预置完整的测试数据：

### 用户数据（4个）
- 参展商：测试参展商 (13800138001)
- 设计公司：测试设计公司 (13800138002)
- 工厂：测试工厂 (13800138003)
- 综合商：测试综合商 (13800138004)

### 展会数据（8个）
- 第4届上海网络安全博览会
- GPOWER2025动力展
- 2025润滑油聚焦产业周论坛推介会
- 上海世环会 节能环保低碳舒适系统展
- 2025设计上海展
- 2025北京国际汽车展览会
- 2025深圳国际服装供应链博览会
- 2025广州国际家具博览会

### 公司数据（5个）
- 上海YY设计有限公司（设计公司）
- 北京ZZ创意设计公司（设计公司）
- 深圳AA搭建工厂（工厂）
- 广州BB展览工厂（工厂）
- 上海XX贸易有限公司（参展商）

## 🧪 测试清单

### 基础功能测试
- [ ] 访问登录页面
- [ ] 使用正确账号密码登录
- [ ] 验证登录成功跳转
- [ ] 测试退出登录

### 首页仪表板测试
- [ ] 查看统计卡片（用户数、展会数、公司数、展馆数）
- [ ] 验证统计数据准确性
- [ ] 查看最近活动时间线

### 用户管理测试
- [ ] 查看用户列表
- [ ] 测试用户搜索（昵称、手机号）
- [ ] 测试用户类型筛选
- [ ] 测试分页功能
- [ ] 验证用户类型标签

### 展会管理测试
- [ ] 查看展会列表
- [ ] 测试展会搜索（名称、展馆）
- [ ] 测试状态筛选
- [ ] 验证展会状态标签
- [ ] 测试分页功能

### 展馆管理测试
- [ ] 查看展馆列表
- [ ] 验证展馆信息完整性

### 展会类别测试
- [ ] 查看类别列表
- [ ] 验证类别信息

### 公司管理测试
- [ ] 查看公司列表
- [ ] 测试公司类型筛选
- [ ] 测试公司名称搜索
- [ ] 验证不同类型标签

### 参展管理测试
- [ ] 查看参展列表
- [ ] 测试展会筛选
- [ ] 测试参展方类型筛选
- [ ] 验证参展状态

### 数据统计测试
- [ ] 查看统计数据
- [ ] 验证热门展会排行

### 界面响应式测试
- [ ] 桌面端显示
- [ ] 平板端显示
- [ ] 手机端显示

## 🔍 API测试

运行API测试脚本：
```bash
node test-api.js
```

预期输出：
```
🧪 开始测试API...

1. 测试健康检查...
✅ 健康检查通过: OK

2. 测试管理员登录...
✅ 管理员登录成功

3. 测试获取统计数据...
✅ 统计数据获取成功:
   用户总数: 4
   展会总数: 8
   公司总数: 5
   展馆总数: 8

🎉 所有API测试通过！
```

## 🐛 常见问题

### 1. 数据库连接失败
```
Error: connect ECONNREFUSED 127.0.0.1:3306
```
**解决方案**：
- 确保MySQL服务已启动
- 检查 `backend/.env` 中的数据库配置
- 确保数据库密码正确

### 2. 端口被占用
```
Error: listen EADDRINUSE: address already in use :::3001
```
**解决方案**：
- 检查是否有其他服务占用3001端口
- 修改 `backend/.env` 中的 `PORT` 配置

### 3. 前端页面空白
**可能原因**：
- 后端服务未启动
- API请求失败
- 跨域问题

**解决方案**：
- 确保后端服务正常运行
- 检查浏览器控制台错误
- 验证API请求是否成功

### 4. 登录失败
**检查项**：
- 用户名：admin
- 密码：gpp13141234
- 网络连接正常
- 后端服务运行正常

## 📈 性能指标

### 预期性能
- 页面加载时间：< 2秒
- API响应时间：< 500ms
- 数据库查询时间：< 100ms

### 资源使用
- 内存使用：< 200MB
- CPU使用：< 10%
- 数据库连接：< 5个

## 📝 测试反馈

测试完成后，请提供以下反馈：

1. **功能完整性**：所有功能是否正常工作
2. **界面体验**：界面是否美观、易用
3. **性能表现**：页面加载和响应速度
4. **发现的问题**：任何bug或改进建议
5. **整体评价**：对系统的整体印象

## 🎉 测试成功标准

- [ ] 所有页面能正常访问
- [ ] 登录功能正常
- [ ] 数据显示正确
- [ ] 搜索筛选功能正常
- [ ] 分页功能正常
- [ ] 响应式布局正常
- [ ] API测试全部通过
- [ ] 无明显性能问题

---

**祝测试顺利！** 🚀

如有任何问题，请检查：
1. 环境配置
2. 服务启动状态
3. 网络连接
4. 浏览器控制台错误
