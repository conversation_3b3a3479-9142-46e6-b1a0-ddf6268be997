# 馆拼拼后台管理系统测试指南

## 🚀 快速开始

### 环境要求
- Node.js >= 16.0.0
- MySQL >= 5.7
- npm 或 yarn

### 一键启动（推荐）

**Linux/macOS:**
```bash
./start-test.sh
```

**Windows:**
```cmd
start-test.bat
```

### 手动启动

#### 1. 安装依赖
```bash
# 安装后端依赖
cd backend
npm install

# 安装后台管理依赖
cd ../admin
npm install
```

#### 2. 配置数据库
编辑 `backend/.env` 文件，设置数据库连接信息：
```env
DB_HOST=localhost
DB_PORT=3306
DB_NAME=guanpinpin
DB_USER=root
DB_PASSWORD=your_mysql_password
```

#### 3. 初始化数据库
```bash
cd backend
npm run migrate  # 创建数据表
npm run seed     # 插入测试数据
```

#### 4. 启动服务
```bash
# 终端1: 启动后端服务
cd backend
npm run dev

# 终端2: 启动后台管理
cd admin
npm run dev
```

## 🧪 测试内容

### 访问地址
- **后端API**: http://localhost:3001
- **后台管理**: http://localhost:5174

### 登录信息
- **用户名**: admin
- **密码**: gpp13141234

## 📋 功能测试清单

### 1. 登录功能测试
- [ ] 访问 http://localhost:5174
- [ ] 使用正确账号密码登录
- [ ] 验证登录成功跳转到首页
- [ ] 测试错误密码登录失败
- [ ] 测试退出登录功能

### 2. 首页仪表板测试
- [ ] 查看统计卡片数据
- [ ] 验证用户总数、展会总数等统计
- [ ] 查看最近活动列表
- [ ] 验证页面响应式布局

### 3. 用户管理测试
- [ ] 查看用户列表
- [ ] 测试用户搜索功能（昵称、手机号、用户类型）
- [ ] 测试分页功能
- [ ] 查看用户详情
- [ ] 测试用户类型标签显示

### 4. 展会管理测试
- [ ] 查看展会列表
- [ ] 测试展会搜索（名称、展馆、状态）
- [ ] 验证展会状态标签
- [ ] 查看展会详情
- [ ] 测试分页功能

### 5. 展馆管理测试
- [ ] 查看展馆列表
- [ ] 验证展馆信息显示
- [ ] 测试新增展馆按钮
- [ ] 测试编辑/删除功能

### 6. 展会类别测试
- [ ] 查看展会类别列表
- [ ] 验证类别信息
- [ ] 测试管理功能

### 7. 公司管理测试
- [ ] 查看公司列表
- [ ] 测试公司类型筛选
- [ ] 测试公司名称搜索
- [ ] 验证不同类型公司的标签显示
- [ ] 查看公司详情

### 8. 参展管理测试
- [ ] 查看参展列表
- [ ] 测试展会筛选
- [ ] 测试参展方类型筛选
- [ ] 验证参展状态显示
- [ ] 查看参展详情

### 9. 数据统计测试
- [ ] 查看统计卡片
- [ ] 验证各项统计数据
- [ ] 查看热门展会排行
- [ ] 验证图表区域

### 10. 响应式测试
- [ ] 测试桌面端显示
- [ ] 测试平板端显示
- [ ] 测试手机端显示
- [ ] 验证侧边栏收缩功能

## 🔍 API测试

### 健康检查
```bash
curl http://localhost:3001/health
```

### 管理员登录
```bash
curl -X POST http://localhost:3001/api/auth/admin-login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"gpp13141234"}'
```

### 获取统计数据
```bash
# 先获取token，然后使用token访问
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:3001/api/admin/stats
```

## 🐛 常见问题

### 1. 数据库连接失败
**问题**: `数据库连接失败` 或 `ECONNREFUSED`
**解决**:
- 确保MySQL服务已启动
- 检查 `backend/.env` 文件中的数据库配置
- 确保数据库用户有足够权限

### 2. 端口被占用
**问题**: `EADDRINUSE: address already in use`
**解决**:
- 后端端口3001被占用：修改 `backend/.env` 中的 `PORT`
- 前端端口5174被占用：修改 `admin/vite.config.js` 中的端口

### 3. 依赖安装失败
**问题**: `npm install` 失败
**解决**:
- 尝试清除缓存：`npm cache clean --force`
- 删除 `node_modules` 重新安装
- 使用淘宝镜像：`npm install --registry=https://registry.npmmirror.com`

### 4. 登录后白屏
**问题**: 登录成功但页面空白
**解决**:
- 检查浏览器控制台错误
- 确保后端API正常运行
- 检查网络请求是否成功

### 5. 数据不显示
**问题**: 页面正常但数据为空
**解决**:
- 确保已执行 `npm run seed` 插入测试数据
- 检查API请求是否成功
- 查看后端日志是否有错误

## 📊 测试数据说明

系统已预置以下测试数据：

### 用户数据
- 4个测试用户（参展商、设计公司、工厂、综合商各1个）
- 用户类型完整，便于测试不同角色

### 展会数据
- 8个测试展会
- 涵盖不同城市、不同类别
- 包含未开始、进行中、已结束等状态

### 公司数据
- 2个设计公司
- 2个工厂
- 1个参展商
- 包含完整的联系信息和等级

### 参展数据
- 8条参展记录
- 涵盖不同展会和参展方类型
- 包含满额和可预约状态

## 🎯 测试重点

1. **功能完整性**: 所有页面和功能都能正常访问
2. **数据准确性**: 统计数据和列表数据显示正确
3. **交互体验**: 搜索、分页、筛选等交互正常
4. **响应式设计**: 在不同设备上显示正常
5. **错误处理**: 网络错误、权限错误等处理得当

## 📝 测试报告

测试完成后，请记录：
- [ ] 测试环境信息
- [ ] 功能测试结果
- [ ] 发现的问题和建议
- [ ] 性能表现
- [ ] 用户体验评价

---

如有问题，请检查：
1. 环境配置是否正确
2. 服务是否正常启动
3. 网络连接是否正常
4. 浏览器控制台是否有错误信息
