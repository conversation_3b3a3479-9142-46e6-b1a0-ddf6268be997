// 城市管理云函数
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  const { action, cityData, cityId } = event
  
  try {
    switch (action) {
      case 'list':
        return await getCityList(event)
      case 'add':
        return await addCity(cityData)
      case 'update':
        return await updateCity(cityId, cityData)
      case 'delete':
        return await deleteCity(cityId)
      case 'init':
        return await initDefaultCities()
      default:
        return {
          success: false,
          message: '无效的操作类型'
        }
    }
  } catch (error) {
    console.error('城市管理失败:', error)
    return {
      success: false,
      message: error.message || '操作失败'
    }
  }
}

// 获取城市列表
async function getCityList(params) {
  const { page = 1, pageSize = 50 } = params
  
  const result = await db.collection('cities')
    .orderBy('sort', 'asc')
    .orderBy('name', 'asc')
    .skip((page - 1) * pageSize)
    .limit(pageSize)
    .get()
  
  const total = await db.collection('cities').count()
  
  return {
    success: true,
    data: {
      list: result.data,
      total: total.total,
      page,
      pageSize,
      totalPages: Math.ceil(total.total / pageSize)
    }
  }
}

// 添加城市
async function addCity(cityData) {
  const { name, province, sort = 999 } = cityData
  
  // 检查城市是否已存在
  const existing = await db.collection('cities')
    .where({ name })
    .get()
  
  if (existing.data.length > 0) {
    return {
      success: false,
      message: '城市已存在'
    }
  }
  
  const result = await db.collection('cities').add({
    data: {
      name,
      province,
      sort,
      status: 1, // 1-启用 0-禁用
      createdAt: new Date(),
      updatedAt: new Date()
    }
  })
  
  return {
    success: true,
    data: { cityId: result._id },
    message: '添加成功'
  }
}

// 更新城市
async function updateCity(cityId, cityData) {
  const result = await db.collection('cities')
    .doc(cityId)
    .update({
      data: {
        ...cityData,
        updatedAt: new Date()
      }
    })
  
  return {
    success: true,
    message: '更新成功'
  }
}

// 删除城市
async function deleteCity(cityId) {
  const result = await db.collection('cities')
    .doc(cityId)
    .remove()
  
  return {
    success: true,
    message: '删除成功'
  }
}

// 初始化默认城市数据
async function initDefaultCities() {
  const defaultCities = [
    { name: '北京', province: '北京市', sort: 1 },
    { name: '上海', province: '上海市', sort: 2 },
    { name: '广州', province: '广东省', sort: 3 },
    { name: '深圳', province: '广东省', sort: 4 },
    { name: '杭州', province: '浙江省', sort: 5 },
    { name: '南京', province: '江苏省', sort: 6 },
    { name: '武汉', province: '湖北省', sort: 7 },
    { name: '成都', province: '四川省', sort: 8 },
    { name: '西安', province: '陕西省', sort: 9 },
    { name: '重庆', province: '重庆市', sort: 10 },
    { name: '天津', province: '天津市', sort: 11 },
    { name: '青岛', province: '山东省', sort: 12 },
    { name: '大连', province: '辽宁省', sort: 13 },
    { name: '厦门', province: '福建省', sort: 14 },
    { name: '苏州', province: '江苏省', sort: 15 }
  ]
  
  // 清空现有数据
  await db.collection('cities').where({}).remove()
  
  // 批量添加
  const promises = defaultCities.map(city => 
    db.collection('cities').add({
      data: {
        ...city,
        status: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    })
  )
  
  await Promise.all(promises)
  
  return {
    success: true,
    message: `初始化完成，添加了${defaultCities.length}个城市`,
    data: { count: defaultCities.length }
  }
}
