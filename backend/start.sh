#!/bin/sh

echo "🚀 启动馆拼拼后端服务..."
echo "📋 环境信息:"
echo "  NODE_ENV: $NODE_ENV"
echo "  PORT: $PORT"
echo "  DB_HOST: $DB_HOST"
echo "  DB_NAME: $DB_NAME"

# 检查必要的环境变量
if [ -z "$PORT" ]; then
    echo "⚠️  PORT 环境变量未设置，使用默认值 80"
    export PORT=80
fi

if [ -z "$NODE_ENV" ]; then
    echo "⚠️  NODE_ENV 环境变量未设置，使用默认值 production"
    export NODE_ENV=production
fi

echo "🔧 启动 Node.js 应用..."
exec node src/app.js
