# 使用官方Node.js镜像
FROM node:18-alpine

# 安装必要的系统依赖
RUN apk add --no-cache curl

# 设置工作目录
WORKDIR /app

# 复制package.json和package-lock.json
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production && npm cache clean --force

# 复制源代码
COPY src/ ./src/

# 创建logs目录
RUN mkdir -p logs

# 设置权限并切换用户
RUN chown -R node:node /app
USER node

# 暴露端口（使用非特权端口）
EXPOSE 8080

# 设置环境变量
ENV NODE_ENV=production
ENV PORT=8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8080/health || exit 1

# 启动应用
CMD ["node", "src/app.js"]
