import { sequelize } from '../config/database.js'
import { logger } from '../utils/logger.js'
import AuthService from '../services/AuthService.js'
import {
  User,
  ExhibitionCategory,
  Venue,
  Exhibition,
  Factory,
  DesignCompany,
  Exhibitor,
  ExhibitionParticipant,
  Admin
} from '../models/index.js'

async function seed() {
  try {
    logger.info('开始插入种子数据...')
    
    // 1. 插入展会类别
    const categories = await ExhibitionCategory.bulkCreate([
      { name: '服装展' },
      { name: '水展' },
      { name: '汽车展' },
      { name: '科技展' },
      { name: '箱包展' },
      { name: '家具展' },
      { name: '建材展' },
      { name: '食品展' },
      { name: '医疗展' },
      { name: '教育展' }
    ], { ignoreDuplicates: true })
    logger.info('插入展会类别完成')
    
    // 2. 插入展馆
    const venues = await Venue.bulkCreate([
      { city: '上海', name: '新国际博览中心', address: '上海市浦东新区龙阳路2345号' },
      { city: '上海', name: '国家会展中心', address: '上海市青浦区崧泽大道333号' },
      { city: '北京', name: '中国国际展览中心', address: '北京市朝阳区北三环东路6号' },
      { city: '深圳', name: '深圳会展中心', address: '深圳市福田区福华三路111号' },
      { city: '广州', name: '中国进出口商品交易会展馆', address: '广州市海珠区阅江中路380号' },
      { city: '杭州', name: '杭州国际博览中心', address: '杭州市萧山区奔竞大道353号' },
      { city: '成都', name: '中国西部国际博览城', address: '成都市天府新区福州路东段88号' },
      { city: '武汉', name: '武汉国际博览中心', address: '武汉市汉阳区鹦鹉大道619号' }
    ], { ignoreDuplicates: true })
    logger.info('插入展馆完成')
    
    // 3. 插入展会
    const exhibitions = await Exhibition.bulkCreate([
      {
        venue_id: 1,
        name: '第4届上海网络安全博览会',
        start_date: '2025-06-05',
        end_date: '2025-06-07',
        category_id: 4,
        status: 0
      },
      {
        venue_id: 1,
        name: 'GPOWER2025动力展',
        start_date: '2025-06-11',
        end_date: '2025-06-13',
        category_id: 3,
        status: 0
      },
      {
        venue_id: 1,
        name: '2025润滑油聚焦产业周论坛推介会',
        start_date: '2025-06-02',
        end_date: '2025-06-03',
        category_id: 2,
        status: 0
      },
      {
        venue_id: 2,
        name: '上海世环会 节能环保低碳舒适系统展',
        start_date: '2025-06-04',
        end_date: '2025-06-06',
        category_id: 2,
        status: 0
      },
      {
        venue_id: 1,
        name: '2025设计上海展',
        start_date: '2025-06-04',
        end_date: '2025-06-07',
        category_id: 6,
        status: 0
      },
      {
        venue_id: 3,
        name: '2025北京国际汽车展览会',
        start_date: '2025-04-25',
        end_date: '2025-05-04',
        category_id: 3,
        status: 0
      },
      {
        venue_id: 4,
        name: '2025深圳国际服装供应链博览会',
        start_date: '2025-07-10',
        end_date: '2025-07-12',
        category_id: 1,
        status: 0
      },
      {
        venue_id: 5,
        name: '2025广州国际家具博览会',
        start_date: '2025-03-18',
        end_date: '2025-03-21',
        category_id: 6,
        status: 0
      }
    ], { ignoreDuplicates: true })
    logger.info('插入展会完成')
    
    // 4. 插入管理员（加密密码）
    const hashedPassword = await AuthService.hashPassword('gpp13141234')
    await Admin.bulkCreate([
      { username: 'admin', password: hashedPassword }
    ], { ignoreDuplicates: true })
    logger.info('插入管理员完成')
    
    // 5. 插入测试用户
    const users = await User.bulkCreate([
      {
        openid: 'test_openid_1',
        unionid: 'test_unionid_1',
        nickname: '测试参展商',
        phone: '13800138001',
        user_type: 1
      },
      {
        openid: 'test_openid_2',
        unionid: 'test_unionid_2',
        nickname: '测试设计公司',
        phone: '13800138002',
        user_type: 2
      },
      {
        openid: 'test_openid_3',
        unionid: 'test_unionid_3',
        nickname: '测试工厂',
        phone: '13800138003',
        user_type: 3
      },
      {
        openid: 'test_openid_4',
        unionid: 'test_unionid_4',
        nickname: '测试综合商',
        phone: '13800138004',
        user_type: 4
      }
    ], { ignoreDuplicates: true })
    logger.info('插入测试用户完成')
    
    // 6. 插入测试公司数据
    await Exhibitor.bulkCreate([
      {
        user_id: 1,
        name: '上海XX贸易有限公司',
        contact_person: '张经理',
        contact_phone: '13800138001',
        exhibition_category_id: 1
      }
    ], { ignoreDuplicates: true })
    
    await DesignCompany.bulkCreate([
      {
        user_id: 2,
        name: '上海YY设计有限公司',
        contact_person: '李经理',
        contact_phone: '13800138002',
        city: '上海',
        level: 4
      },
      {
        user_id: 4,
        name: '北京ZZ创意设计公司',
        contact_person: '王经理',
        contact_phone: '13800138004',
        city: '北京',
        level: 3
      }
    ], { ignoreDuplicates: true })
    
    await Factory.bulkCreate([
      {
        user_id: 3,
        name: '深圳AA搭建工厂',
        contact_person: '赵经理',
        contact_phone: '13800138003',
        city: '深圳',
        specialties: '钢结构,木结构,特装搭建',
        level: 5
      },
      {
        user_id: 4,
        name: '广州BB展览工厂',
        contact_person: '钱经理',
        contact_phone: '13800138004',
        city: '广州',
        specialties: '标准展位,特装展台',
        level: 4
      }
    ], { ignoreDuplicates: true })
    logger.info('插入测试公司数据完成')
    
    // 7. 插入参展信息
    await ExhibitionParticipant.bulkCreate([
      { exhibition_id: 1, participant_id: 1, participant_type: 2, is_full: 0 },
      { exhibition_id: 1, participant_id: 1, participant_type: 3, is_full: 0 },
      { exhibition_id: 2, participant_id: 1, participant_type: 2, is_full: 0 },
      { exhibition_id: 2, participant_id: 1, participant_type: 3, is_full: 1 },
      { exhibition_id: 3, participant_id: 1, participant_type: 2, is_full: 0 },
      { exhibition_id: 4, participant_id: 1, participant_type: 3, is_full: 0 },
      { exhibition_id: 5, participant_id: 2, participant_type: 2, is_full: 0 },
      { exhibition_id: 5, participant_id: 2, participant_type: 3, is_full: 0 }
    ], { ignoreDuplicates: true })
    logger.info('插入参展信息完成')
    
    logger.info('种子数据插入完成')
    process.exit(0)
  } catch (error) {
    logger.error('插入种子数据失败:', error)
    process.exit(1)
  }
}

seed()
