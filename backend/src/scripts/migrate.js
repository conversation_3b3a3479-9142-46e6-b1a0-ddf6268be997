import { sequelize, testConnection, syncDatabase } from '../config/database.js'
import { logger } from '../utils/logger.js'

// 导入所有模型以确保关联关系被定义
import '../models/index.js'

async function migrate() {
  try {
    logger.info('开始数据库迁移...')
    
    // 测试数据库连接
    await testConnection()
    
    // 同步数据库模型
    await syncDatabase(false) // false表示不强制重建表
    
    logger.info('数据库迁移完成')
    process.exit(0)
  } catch (error) {
    logger.error('数据库迁移失败:', error)
    process.exit(1)
  }
}

migrate()
