import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import rateLimit from 'express-rate-limit'
import dotenv from 'dotenv'
import { fileURLToPath } from 'url'
import { dirname, join } from 'path'

// 导入路由
import authRoutes from './routes/auth.js'
import userRoutes from './routes/user.js'
import exhibitionRoutes from './routes/exhibition.js'
import exhibitionsRoutes from './routes/exhibitions.js'
import factoryRoutes from './routes/factory.js'
import designCompanyRoutes from './routes/designCompany.js'
import searchRoutes from './routes/search.js'
import favoriteRoutes from './routes/favorite.js'
import adminRoutes from './routes/admin.js'

// 导入中间件
import { errorHandler } from './middleware/errorHandler.js'
import { logger } from './utils/logger.js'
import { testConnection } from './config/database.js'

// 导入模型以确保关联关系被定义
import './models/index.js'

// 获取当前文件目录
const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// 加载环境变量
dotenv.config({ path: join(__dirname, '../.env') })

const app = express()
const PORT = process.env.PORT || 3001

// 安全中间件
app.use(helmet())

// 跨域配置
const corsOrigins = process.env.CORS_ORIGIN ?
  process.env.CORS_ORIGIN.split(',') :
  ['http://localhost:5173', 'http://localhost:5174']

app.use(cors({
  origin: corsOrigins,
  credentials: true
}))

// 限流配置
const limiter = rateLimit({
  windowMs: 5000, // 5秒
  max: 15, // 限制每个IP 5秒内最多15个请求（即0.33秒间隔）
  message: {
    code: 429,
    message: '请求过于频繁，请稍后再试'
  }
})
app.use('/api', limiter)

// 解析JSON
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true }))

// 请求日志
app.use((req, res, next) => {
  logger.info(`${req.method} ${req.path} - ${req.ip}`)
  next()
})

// 健康检查
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'ok',
    message: '馆拼拼API服务运行正常',
    data: {
      service: 'guanpinpin-backend',
      version: '1.0.0',
      environment: process.env.NODE_ENV,
      port: process.env.PORT,
      timestamp: new Date().toISOString(),
      uptime: Math.floor(process.uptime()) + 's'
    }
  })
})

// 简单的根路径
app.get('/', (req, res) => {
  res.json({
    message: '馆拼拼API服务',
    version: '1.0.0',
    health: '/health'
  })
})

// API路由
app.use('/api/auth', authRoutes)
app.use('/api/user', userRoutes)
app.use('/api/exhibitions', exhibitionsRoutes)
app.use('/api/exhibition', exhibitionRoutes)
app.use('/api/factory', factoryRoutes)
app.use('/api/design-company', designCompanyRoutes)
app.use('/api/search', searchRoutes)
app.use('/api/favorite', favoriteRoutes)
app.use('/api/admin', adminRoutes)

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    code: 404,
    message: '接口不存在'
  })
})

// 错误处理中间件
app.use(errorHandler)

// 启动服务器
const startServer = () => {
  try {
    app.listen(PORT, '0.0.0.0', () => {
      logger.info(`🚀 服务器启动成功，端口: ${PORT}`)
      logger.info(`🌍 环境: ${process.env.NODE_ENV}`)

      // 异步测试数据库连接，不阻塞启动
      setTimeout(() => {
        testConnection()
          .then(() => logger.info('✅ 数据库连接成功'))
          .catch(error => logger.warn('⚠️  数据库连接失败:', error.message))
      }, 2000) // 延迟2秒再测试数据库
    })
  } catch (error) {
    logger.error('❌ 服务器启动失败:', error)
    process.exit(1)
  }
}

startServer()

// 优雅关闭
process.on('SIGTERM', () => {
  logger.info('收到SIGTERM信号，正在关闭服务器...')
  process.exit(0)
})

process.on('SIGINT', () => {
  logger.info('收到SIGINT信号，正在关闭服务器...')
  process.exit(0)
})

export default app
