import { UserFavorite, Factory, DesignCompany, User } from '../models/index.js'
import { logger } from '../utils/logger.js'

class FavoriteService {
  /**
   * 获取用户收藏列表
   * @param {number} userId 用户ID
   * @param {Object} params 查询参数
   */
  async getUserFavorites(userId, params = {}) {
    try {
      const { type, page = 1, pageSize = 10 } = params
      
      const whereConditions = {
        user_id: userId,
        is_deleted: 0
      }
      
      if (type) {
        whereConditions.favorite_type = type
      }
      
      const offset = (page - 1) * pageSize
      
      const { count, rows } = await UserFavorite.findAndCountAll({
        where: whereConditions,
        order: [['created_at', 'DESC']],
        limit: parseInt(pageSize),
        offset: offset
      })
      
      // 获取收藏的详细信息
      const favorites = await Promise.all(
        rows.map(async (favorite) => {
          const favoriteInfo = await this.getFavoriteDetail(favorite)
          return favoriteInfo
        })
      )
      
      return {
        list: favorites.filter(f => f !== null),
        total: count,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      }
    } catch (error) {
      logger.error('获取收藏列表失败:', error)
      throw new Error('获取收藏列表失败')
    }
  }
  
  /**
   * 添加收藏
   * @param {number} userId 用户ID
   * @param {Object} favoriteData 收藏数据
   */
  async addFavorite(userId, favoriteData) {
    try {
      const { type, id } = favoriteData
      
      // 验证收藏类型
      if (![2, 3, 4].includes(type)) {
        throw new Error('无效的收藏类型')
      }
      
      // 验证收藏对象是否存在
      const exists = await this.checkFavoriteExists(type, id)
      if (!exists) {
        throw new Error('收藏对象不存在')
      }
      
      // 检查是否已经收藏
      const existingFavorite = await UserFavorite.findOne({
        where: {
          user_id: userId,
          favorite_type: type,
          favorite_id: id,
          is_deleted: 0
        }
      })
      
      if (existingFavorite) {
        throw new Error('已经收藏过了')
      }
      
      // 创建收藏记录
      const favorite = await UserFavorite.create({
        user_id: userId,
        favorite_type: type,
        favorite_id: id
      })
      
      return await this.getFavoriteDetail(favorite)
    } catch (error) {
      logger.error('添加收藏失败:', error)
      throw error
    }
  }
  
  /**
   * 取消收藏
   * @param {number} userId 用户ID
   * @param {number} favoriteId 收藏ID
   */
  async removeFavorite(userId, favoriteId) {
    try {
      const favorite = await UserFavorite.findOne({
        where: {
          id: favoriteId,
          user_id: userId,
          is_deleted: 0
        }
      })
      
      if (!favorite) {
        throw new Error('收藏记录不存在')
      }
      
      await favorite.update({ is_deleted: 1 })
      
      return true
    } catch (error) {
      logger.error('取消收藏失败:', error)
      throw error
    }
  }
  
  /**
   * 检查是否已收藏
   * @param {number} userId 用户ID
   * @param {number} type 收藏类型
   * @param {number} id 收藏对象ID
   */
  async checkIsFavorited(userId, type, id) {
    try {
      const favorite = await UserFavorite.findOne({
        where: {
          user_id: userId,
          favorite_type: type,
          favorite_id: id,
          is_deleted: 0
        }
      })
      
      return !!favorite
    } catch (error) {
      logger.error('检查收藏状态失败:', error)
      return false
    }
  }
  
  /**
   * 获取收藏详细信息
   * @param {Object} favorite 收藏记录
   */
  async getFavoriteDetail(favorite) {
    try {
      const { favorite_type, favorite_id } = favorite
      
      let detail = null
      
      switch (favorite_type) {
        case 2: // 设计公司
          detail = await DesignCompany.findOne({
            where: { id: favorite_id, is_deleted: 0 },
            include: [
              {
                model: User,
                as: 'user',
                attributes: ['nickname']
              }
            ]
          })
          
          if (detail) {
            return {
              id: favorite.id,
              type: 'design',
              favoriteType: favorite_type,
              favoriteId: favorite_id,
              name: detail.name,
              contactPerson: detail.contact_person,
              contactPhone: detail.contact_phone,
              city: detail.city,
              level: detail.level,
              createdAt: favorite.created_at
            }
          }
          break
          
        case 3: // 工厂
          detail = await Factory.findOne({
            where: { id: favorite_id, is_deleted: 0 },
            include: [
              {
                model: User,
                as: 'user',
                attributes: ['nickname']
              }
            ]
          })
          
          if (detail) {
            return {
              id: favorite.id,
              type: 'factory',
              favoriteType: favorite_type,
              favoriteId: favorite_id,
              name: detail.name,
              contactPerson: detail.contact_person,
              contactPhone: detail.contact_phone,
              city: detail.city,
              specialties: detail.specialties,
              level: detail.level,
              createdAt: favorite.created_at
            }
          }
          break
          
        case 4: // 综合商（需要同时查询设计公司和工厂）
          const designCompany = await DesignCompany.findOne({
            where: { id: favorite_id, is_deleted: 0 }
          })
          
          const factory = await Factory.findOne({
            where: { id: favorite_id, is_deleted: 0 }
          })
          
          if (designCompany || factory) {
            return {
              id: favorite.id,
              type: 'comprehensive',
              favoriteType: favorite_type,
              favoriteId: favorite_id,
              designCompany: designCompany ? {
                name: designCompany.name,
                contactPerson: designCompany.contact_person,
                contactPhone: designCompany.contact_phone,
                city: designCompany.city,
                level: designCompany.level
              } : null,
              factory: factory ? {
                name: factory.name,
                contactPerson: factory.contact_person,
                contactPhone: factory.contact_phone,
                city: factory.city,
                specialties: factory.specialties,
                level: factory.level
              } : null,
              createdAt: favorite.created_at
            }
          }
          break
      }
      
      return null
    } catch (error) {
      logger.error('获取收藏详情失败:', error)
      return null
    }
  }
  
  /**
   * 检查收藏对象是否存在
   * @param {number} type 收藏类型
   * @param {number} id 收藏对象ID
   */
  async checkFavoriteExists(type, id) {
    try {
      switch (type) {
        case 2: // 设计公司
          const designCompany = await DesignCompany.findOne({
            where: { id, is_deleted: 0 }
          })
          return !!designCompany
          
        case 3: // 工厂
          const factory = await Factory.findOne({
            where: { id, is_deleted: 0 }
          })
          return !!factory
          
        case 4: // 综合商
          const hasDesign = await DesignCompany.findOne({
            where: { id, is_deleted: 0 }
          })
          const hasFactory = await Factory.findOne({
            where: { id, is_deleted: 0 }
          })
          return !!(hasDesign || hasFactory)
          
        default:
          return false
      }
    } catch (error) {
      logger.error('检查收藏对象存在性失败:', error)
      return false
    }
  }
}

export default new FavoriteService()
