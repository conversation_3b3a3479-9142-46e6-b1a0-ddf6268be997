import axios from 'axios'
import bcrypt from 'bcryptjs'
import { User, Factory, DesignCompany, Exhibitor, Admin } from '../models/index.js'
import { generateToken } from '../utils/jwt.js'
import { logger } from '../utils/logger.js'

class AuthService {
  /**
   * 微信云开发登录
   * @param {string} openid 微信openid
   * @param {string} unionid 微信unionid
   */
  async cloudLogin(openid, unionid) {
    try {
      logger.info('开始云开发登录，openid:', openid)

      // 查找或创建用户
      let user = await User.findOne({
        where: { openid: openid, is_deleted: 0 }
      })
      logger.info('查找用户结果:', user ? '找到用户' : '新用户')

      let isNewUser = false

      if (!user) {
        // 新用户，创建用户记录
        logger.info('创建新用户')
        user = await User.create({
          openid: openid,
          unionid: unionid,
          nickname: '微信用户' + Math.floor(Math.random() * 1000),
          user_type: 1 // 默认为参展商，后续可修改
        })
        isNewUser = true
        logger.info('新用户创建成功，ID:', user.id)
      } else {
        // 更新用户信息
        logger.info('更新现有用户信息')
        if (unionid && unionid !== user.unionid) {
          await user.update({ unionid: unionid })
        }
      }

      // 生成JWT Token
      logger.info('生成JWT Token')
      const token = generateToken({
        id: user.id,
        openid: user.openid,
        userType: user.user_type,
        type: 'user'
      })

      logger.info('云开发登录成功，用户ID:', user.id)
      return {
        token,
        userInfo: {
          id: user.id,
          openid: user.openid,
          nickname: user.nickname,
          phone: user.phone,
          userType: user.user_type
        },
        isNewUser
      }
    } catch (error) {
      logger.error('云开发登录失败详细错误:', {
        message: error.message,
        stack: error.stack,
        openid: openid
      })
      throw new Error(`云开发登录失败: ${error.message}`)
    }
  }

  /**
   * 微信授权登录
   * @param {string} code 微信授权码
   */
  async wechatLogin(code) {
    try {
      logger.info('开始微信登录，code:', code)

      // 调用微信API获取access_token和openid
      const wechatUserInfo = await this.getWechatUserInfo(code)
      logger.info('获取微信用户信息成功:', wechatUserInfo)

      // 查找或创建用户
      let user = await User.findOne({
        where: { openid: wechatUserInfo.openid, is_deleted: 0 }
      })
      logger.info('查找用户结果:', user ? '找到用户' : '新用户')

      let isNewUser = false

      if (!user) {
        // 新用户，创建用户记录
        logger.info('创建新用户')
        user = await User.create({
          openid: wechatUserInfo.openid,
          unionid: wechatUserInfo.unionid,
          nickname: wechatUserInfo.nickname,
          user_type: 1 // 默认为参展商，后续可修改
        })
        isNewUser = true
        logger.info('新用户创建成功，ID:', user.id)
      } else {
        // 更新用户信息
        logger.info('更新现有用户信息')
        await user.update({
          unionid: wechatUserInfo.unionid || user.unionid,
          nickname: wechatUserInfo.nickname || user.nickname
        })
      }

      // 生成JWT Token
      logger.info('生成JWT Token')
      const token = generateToken({
        id: user.id,
        openid: user.openid,
        userType: user.user_type,
        type: 'user'
      })

      logger.info('微信登录成功，用户ID:', user.id)
      return {
        token,
        userInfo: {
          id: user.id,
          openid: user.openid,
          nickname: user.nickname,
          phone: user.phone,
          userType: user.user_type
        },
        isNewUser
      }
    } catch (error) {
      logger.error('微信登录失败详细错误:', {
        message: error.message,
        stack: error.stack,
        code: code
      })
      throw new Error(`微信登录失败: ${error.message}`)
    }
  }
  
  /**
   * 获取微信用户信息
   * @param {string} code 微信授权码
   */
  async getWechatUserInfo(code) {
    try {
      const appId = process.env.WECHAT_APP_ID
      const appSecret = process.env.WECHAT_APP_SECRET

      // 检查是否为测试code或空code，如果是则返回模拟数据
      if (!appId || !appSecret || !code || code.startsWith('test_code_') || code.startsWith('mock_code_')) {
        logger.info('使用模拟微信用户数据，code:', code || '空code')
        return {
          openid: 'mock_openid_' + Date.now(),
          unionid: 'mock_unionid_' + Date.now(),
          nickname: '微信用户' + Math.floor(Math.random() * 1000)
        }
      }
      
      // 获取access_token
      const tokenResponse = await axios.get('https://api.weixin.qq.com/sns/oauth2/access_token', {
        params: {
          appid: appId,
          secret: appSecret,
          code: code,
          grant_type: 'authorization_code'
        },
        httpsAgent: new (await import('https')).Agent({
          rejectUnauthorized: false // 忽略SSL证书验证
        })
      })
      
      const { access_token, openid } = tokenResponse.data
      
      if (!access_token || !openid) {
        throw new Error('获取微信access_token失败')
      }
      
      // 获取用户信息
      const userResponse = await axios.get('https://api.weixin.qq.com/sns/userinfo', {
        params: {
          access_token,
          openid,
          lang: 'zh_CN'
        },
        httpsAgent: new (await import('https')).Agent({
          rejectUnauthorized: false // 忽略SSL证书验证
        })
      })
      
      return {
        openid: userResponse.data.openid,
        unionid: userResponse.data.unionid,
        nickname: userResponse.data.nickname
      }
    } catch (error) {
      logger.error('获取微信用户信息失败:', error)
      throw new Error('获取微信用户信息失败')
    }
  }
  
  /**
   * 管理员登录
   * @param {string} username 用户名
   * @param {string} password 密码
   */
  async adminLogin(username, password) {
    try {
      const admin = await Admin.findOne({
        where: { username, is_deleted: 0 }
      })
      
      if (!admin) {
        throw new Error('用户名或密码错误')
      }
      
      // 验证密码
      const isValidPassword = await this.verifyPassword(password, admin.password)
      if (!isValidPassword) {
        throw new Error('用户名或密码错误')
      }
      
      // 生成JWT Token
      const token = generateToken({
        id: admin.id,
        username: admin.username,
        type: 'admin'
      })
      
      return {
        token,
        adminInfo: {
          id: admin.id,
          username: admin.username
        }
      }
    } catch (error) {
      logger.error('管理员登录失败:', error)
      throw error
    }
  }
  
  /**
   * 验证密码
   * @param {string} plainPassword 明文密码
   * @param {string} hashedPassword 加密密码
   */
  async verifyPassword(plainPassword, hashedPassword) {
    try {
      // 如果是明文密码（开发阶段），直接比较
      if (hashedPassword === plainPassword) {
        return true
      }
      
      // 使用bcrypt验证
      return await bcrypt.compare(plainPassword, hashedPassword)
    } catch (error) {
      logger.error('密码验证失败:', error)
      return false
    }
  }
  
  /**
   * 加密密码
   * @param {string} password 明文密码
   */
  async hashPassword(password) {
    try {
      const saltRounds = 10
      return await bcrypt.hash(password, saltRounds)
    } catch (error) {
      logger.error('密码加密失败:', error)
      throw new Error('密码加密失败')
    }
  }
}

export default new AuthService()
