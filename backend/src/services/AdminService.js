import { Op } from 'sequelize'
import {
  User,
  Exhibition,
  Venue,
  ExhibitionCategory,
  Factory,
  DesignCompany,
  Exhibitor,
  ExhibitionParticipant,
  UserFavorite
} from '../models/index.js'
import { logger } from '../utils/logger.js'

class AdminService {
  /**
   * 获取统计数据
   */
  async getStats() {
    try {
      const [
        userCount,
        exhibitionCount,
        factoryCount,
        designCompanyCount,
        exhibitorCount,
        venueCount,
        favoriteCount
      ] = await Promise.all([
        User.count({ where: { is_deleted: 0 } }),
        Exhibition.count({ where: { is_deleted: 0 } }),
        Factory.count({ where: { is_deleted: 0 } }),
        DesignCompany.count({ where: { is_deleted: 0 } }),
        Exhibitor.count({ where: { is_deleted: 0 } }),
        Venue.count({ where: { is_deleted: 0 } }),
        UserFavorite.count({ where: { is_deleted: 0 } })
      ])

      return {
        userCount,
        exhibitionCount,
        companyCount: factoryCount + designCompanyCount + exhibitorCount,
        venueCount,
        favoriteCount,
        factoryCount,
        designCompanyCount,
        exhibitorCount
      }
    } catch (error) {
      logger.error('获取统计数据失败:', error)
      throw new Error('获取统计数据失败')
    }
  }

  /**
   * 获取用户列表
   * @param {Object} params 查询参数
   */
  async getUsers(params = {}) {
    try {
      const { nickname, phone, userType, page = 1, pageSize = 10 } = params

      const whereConditions = {
        is_deleted: 0
      }

      if (nickname) {
        whereConditions.nickname = { [Op.like]: `%${nickname}%` }
      }

      if (phone) {
        whereConditions.phone = { [Op.like]: `%${phone}%` }
      }

      if (userType) {
        whereConditions.user_type = userType
      }

      const offset = (page - 1) * pageSize

      const { count, rows } = await User.findAndCountAll({
        where: whereConditions,
        order: [['created_at', 'DESC']],
        limit: parseInt(pageSize),
        offset: offset
      })

      const list = rows.map(user => ({
        id: user.id,
        openid: user.openid,
        nickname: user.nickname,
        phone: user.phone,
        userType: user.user_type,
        createdAt: user.created_at,
        updatedAt: user.updated_at
      }))

      return {
        list,
        total: count,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      }
    } catch (error) {
      logger.error('获取用户列表失败:', error)
      throw new Error('获取用户列表失败')
    }
  }

  /**
   * 获取展会列表
   * @param {Object} params 查询参数
   */
  async getExhibitions(params = {}) {
    try {
      const { name, venueId, status, page = 1, pageSize = 10 } = params

      const whereConditions = {
        is_deleted: 0
      }

      if (name) {
        whereConditions.name = { [Op.like]: `%${name}%` }
      }

      if (venueId) {
        whereConditions.venue_id = venueId
      }

      if (status !== undefined && status !== '') {
        whereConditions.status = status
      }

      const offset = (page - 1) * pageSize

      const { count, rows } = await Exhibition.findAndCountAll({
        where: whereConditions,
        include: [
          {
            model: Venue,
            as: 'venue',
            attributes: ['name', 'city']
          },
          {
            model: ExhibitionCategory,
            as: 'category',
            attributes: ['name']
          }
        ],
        order: [['start_date', 'DESC']],
        limit: parseInt(pageSize),
        offset: offset
      })

      const list = rows.map(exhibition => ({
        id: exhibition.id,
        name: exhibition.name,
        city: exhibition.city,
        startDate: exhibition.start_date,
        endDate: exhibition.end_date,
        status: exhibition.status,
        venue: exhibition.venue?.name,
        venueCity: exhibition.venue?.city,
        category: exhibition.category?.name,
        createdAt: exhibition.created_at
      }))

      return {
        list,
        total: count,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      }
    } catch (error) {
      logger.error('获取展会列表失败:', error)
      throw new Error('获取展会列表失败')
    }
  }

  /**
   * 获取展馆列表
   */
  async getVenues() {
    try {
      const venues = await Venue.findAll({
        where: { is_deleted: 0 },
        order: [['city', 'ASC'], ['name', 'ASC']]
      })

      return venues.map(venue => ({
        id: venue.id,
        city: venue.city,
        name: venue.name,
        address: venue.address,
        createdAt: venue.created_at
      }))
    } catch (error) {
      logger.error('获取展馆列表失败:', error)
      throw new Error('获取展馆列表失败')
    }
  }

  /**
   * 获取展会类别列表
   */
  async getCategories() {
    try {
      const categories = await ExhibitionCategory.findAll({
        where: { is_deleted: 0 },
        order: [['name', 'ASC']]
      })

      return categories.map(category => ({
        id: category.id,
        name: category.name,
        createdAt: category.created_at
      }))
    } catch (error) {
      logger.error('获取展会类别失败:', error)
      throw new Error('获取展会类别失败')
    }
  }

  /**
   * 获取公司列表
   * @param {Object} params 查询参数
   */
  async getCompanies(params = {}) {
    try {
      const { type, name, page = 1, pageSize = 10 } = params

      let companies = []
      let total = 0

      const offset = (page - 1) * pageSize

      if (!type || type === 'design') {
        // 获取设计公司
        const whereConditions = { is_deleted: 0 }
        if (name) {
          whereConditions.name = { [Op.like]: `%${name}%` }
        }

        const designResult = await DesignCompany.findAndCountAll({
          where: whereConditions,
          include: [
            {
              model: User,
              as: 'user',
              attributes: ['nickname']
            }
          ],
          limit: type === 'design' ? parseInt(pageSize) : undefined,
          offset: type === 'design' ? offset : 0,
          order: [['created_at', 'DESC']]
        })

        const designCompanies = designResult.rows.map(company => ({
          id: company.id,
          name: company.name,
          type: 'design',
          contactPerson: company.contact_person,
          contactPhone: company.contact_phone,
          city: company.city,
          level: company.level,
          createdAt: company.created_at
        }))

        companies = companies.concat(designCompanies)
        if (type === 'design') total = designResult.count
      }

      if (!type || type === 'factory') {
        // 获取工厂
        const whereConditions = { is_deleted: 0 }
        if (name) {
          whereConditions.name = { [Op.like]: `%${name}%` }
        }

        const factoryResult = await Factory.findAndCountAll({
          where: whereConditions,
          include: [
            {
              model: User,
              as: 'user',
              attributes: ['nickname']
            }
          ],
          limit: type === 'factory' ? parseInt(pageSize) : undefined,
          offset: type === 'factory' ? offset : 0,
          order: [['created_at', 'DESC']]
        })

        const factories = factoryResult.rows.map(factory => ({
          id: factory.id,
          name: factory.name,
          type: 'factory',
          contactPerson: factory.contact_person,
          contactPhone: factory.contact_phone,
          city: factory.city,
          level: factory.level,
          specialties: factory.specialties,
          createdAt: factory.created_at
        }))

        companies = companies.concat(factories)
        if (type === 'factory') total = factoryResult.count
      }

      if (!type || type === 'exhibitor') {
        // 获取参展商
        const whereConditions = { is_deleted: 0 }
        if (name) {
          whereConditions.name = { [Op.like]: `%${name}%` }
        }

        const exhibitorResult = await Exhibitor.findAndCountAll({
          where: whereConditions,
          include: [
            {
              model: User,
              as: 'user',
              attributes: ['nickname']
            },
            {
              model: ExhibitionCategory,
              as: 'category',
              attributes: ['name']
            }
          ],
          limit: type === 'exhibitor' ? parseInt(pageSize) : undefined,
          offset: type === 'exhibitor' ? offset : 0,
          order: [['created_at', 'DESC']]
        })

        const exhibitors = exhibitorResult.rows.map(exhibitor => ({
          id: exhibitor.id,
          name: exhibitor.name,
          type: 'exhibitor',
          contactPerson: exhibitor.contact_person,
          contactPhone: exhibitor.contact_phone,
          category: exhibitor.category?.name,
          createdAt: exhibitor.created_at
        }))

        companies = companies.concat(exhibitors)
        if (type === 'exhibitor') total = exhibitorResult.count
      }

      // 如果没有指定类型，需要手动分页
      if (!type) {
        total = companies.length
        companies = companies.slice(offset, offset + parseInt(pageSize))
      }

      return {
        list: companies,
        total,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      }
    } catch (error) {
      logger.error('获取公司列表失败:', error)
      throw new Error('获取公司列表失败')
    }
  }

  /**
   * 获取参展列表
   * @param {Object} params 查询参数
   */
  async getParticipants(params = {}) {
    try {
      const { exhibitionId, participantType, page = 1, pageSize = 10 } = params

      const whereConditions = {
        is_deleted: 0
      }

      if (exhibitionId) {
        whereConditions.exhibition_id = exhibitionId
      }

      if (participantType) {
        whereConditions.participant_type = participantType
      }

      const offset = (page - 1) * pageSize

      const { count, rows } = await ExhibitionParticipant.findAndCountAll({
        where: whereConditions,
        include: [
          {
            model: Exhibition,
            as: 'exhibition',
            attributes: ['name'],
            include: [
              {
                model: Venue,
                as: 'venue',
                attributes: ['name']
              }
            ]
          }
        ],
        order: [['created_at', 'DESC']],
        limit: parseInt(pageSize),
        offset: offset
      })

      // 获取参展方详细信息
      const list = await Promise.all(
        rows.map(async (participant) => {
          let participantInfo = null

          if (participant.participant_type === 2) {
            // 设计公司
            participantInfo = await DesignCompany.findByPk(participant.participant_id, {
              attributes: ['name', 'contact_person', 'contact_phone']
            })
          } else if (participant.participant_type === 3) {
            // 工厂
            participantInfo = await Factory.findByPk(participant.participant_id, {
              attributes: ['name', 'contact_person', 'contact_phone']
            })
          }

          return {
            id: participant.id,
            exhibitionId: participant.exhibition_id,
            exhibitionName: participant.exhibition?.name,
            venueName: participant.exhibition?.venue?.name,
            participantId: participant.participant_id,
            participantType: participant.participant_type,
            participantName: participantInfo?.name || '未知',
            contactPerson: participantInfo?.contact_person,
            contactPhone: participantInfo?.contact_phone,
            isFull: participant.is_full,
            createdAt: participant.created_at
          }
        })
      )

      return {
        list,
        total: count,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      }
    } catch (error) {
      logger.error('获取参展列表失败:', error)
      throw new Error('获取参展列表失败')
    }
  }

  /**
   * 获取参展详情
   * @param {number} id 参展ID
   */
  async getParticipantDetail(id) {
    try {
      const participant = await ExhibitionParticipant.findOne({
        where: { id, is_deleted: 0 },
        include: [
          {
            model: Exhibition,
            as: 'exhibition',
            attributes: ['id', 'name'],
            include: [
              {
                model: Venue,
                as: 'venue',
                attributes: ['name']
              }
            ]
          }
        ]
      })

      if (!participant) {
        throw new Error('参展信息不存在')
      }

      // 获取参展方详细信息
      let participantInfo = null
      if (participant.participant_type === 2) {
        // 设计公司
        participantInfo = await DesignCompany.findByPk(participant.participant_id, {
          attributes: ['name', 'contact_person', 'contact_phone']
        })
      } else if (participant.participant_type === 3) {
        // 工厂
        participantInfo = await Factory.findByPk(participant.participant_id, {
          attributes: ['name', 'contact_person', 'contact_phone']
        })
      } else if (participant.participant_type === 4) {
        // 参展商
        participantInfo = await Exhibitor.findByPk(participant.participant_id, {
          attributes: ['name', 'contact_person', 'contact_phone']
        })
      }

      return {
        id: participant.id,
        exhibitionId: participant.exhibition_id,
        exhibitionName: participant.exhibition?.name,
        venueName: participant.exhibition?.venue?.name,
        participantId: participant.participant_id,
        participantType: participant.participant_type,
        participantName: participantInfo?.name || '未知',
        contactPerson: participantInfo?.contact_person,
        contactPhone: participantInfo?.contact_phone,
        isFull: participant.is_full,
        createdAt: participant.created_at
      }
    } catch (error) {
      logger.error('获取参展详情失败:', error)
      throw error
    }
  }

  /**
   * 创建参展信息
   * @param {Object} data 参展数据
   */
  async createParticipant(data) {
    try {
      // 检查是否已存在相同的参展记录
      const existingParticipant = await ExhibitionParticipant.findOne({
        where: {
          exhibition_id: data.exhibition_id,
          participant_id: data.participant_id,
          participant_type: data.participant_type,
          is_deleted: 0
        }
      })

      if (existingParticipant) {
        throw new Error('该参展方已参加此展会')
      }

      const participant = await ExhibitionParticipant.create({
        ...data,
        is_deleted: 0
      })

      return participant
    } catch (error) {
      logger.error('创建参展信息失败:', error)
      throw error
    }
  }

  /**
   * 更新参展信息
   * @param {number} id 参展ID
   * @param {Object} data 更新数据
   */
  async updateParticipant(id, data) {
    try {
      const participant = await ExhibitionParticipant.findByPk(id)
      if (!participant) {
        throw new Error('参展信息不存在')
      }

      await participant.update(data)
      return participant
    } catch (error) {
      logger.error('更新参展信息失败:', error)
      throw error
    }
  }

  /**
   * 删除参展信息
   * @param {number} id 参展ID
   */
  async deleteParticipant(id) {
    try {
      const participant = await ExhibitionParticipant.findByPk(id)
      if (!participant) {
        throw new Error('参展信息不存在')
      }

      await participant.update({ is_deleted: 1 })
      return true
    } catch (error) {
      logger.error('删除参展信息失败:', error)
      throw error
    }
  }

  /**
   * 获取可选参展方列表
   * @param {number} participantType 参展方类型
   */
  async getAvailableParticipants(participantType) {
    try {
      let participants = []

      if (participantType === 2) {
        // 设计公司
        participants = await DesignCompany.findAll({
          where: { is_deleted: 0 },
          attributes: ['id', 'name', 'contact_person', 'contact_phone', 'city'],
          order: [['name', 'ASC']]
        })
      } else if (participantType === 3) {
        // 工厂
        participants = await Factory.findAll({
          where: { is_deleted: 0 },
          attributes: ['id', 'name', 'contact_person', 'contact_phone', 'city'],
          order: [['name', 'ASC']]
        })
      } else if (participantType === 4) {
        // 参展商
        participants = await Exhibitor.findAll({
          where: { is_deleted: 0 },
          attributes: ['id', 'name', 'contact_person', 'contact_phone'],
          order: [['name', 'ASC']]
        })
      }

      return participants.map(p => ({
        id: p.id,
        name: p.name,
        contactPerson: p.contact_person,
        contactPhone: p.contact_phone,
        city: p.city || ''
      }))
    } catch (error) {
      logger.error('获取可选参展方列表失败:', error)
      throw new Error('获取可选参展方列表失败')
    }
  }

  // ==================== 用户管理 ====================

  /**
   * 删除用户
   * @param {number} id 用户ID
   */
  async deleteUser(id) {
    try {
      const user = await User.findByPk(id)
      if (!user) {
        throw new Error('用户不存在')
      }

      await user.update({ is_deleted: 1 })
      return true
    } catch (error) {
      logger.error('删除用户失败:', error)
      throw error
    }
  }

  /**
   * 更新用户状态
   * @param {number} id 用户ID
   * @param {Object} data 更新数据
   */
  async updateUser(id, data) {
    try {
      const user = await User.findByPk(id)
      if (!user) {
        throw new Error('用户不存在')
      }

      await user.update(data)
      return user
    } catch (error) {
      logger.error('更新用户失败:', error)
      throw error
    }
  }

  // ==================== 展会管理 ====================

  /**
   * 创建展会
   * @param {Object} data 展会数据
   */
  async createExhibition(data) {
    try {
      // 如果没有提供城市，根据展馆ID获取城市
      if (!data.city && data.venue_id) {
        const venue = await Venue.findByPk(data.venue_id)
        if (venue) {
          data.city = venue.city
        }
      }

      const exhibition = await Exhibition.create({
        ...data,
        status: 0, // 默认未开始
        is_deleted: 0
      })
      return exhibition
    } catch (error) {
      logger.error('创建展会失败:', error)
      throw new Error('创建展会失败')
    }
  }

  /**
   * 更新展会
   * @param {number} id 展会ID
   * @param {Object} data 更新数据
   */
  async updateExhibition(id, data) {
    try {
      const exhibition = await Exhibition.findByPk(id)
      if (!exhibition) {
        throw new Error('展会不存在')
      }

      await exhibition.update(data)
      return exhibition
    } catch (error) {
      logger.error('更新展会失败:', error)
      throw error
    }
  }

  /**
   * 删除展会
   * @param {number} id 展会ID
   */
  async deleteExhibition(id) {
    try {
      const exhibition = await Exhibition.findByPk(id)
      if (!exhibition) {
        throw new Error('展会不存在')
      }

      await exhibition.update({ is_deleted: 1 })
      return true
    } catch (error) {
      logger.error('删除展会失败:', error)
      throw error
    }
  }

  /**
   * 获取展会详情
   * @param {number} id 展会ID
   */
  async getExhibitionDetail(id) {
    try {
      const exhibition = await Exhibition.findOne({
        where: { id, is_deleted: 0 },
        include: [
          {
            model: Venue,
            as: 'venue',
            attributes: ['id', 'name', 'city', 'address']
          },
          {
            model: ExhibitionCategory,
            as: 'category',
            attributes: ['id', 'name']
          }
        ]
      })

      if (!exhibition) {
        throw new Error('展会不存在')
      }

      return exhibition
    } catch (error) {
      logger.error('获取展会详情失败:', error)
      throw error
    }
  }

  // ==================== 展馆管理 ====================

  /**
   * 创建展馆
   * @param {Object} data 展馆数据
   */
  async createVenue(data) {
    try {
      const venue = await Venue.create({
        ...data,
        is_deleted: 0
      })
      return venue
    } catch (error) {
      logger.error('创建展馆失败:', error)
      throw new Error('创建展馆失败')
    }
  }

  /**
   * 更新展馆
   * @param {number} id 展馆ID
   * @param {Object} data 更新数据
   */
  async updateVenue(id, data) {
    try {
      const venue = await Venue.findByPk(id)
      if (!venue) {
        throw new Error('展馆不存在')
      }

      await venue.update(data)
      return venue
    } catch (error) {
      logger.error('更新展馆失败:', error)
      throw error
    }
  }

  /**
   * 删除展馆
   * @param {number} id 展馆ID
   */
  async deleteVenue(id) {
    try {
      const venue = await Venue.findByPk(id)
      if (!venue) {
        throw new Error('展馆不存在')
      }

      await venue.update({ is_deleted: 1 })
      return true
    } catch (error) {
      logger.error('删除展馆失败:', error)
      throw error
    }
  }

  /**
   * 获取展馆详情
   * @param {number} id 展馆ID
   */
  async getVenueDetail(id) {
    try {
      const venue = await Venue.findOne({
        where: { id, is_deleted: 0 }
      })

      if (!venue) {
        throw new Error('展馆不存在')
      }

      return venue
    } catch (error) {
      logger.error('获取展馆详情失败:', error)
      throw error
    }
  }

  // ==================== 展会类别管理 ====================

  /**
   * 创建展会类别
   * @param {Object} data 类别数据
   */
  async createCategory(data) {
    try {
      const category = await ExhibitionCategory.create({
        ...data,
        is_deleted: 0
      })
      return category
    } catch (error) {
      logger.error('创建展会类别失败:', error)
      throw new Error('创建展会类别失败')
    }
  }

  /**
   * 更新展会类别
   * @param {number} id 类别ID
   * @param {Object} data 更新数据
   */
  async updateCategory(id, data) {
    try {
      const category = await ExhibitionCategory.findByPk(id)
      if (!category) {
        throw new Error('展会类别不存在')
      }

      await category.update(data)
      return category
    } catch (error) {
      logger.error('更新展会类别失败:', error)
      throw error
    }
  }

  /**
   * 删除展会类别
   * @param {number} id 类别ID
   */
  async deleteCategory(id) {
    try {
      const category = await ExhibitionCategory.findByPk(id)
      if (!category) {
        throw new Error('展会类别不存在')
      }

      await category.update({ is_deleted: 1 })
      return true
    } catch (error) {
      logger.error('删除展会类别失败:', error)
      throw error
    }
  }

  /**
   * 获取展会类别详情
   * @param {number} id 类别ID
   */
  async getCategoryDetail(id) {
    try {
      const category = await ExhibitionCategory.findOne({
        where: { id, is_deleted: 0 }
      })

      if (!category) {
        throw new Error('展会类别不存在')
      }

      return category
    } catch (error) {
      logger.error('获取展会类别详情失败:', error)
      throw error
    }
  }

  // ==================== 公司管理 ====================

  /**
   * 删除公司
   * @param {string} type 公司类型 (design/factory/exhibitor)
   * @param {number} id 公司ID
   */
  async deleteCompany(type, id) {
    try {
      let company = null

      if (type === 'design') {
        company = await DesignCompany.findByPk(id)
      } else if (type === 'factory') {
        company = await Factory.findByPk(id)
      } else if (type === 'exhibitor') {
        company = await Exhibitor.findByPk(id)
      }

      if (!company) {
        throw new Error('公司不存在')
      }

      await company.update({ is_deleted: 1 })
      return true
    } catch (error) {
      logger.error('删除公司失败:', error)
      throw error
    }
  }

  /**
   * 获取公司详情
   * @param {string} type 公司类型
   * @param {number} id 公司ID
   */
  async getCompanyDetail(type, id) {
    try {
      let company = null

      if (type === 'design') {
        company = await DesignCompany.findOne({
          where: { id, is_deleted: 0 }
        })
      } else if (type === 'factory') {
        company = await Factory.findOne({
          where: { id, is_deleted: 0 }
        })
      } else if (type === 'exhibitor') {
        company = await Exhibitor.findOne({
          where: { id, is_deleted: 0 },
          include: [
            {
              model: ExhibitionCategory,
              as: 'category',
              attributes: ['id', 'name']
            }
          ]
        })
      }

      if (!company) {
        throw new Error('公司不存在')
      }

      return company
    } catch (error) {
      logger.error('获取公司详情失败:', error)
      throw error
    }
  }

  /**
   * 更新公司信息
   * @param {string} type 公司类型
   * @param {number} id 公司ID
   * @param {Object} data 更新数据
   */
  async updateCompany(type, id, data) {
    try {
      let company = null

      if (type === 'design') {
        company = await DesignCompany.findByPk(id)
      } else if (type === 'factory') {
        company = await Factory.findByPk(id)
      } else if (type === 'exhibitor') {
        company = await Exhibitor.findByPk(id)
      }

      if (!company) {
        throw new Error('公司不存在')
      }

      await company.update(data)
      return company
    } catch (error) {
      logger.error('更新公司失败:', error)
      throw error
    }
  }
}

export default new AdminService()
