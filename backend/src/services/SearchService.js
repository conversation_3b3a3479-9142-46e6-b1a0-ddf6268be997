import { Op } from 'sequelize'
import { 
  Exhibition, 
  Venue, 
  ExhibitionCategory, 
  ExhibitionParticipant, 
  Factory, 
  DesignCompany,
  User 
} from '../models/index.js'
import { logger } from '../utils/logger.js'

class SearchService {
  /**
   * 搜索工厂
   * @param {Object} searchParams 搜索参数
   */
  async searchFactories(searchParams) {
    try {
      const { city, date, venue, venueId, keyword, page = 1, pageSize = 10 } = searchParams

      // 首先根据条件查找展会
      const exhibitions = await this.findExhibitions({ city, date, venue, venueId })
      
      if (exhibitions.length === 0) {
        return {
          list: [],
          total: 0,
          page: parseInt(page),
          pageSize: parseInt(pageSize)
        }
      }
      
      const exhibitionIds = exhibitions.map(e => e.id)
      
      // 查找参展的工厂
      const whereConditions = {
        is_deleted: 0
      }
      
      // 关键字搜索
      if (keyword) {
        whereConditions[Op.or] = [
          { name: { [Op.like]: `%${keyword}%` } },
          { contact_person: { [Op.like]: `%${keyword}%` } },
          { specialties: { [Op.like]: `%${keyword}%` } }
        ]
      }
      
      const offset = (page - 1) * pageSize
      
      const { count, rows } = await Factory.findAndCountAll({
        where: whereConditions,
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'nickname']
          }
        ],
        order: [['level', 'DESC'], ['created_at', 'DESC']],
        limit: parseInt(pageSize),
        offset: offset
      })
      
      // 过滤出参展的工厂
      const participantFactories = await ExhibitionParticipant.findAll({
        where: {
          exhibition_id: { [Op.in]: exhibitionIds },
          participant_type: { [Op.in]: [3, 4] }, // 工厂或综合商
          is_deleted: 0
        },
        attributes: ['participant_id', 'participant_type', 'is_full']
      })
      
      const participantMap = new Map()
      participantFactories.forEach(p => {
        participantMap.set(p.participant_id, {
          type: p.participant_type,
          isFull: p.is_full
        })
      })
      
      // 过滤结果
      const filteredFactories = rows.filter(factory => 
        participantMap.has(factory.id)
      ).map(factory => {
        const participant = participantMap.get(factory.id)
        return {
          id: factory.id,
          name: factory.name,
          contactPerson: factory.contact_person,
          contactPhone: factory.contact_phone,
          city: factory.city,
          specialties: factory.specialties,
          level: factory.level,
          isFull: participant.isFull,
          type: participant.type === 4 ? 'comprehensive' : 'factory'
        }
      })
      
      return {
        list: filteredFactories,
        total: filteredFactories.length,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      }
    } catch (error) {
      logger.error('搜索工厂失败:', error)
      throw new Error('搜索工厂失败')
    }
  }
  
  /**
   * 搜索设计公司
   * @param {Object} searchParams 搜索参数
   */
  async searchDesignCompanies(searchParams) {
    try {
      const { city, date, venue, venueId, keyword, page = 1, pageSize = 10 } = searchParams

      // 首先根据条件查找展会
      const exhibitions = await this.findExhibitions({ city, date, venue, venueId })
      
      if (exhibitions.length === 0) {
        return {
          list: [],
          total: 0,
          page: parseInt(page),
          pageSize: parseInt(pageSize)
        }
      }
      
      const exhibitionIds = exhibitions.map(e => e.id)
      
      // 查找参展的设计公司
      const whereConditions = {
        is_deleted: 0
      }
      
      // 关键字搜索
      if (keyword) {
        whereConditions[Op.or] = [
          { name: { [Op.like]: `%${keyword}%` } },
          { contact_person: { [Op.like]: `%${keyword}%` } }
        ]
      }
      
      const offset = (page - 1) * pageSize
      
      const { count, rows } = await DesignCompany.findAndCountAll({
        where: whereConditions,
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'nickname']
          }
        ],
        order: [['level', 'DESC'], ['created_at', 'DESC']],
        limit: parseInt(pageSize),
        offset: offset
      })
      
      // 过滤出参展的设计公司
      const participantCompanies = await ExhibitionParticipant.findAll({
        where: {
          exhibition_id: { [Op.in]: exhibitionIds },
          participant_type: { [Op.in]: [2, 4] }, // 设计公司或综合商
          is_deleted: 0
        },
        attributes: ['participant_id', 'participant_type', 'is_full']
      })
      
      const participantMap = new Map()
      participantCompanies.forEach(p => {
        participantMap.set(p.participant_id, {
          type: p.participant_type,
          isFull: p.is_full
        })
      })
      
      // 过滤结果
      const filteredCompanies = rows.filter(company => 
        participantMap.has(company.id)
      ).map(company => {
        const participant = participantMap.get(company.id)
        return {
          id: company.id,
          name: company.name,
          contactPerson: company.contact_person,
          contactPhone: company.contact_phone,
          city: company.city,
          level: company.level,
          isFull: participant.isFull,
          type: participant.type === 4 ? 'comprehensive' : 'design'
        }
      })
      
      return {
        list: filteredCompanies,
        total: filteredCompanies.length,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      }
    } catch (error) {
      logger.error('搜索设计公司失败:', error)
      throw new Error('搜索设计公司失败')
    }
  }
  
  /**
   * 根据条件查找展会
   * @param {Object} conditions 查找条件
   */
  async findExhibitions(conditions) {
    try {
      const { city, date, venue, venueId } = conditions

      const whereConditions = {
        status: { [Op.ne]: 9 }, // 状态非已结束
        is_deleted: 0
      }

      // 城市条件
      if (city) {
        whereConditions.city = city
      }

      // 场馆ID条件
      if (venueId) {
        whereConditions.venue_id = venueId
      }

      // 日期条件 - 查找在指定日期进行的展会
      if (date) {
        whereConditions[Op.and] = [
          { start_date: { [Op.lte]: date } },
          { end_date: { [Op.gte]: date } }
        ]
      }

      const includeConditions = []

      // 展馆条件（兼容旧的venue名称搜索）
      if (venue && !venueId) {
        const venueWhere = {}
        if (venue) venueWhere.name = { [Op.like]: `%${venue}%` }

        includeConditions.push({
          model: Venue,
          as: 'venue',
          where: venueWhere,
          required: true
        })
      } else {
        includeConditions.push({
          model: Venue,
          as: 'venue',
          required: true
        })
      }

      const exhibitions = await Exhibition.findAll({
        where: whereConditions,
        include: includeConditions
      })

      return exhibitions
    } catch (error) {
      logger.error('查找展会失败:', error)
      throw new Error('查找展会失败')
    }
  }
  
  /**
   * 获取近期展会
   * @param {number} limit 限制数量
   * @param {string} city 城市过滤
   */
  async getRecentExhibitions(limit = 10, city = null) {
    try {
      const whereConditions = {
        start_date: { [Op.gte]: new Date() },
        is_deleted: 0
      }

      // 如果指定了城市，添加城市过滤条件
      if (city) {
        whereConditions.city = city
      }

      const exhibitions = await Exhibition.findAll({
        where: whereConditions,
        include: [
          {
            model: Venue,
            as: 'venue',
            attributes: ['name', 'city', 'address']
          },
          {
            model: ExhibitionCategory,
            as: 'category',
            attributes: ['name']
          }
        ],
        order: [['start_date', 'ASC']],
        limit: limit
      })

      return exhibitions.map(exhibition => ({
        id: exhibition.id,
        name: exhibition.name,
        city: exhibition.city,
        startDate: exhibition.start_date,
        endDate: exhibition.end_date,
        status: exhibition.status,
        venue: exhibition.venue,
        category: exhibition.category
      }))
    } catch (error) {
      logger.error('获取近期展会失败:', error)
      throw new Error('获取近期展会失败')
    }
  }

  /**
   * 获取工厂详情
   * @param {number} id 工厂ID
   */
  async getFactoryDetail(id) {
    try {
      const factory = await Factory.findOne({
        where: {
          id,
          is_deleted: 0
        },
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'nickname']
          }
        ]
      })

      if (!factory) {
        throw new Error('工厂不存在')
      }

      return {
        id: factory.id,
        name: factory.name,
        city: factory.city,
        contactPerson: factory.contact_person,
        contactPhone: factory.contact_phone,
        specialties: factory.specialties,
        level: factory.level,
        type: 'factory',
        isFull: false // 这里可以根据实际业务逻辑判断
      }
    } catch (error) {
      logger.error('获取工厂详情失败:', error)
      throw error
    }
  }

  /**
   * 获取设计公司详情
   * @param {number} id 设计公司ID
   */
  async getDesignCompanyDetail(id) {
    try {
      const company = await DesignCompany.findOne({
        where: {
          id,
          is_deleted: 0
        },
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'nickname']
          }
        ]
      })

      if (!company) {
        throw new Error('设计公司不存在')
      }

      return {
        id: company.id,
        name: company.name,
        city: company.city,
        contactPerson: company.contact_person,
        contactPhone: company.contact_phone,
        level: company.level,
        type: 'design',
        isFull: false // 这里可以根据实际业务逻辑判断
      }
    } catch (error) {
      logger.error('获取设计公司详情失败:', error)
      throw error
    }
  }

  /**
   * 获取展会详情
   * @param {number} id 展会ID
   */
  async getExhibitionDetail(id) {
    try {
      const exhibition = await Exhibition.findOne({
        where: {
          id,
          is_deleted: 0
        },
        include: [
          {
            model: Venue,
            as: 'venue',
            attributes: ['name', 'city', 'address']
          },
          {
            model: ExhibitionCategory,
            as: 'category',
            attributes: ['name']
          }
        ]
      })

      if (!exhibition) {
        throw new Error('展会不存在')
      }

      return {
        id: exhibition.id,
        name: exhibition.name,
        startDate: exhibition.start_date,
        endDate: exhibition.end_date,
        status: exhibition.status,
        venue: exhibition.venue,
        category: exhibition.category,
        description: exhibition.description
      }
    } catch (error) {
      logger.error('获取展会详情失败:', error)
      throw error
    }
  }

  /**
   * 获取展馆列表
   */
  async getVenues() {
    try {
      const venues = await Venue.findAll({
        where: {
          is_deleted: 0
        },
        attributes: ['id', 'name', 'city', 'address'],
        order: [['city', 'ASC'], ['name', 'ASC']]
      })

      return venues.map(venue => ({
        id: venue.id,
        name: venue.name,
        city: venue.city,
        address: venue.address
      }))
    } catch (error) {
      logger.error('获取展馆列表失败:', error)
      throw new Error('获取展馆列表失败')
    }
  }
}

export default new SearchService()
