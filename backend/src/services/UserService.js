import { User, Factory, DesignCompany, Exhibitor, ExhibitionCategory } from '../models/index.js'
import { logger } from '../utils/logger.js'

class UserService {
  /**
   * 获取用户详细信息
   * @param {number} userId 用户ID
   */
  async getUserProfile(userId) {
    try {
      const user = await User.findOne({
        where: { id: userId, is_deleted: 0 },
        include: [
          {
            model: Factory,
            as: 'factory',
            required: false,
            where: { is_deleted: 0 }
          },
          {
            model: DesignCompany,
            as: 'designCompany',
            required: false,
            where: { is_deleted: 0 }
          },
          {
            model: Exhibitor,
            as: 'exhibitor',
            required: false,
            where: { is_deleted: 0 },
            include: [
              {
                model: ExhibitionCategory,
                as: 'category',
                required: false
              }
            ]
          }
        ]
      })
      
      if (!user) {
        throw new Error('用户不存在')
      }
      
      // 组装返回数据
      const userInfo = {
        id: user.id,
        openid: user.openid,
        nickname: user.nickname,
        phone: user.phone,
        userType: user.user_type,
        companyInfo: null
      }
      
      // 根据用户类型获取对应的公司信息
      switch (user.user_type) {
        case 1: // 参展商
          if (user.exhibitor) {
            userInfo.companyInfo = {
              id: user.exhibitor.id,
              name: user.exhibitor.name,
              contactPerson: user.exhibitor.contact_person,
              contactPhone: user.exhibitor.contact_phone,
              category: user.exhibitor.category
            }
          }
          break
        case 2: // 设计公司
          if (user.designCompany) {
            userInfo.companyInfo = {
              id: user.designCompany.id,
              name: user.designCompany.name,
              contactPerson: user.designCompany.contact_person,
              contactPhone: user.designCompany.contact_phone,
              city: user.designCompany.city,
              level: user.designCompany.level
            }
          }
          break
        case 3: // 工厂
          if (user.factory) {
            userInfo.companyInfo = {
              id: user.factory.id,
              name: user.factory.name,
              contactPerson: user.factory.contact_person,
              contactPhone: user.factory.contact_phone,
              city: user.factory.city,
              specialties: user.factory.specialties,
              level: user.factory.level
            }
          }
          break
        case 4: // 综合商
          userInfo.companyInfo = {
            designCompany: user.designCompany ? {
              id: user.designCompany.id,
              name: user.designCompany.name,
              contactPerson: user.designCompany.contact_person,
              contactPhone: user.designCompany.contact_phone,
              city: user.designCompany.city,
              level: user.designCompany.level
            } : null,
            factory: user.factory ? {
              id: user.factory.id,
              name: user.factory.name,
              contactPerson: user.factory.contact_person,
              contactPhone: user.factory.contact_phone,
              city: user.factory.city,
              specialties: user.factory.specialties,
              level: user.factory.level
            } : null
          }
          break
      }
      
      return userInfo
    } catch (error) {
      logger.error('获取用户信息失败:', error)
      throw error
    }
  }
  
  /**
   * 更新用户基本信息
   * @param {number} userId 用户ID
   * @param {Object} updateData 更新数据
   */
  async updateUserProfile(userId, updateData) {
    try {
      const user = await User.findOne({
        where: { id: userId, is_deleted: 0 }
      })
      
      if (!user) {
        throw new Error('用户不存在')
      }
      
      // 只允许更新特定字段
      const allowedFields = ['nickname', 'phone']
      const updateFields = {}
      
      allowedFields.forEach(field => {
        if (updateData[field] !== undefined) {
          updateFields[field] = updateData[field]
        }
      })
      
      await user.update(updateFields)
      
      return await this.getUserProfile(userId)
    } catch (error) {
      logger.error('更新用户信息失败:', error)
      throw error
    }
  }
  
  /**
   * 完善用户信息（首次注册）
   * @param {number} userId 用户ID
   * @param {Object} profileData 用户资料
   */
  async completeProfile(userId, profileData) {
    try {
      const { userType, companyInfo } = profileData
      
      const user = await User.findOne({
        where: { id: userId, is_deleted: 0 }
      })
      
      if (!user) {
        throw new Error('用户不存在')
      }
      
      // 更新用户类型
      await user.update({ user_type: userType })
      
      // 根据用户类型创建对应的公司信息
      switch (userType) {
        case 1: // 参展商
          await Exhibitor.create({
            user_id: userId,
            name: companyInfo.name,
            contact_person: companyInfo.contactPerson,
            contact_phone: companyInfo.contactPhone,
            exhibition_category_id: companyInfo.categoryId
          })
          break
          
        case 2: // 设计公司
          await DesignCompany.create({
            user_id: userId,
            name: companyInfo.name,
            contact_person: companyInfo.contactPerson,
            contact_phone: companyInfo.contactPhone,
            city: companyInfo.city,
            level: companyInfo.level || 1
          })
          break
          
        case 3: // 工厂
          await Factory.create({
            user_id: userId,
            name: companyInfo.name,
            contact_person: companyInfo.contactPerson,
            contact_phone: companyInfo.contactPhone,
            city: companyInfo.city,
            specialties: companyInfo.specialties,
            level: companyInfo.level || 1
          })
          break
          
        case 4: // 综合商
          // 创建设计公司记录
          if (companyInfo.designCompany) {
            await DesignCompany.create({
              user_id: userId,
              name: companyInfo.designCompany.name,
              contact_person: companyInfo.designCompany.contactPerson,
              contact_phone: companyInfo.designCompany.contactPhone,
              city: companyInfo.designCompany.city,
              level: companyInfo.designCompany.level || 1
            })
          }
          
          // 创建工厂记录
          if (companyInfo.factory) {
            await Factory.create({
              user_id: userId,
              name: companyInfo.factory.name,
              contact_person: companyInfo.factory.contactPerson,
              contact_phone: companyInfo.factory.contactPhone,
              city: companyInfo.factory.city,
              specialties: companyInfo.factory.specialties,
              level: companyInfo.factory.level || 1
            })
          }
          break
          
        default:
          throw new Error('无效的用户类型')
      }
      
      return await this.getUserProfile(userId)
    } catch (error) {
      logger.error('完善用户信息失败:', error)
      throw error
    }
  }
}

export default new UserService()
