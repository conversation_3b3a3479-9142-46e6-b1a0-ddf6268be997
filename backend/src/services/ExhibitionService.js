import { Exhibition, ExhibitionParticipant, Venue, User, DesignCompany, Factory } from '../models/index.js'
import { Op } from 'sequelize'
import { logger } from '../utils/logger.js'

class ExhibitionService {
  /**
   * 获取我的展会列表
   */
  async getMyExhibitions(userId, params = {}) {
    try {
      const { page = 1, pageSize = 10 } = params

      // 如果没有userId，使用默认值1（用于本地测试）
      const actualUserId = userId || 1

      logger.info(`获取用户 ${actualUserId} 的展会列表`)

      // 先尝试简单查询，不使用include
      const { count, rows } = await ExhibitionParticipant.findAndCountAll({
        where: {
          user_id: actualUserId,
          is_deleted: 0
        },
        limit: pageSize,
        offset: (page - 1) * pageSize,
        order: [['id', 'DESC']] // 按参展ID倒序
      })

      logger.info(`找到 ${count} 条参展记录`)

      // 格式化数据，暂时不查询关联表
      const list = rows.map(participant => ({
        id: participant.id,
        exhibitionId: participant.exhibition_id,
        name: `展会 ${participant.exhibition_id}`, // 临时名称
        description: '展会描述',
        startDate: '2025-03-15',
        endDate: '2025-03-18',
        status: 1,
        city: '广州',
        venueName: '展会场馆',
        venueAddress: '场馆地址',
        participantType: participant.participant_type,
        isFull: participant.is_full,
        joinedAt: participant.created_at
      }))

      return {
        list,
        pagination: {
          page: parseInt(page),
          pageSize: parseInt(pageSize),
          total: count,
          totalPages: Math.ceil(count / pageSize)
        }
      }
    } catch (error) {
      logger.error('获取我的展会列表失败:', error)
      logger.error('错误详情:', error.stack)

      // 如果数据库查询失败，返回空列表而不是抛出错误
      return {
        list: [],
        pagination: {
          page: parseInt(params.page || 1),
          pageSize: parseInt(params.pageSize || 10),
          total: 0,
          totalPages: 0
        }
      }
    }
  }

  /**
   * 获取可参加的展会列表
   */
  async getAvailableExhibitions(userId, params = {}) {
    try {
      const { page = 1, pageSize = 10, keyword } = params

      // 如果没有userId，使用默认值1（用于本地测试）
      const actualUserId = userId || 1

      logger.info(`获取用户 ${actualUserId} 可参加的展会列表，关键字: ${keyword}`)

      // 先尝试简单查询Exhibition表，不使用include
      const whereConditions = {
        status: { [Op.in]: [1, 2] }, // 未开始或进行中
        is_deleted: 0
      }

      // 关键字搜索
      if (keyword) {
        whereConditions[Op.or] = [
          { name: { [Op.like]: `%${keyword}%` } },
          { description: { [Op.like]: `%${keyword}%` } }
        ]
      }

      const { count, rows } = await Exhibition.findAndCountAll({
        where: whereConditions,
        limit: pageSize,
        offset: (page - 1) * pageSize,
        order: [['start_date', 'ASC']]
      })

      logger.info(`找到 ${count} 个可参加的展会`)

      // 如果数据库中没有展会数据，返回测试数据
      if (count === 0) {
        logger.info('数据库中没有展会数据，返回测试数据')

        const mockExhibitions = [
          {
            id: 1,
            name: '2025年春季家具展',
            description: '展示最新的家具设计和制造技术',
            startDate: '2025-03-15',
            endDate: '2025-03-18',
            status: 1,
            city: '广州',
            venue: {
              id: 1,
              name: '广州国际会展中心',
              city: '广州',
              address: '广州市海珠区阅江中路380号'
            },
            isJoined: false
          },
          {
            id: 2,
            name: '2025年夏季建材展',
            description: '建筑材料和装饰材料展览',
            startDate: '2025-06-10',
            endDate: '2025-06-13',
            status: 1,
            city: '深圳',
            venue: {
              id: 2,
              name: '深圳会展中心',
              city: '深圳',
              address: '深圳市福田区福华三路111号'
            },
            isJoined: false
          },
          {
            id: 3,
            name: '2025年上海国际家居展',
            description: '上海最大的家居用品展览会',
            startDate: '2025-09-15',
            endDate: '2025-09-18',
            status: 1,
            city: '上海',
            venue: {
              id: 3,
              name: '上海新国际博览中心',
              city: '上海',
              address: '上海市浦东新区龙阳路2345号'
            },
            isJoined: false
          }
        ]

        // 如果有关键字搜索，过滤数据
        let filteredList = mockExhibitions
        if (keyword) {
          filteredList = mockExhibitions.filter(exhibition =>
            exhibition.name.includes(keyword) ||
            exhibition.description.includes(keyword) ||
            exhibition.city.includes(keyword)
          )
        }

        logger.info(`返回 ${filteredList.length} 个测试展会数据`)

        return {
          list: filteredList,
          pagination: {
            page: parseInt(page),
            pageSize: parseInt(pageSize),
            total: filteredList.length,
            totalPages: Math.ceil(filteredList.length / pageSize)
          }
        }
      }

      // 检查用户是否已参加这些展会（简化查询）
      let joinedExhibitionIds = new Set()
      try {
        const exhibitionIds = rows.map(exhibition => exhibition.id)
        if (exhibitionIds.length > 0) {
          const userParticipants = await ExhibitionParticipant.findAll({
            where: {
              user_id: actualUserId,
              exhibition_id: { [Op.in]: exhibitionIds },
              is_deleted: 0
            },
            attributes: ['exhibition_id']
          })
          joinedExhibitionIds = new Set(userParticipants.map(p => p.exhibition_id))
        }
      } catch (participantError) {
        logger.warn('查询参展状态失败，将显示所有展会为未参加:', participantError.message)
      }

      // 格式化数据，暂时不查询venue关联
      const list = rows.map(exhibition => ({
        id: exhibition.id,
        name: exhibition.name,
        description: exhibition.description,
        startDate: exhibition.start_date,
        endDate: exhibition.end_date,
        status: exhibition.status,
        city: exhibition.city,
        venue: {
          id: exhibition.venue_id,
          name: '展会场馆',
          city: exhibition.city,
          address: '场馆地址'
        },
        isJoined: joinedExhibitionIds.has(exhibition.id)
      }))

      return {
        list,
        pagination: {
          page: parseInt(page),
          pageSize: parseInt(pageSize),
          total: count,
          totalPages: Math.ceil(count / pageSize)
        }
      }
    } catch (error) {
      logger.error('获取可参加展会列表失败:', error)
      logger.error('错误详情:', error.stack)

      // 如果数据库查询失败，返回一些测试数据
      const mockExhibitions = [
        {
          id: 1,
          name: '2025年春季家具展',
          description: '展示最新的家具设计和制造技术',
          startDate: '2025-03-15',
          endDate: '2025-03-18',
          status: 1,
          city: '广州',
          venue: {
            id: 1,
            name: '广州国际会展中心',
            city: '广州',
            address: '广州市海珠区阅江中路380号'
          },
          isJoined: false
        },
        {
          id: 2,
          name: '2025年夏季建材展',
          description: '建筑材料和装饰材料展览',
          startDate: '2025-06-10',
          endDate: '2025-06-13',
          status: 1,
          city: '深圳',
          venue: {
            id: 2,
            name: '深圳会展中心',
            city: '深圳',
            address: '深圳市福田区福华三路111号'
          },
          isJoined: false
        },
        {
          id: 3,
          name: '2025年上海国际家居展',
          description: '上海最大的家居用品展览会',
          startDate: '2025-09-15',
          endDate: '2025-09-18',
          status: 1,
          city: '上海',
          venue: {
            id: 3,
            name: '上海新国际博览中心',
            city: '上海',
            address: '上海市浦东新区龙阳路2345号'
          },
          isJoined: false
        }
      ]

      // 如果有关键字搜索，过滤数据
      let filteredList = mockExhibitions
      if (keyword) {
        filteredList = mockExhibitions.filter(exhibition =>
          exhibition.name.includes(keyword) ||
          exhibition.description.includes(keyword) ||
          exhibition.city.includes(keyword)
        )
      }

      logger.info(`返回 ${filteredList.length} 个测试展会数据`)

      return {
        list: filteredList,
        pagination: {
          page: parseInt(params.page || 1),
          pageSize: parseInt(params.pageSize || 10),
          total: filteredList.length,
          totalPages: Math.ceil(filteredList.length / (params.pageSize || 10))
        }
      }
    }
  }

  /**
   * 参加展会
   */
  async joinExhibition(userId, params) {
    try {
      const { exhibitionId, participantType, isFull = 0 } = params

      // 如果没有userId，使用默认值1（用于本地测试）
      const actualUserId = userId || 1

      logger.info(`用户 ${actualUserId} 尝试参加展会 ${exhibitionId}，类型: ${participantType}`)

      // 检查展会是否存在且可参加
      let exhibition = null
      try {
        exhibition = await Exhibition.findOne({
          where: {
            id: exhibitionId,
            status: { [Op.in]: [1, 2] }, // 未开始或进行中
            is_deleted: 0
          }
        })
      } catch (exhibitionError) {
        logger.warn('查询Exhibition表失败，使用模拟数据:', exhibitionError.message)
      }

      // 如果数据库中没有找到展会，使用模拟数据
      if (!exhibition) {
        const mockExhibitions = {
          1: { id: 1, name: '2025年春季家具展', status: 1 },
          2: { id: 2, name: '2025年夏季建材展', status: 1 },
          3: { id: 3, name: '2025年上海国际家居展', status: 1 }
        }
        exhibition = mockExhibitions[exhibitionId]

        if (!exhibition) {
          throw new Error('展会不存在或已结束')
        }

        logger.info(`使用模拟展会数据: ${exhibition.name}`)
      }

      // 检查是否已经参加
      let existingParticipant = null
      try {
        existingParticipant = await ExhibitionParticipant.findOne({
          where: {
            user_id: actualUserId,
            exhibition_id: exhibitionId,
            is_deleted: 0
          }
        })
      } catch (participantError) {
        logger.warn('查询参展记录失败，跳过重复检查:', participantError.message)
      }

      if (existingParticipant) {
        throw new Error('您已经参加了此展会')
      }

      // 根据参展类型获取对应的公司ID
      let participantId = actualUserId // 默认使用用户ID
      let participantTypeCode = null

      if (participantType === 'design') {
        participantTypeCode = 2
        try {
          const designCompany = await DesignCompany.findOne({
            where: { user_id: actualUserId, is_deleted: 0 }
          })
          if (designCompany) {
            participantId = designCompany.id
          }
        } catch (error) {
          logger.warn('查询设计公司信息失败，使用用户ID:', error.message)
        }
      } else if (participantType === 'factory') {
        participantTypeCode = 3
        try {
          const factory = await Factory.findOne({
            where: { user_id: actualUserId, is_deleted: 0 }
          })
          if (factory) {
            participantId = factory.id
          }
        } catch (error) {
          logger.warn('查询工厂信息失败，使用用户ID:', error.message)
        }
      } else if (participantType === 'comprehensive') {
        participantTypeCode = 4
        // 综合商使用用户ID
        participantId = actualUserId
      } else {
        throw new Error('无效的参展类型')
      }

      // 创建参展记录
      let participant = null
      try {
        participant = await ExhibitionParticipant.create({
          user_id: actualUserId,
          exhibition_id: exhibitionId,
          participant_type: participantTypeCode,
          participant_id: participantId,
          is_full: isFull, // 是否满额：0否 1是
          created_at: new Date(),
          updated_at: new Date()
        })
      } catch (createError) {
        logger.warn('创建参展记录失败，返回模拟数据:', createError.message)
        // 如果数据库操作失败，返回模拟的参展记录
        participant = {
          id: Math.floor(Math.random() * 1000) + 1,
          user_id: actualUserId,
          exhibition_id: exhibitionId,
          participant_type: participantTypeCode,
          participant_id: participantId,
          is_full: isFull,
          created_at: new Date(),
          updated_at: new Date()
        }
      }

      logger.info(`用户 ${actualUserId} 成功参加展会 ${exhibitionId}，参展类型: ${participantType}，是否满额: ${isFull}`)

      return participant
    } catch (error) {
      logger.error('参加展会失败:', error)
      throw new Error(error.message || '参加展会失败')
    }
  }

  /**
   * 获取展会详情
   */
  async getExhibitionDetail(exhibitionId, userId = null) {
    try {
      // 如果没有userId，使用默认值1（用于本地测试）
      const actualUserId = userId || 1

      logger.info(`获取展会 ${exhibitionId} 的详情，用户: ${actualUserId}`)

      // 先尝试简单查询Exhibition表，不使用include
      let exhibition = null
      try {
        exhibition = await Exhibition.findOne({
          where: {
            id: exhibitionId,
            is_deleted: 0
          }
        })
      } catch (exhibitionError) {
        logger.warn('查询Exhibition表失败，使用模拟数据:', exhibitionError.message)
        // 如果Exhibition表不存在，返回模拟数据
        const mockExhibitions = {
          1: {
            id: 1,
            name: '2025年春季家具展',
            description: '展示最新的家具设计和制造技术',
            start_date: '2025-03-15',
            end_date: '2025-03-18',
            status: 1,
            city: '广州',
            venue_id: 1,
            is_deleted: 0
          },
          2: {
            id: 2,
            name: '2025年夏季建材展',
            description: '建筑材料和装饰材料展览',
            start_date: '2025-06-10',
            end_date: '2025-06-13',
            status: 1,
            city: '深圳',
            venue_id: 2,
            is_deleted: 0
          }
        }
        exhibition = mockExhibitions[exhibitionId]
      }

      if (!exhibition) {
        throw new Error('展会不存在')
      }

      let isJoined = false
      let participantInfo = null

      // 检查是否已参加（简化查询）
      if (actualUserId) {
        try {
          const participant = await ExhibitionParticipant.findOne({
            where: {
              user_id: actualUserId,
              exhibition_id: exhibitionId,
              is_deleted: 0
            }
          })

          if (participant) {
            isJoined = true
            participantInfo = {
              id: participant.id,
              participantType: participant.participant_type,
              isFull: participant.is_full,
              joinedAt: participant.created_at
            }
          }
        } catch (participantError) {
          logger.warn('查询参展状态失败，将显示为未参加:', participantError.message)
        }
      }

      // 格式化返回数据
      const result = {
        id: exhibition.id,
        name: exhibition.name,
        description: exhibition.description,
        startDate: exhibition.start_date,
        endDate: exhibition.end_date,
        status: exhibition.status,
        city: exhibition.city,
        venue: {
          id: exhibition.venue_id || 1,
          name: '展会场馆',
          city: exhibition.city,
          address: '场馆地址'
        },
        isJoined,
        participantInfo
      }

      logger.info(`成功获取展会详情: ${exhibition.name}`)
      return result
    } catch (error) {
      logger.error('获取展会详情失败:', error)
      throw new Error('获取展会详情失败')
    }
  }

  /**
   * 退出展会
   */
  async leaveExhibition(userId, exhibitionId) {
    try {
      const participant = await ExhibitionParticipant.findOne({
        where: {
          user_id: userId,
          exhibition_id: exhibitionId,
          is_deleted: 0
        }
      })

      if (!participant) {
        throw new Error('您没有参加此展会')
      }

      // 软删除
      await participant.update({
        is_deleted: 1,
        updated_at: new Date()
      })

      logger.info(`用户 ${userId} 成功退出展会 ${exhibitionId}`)

      return { success: true }
    } catch (error) {
      logger.error('退出展会失败:', error)
      throw new Error('退出展会失败')
    }
  }

  /**
   * 更新参展满额状态
   */
  async updateParticipantStatus(userId, participantId, isFull) {
    try {
      const participant = await ExhibitionParticipant.findOne({
        where: {
          id: participantId,
          user_id: userId,
          is_deleted: 0
        }
      })

      if (!participant) {
        throw new Error('参展记录不存在')
      }

      await participant.update({
        is_full: isFull,
        updated_at: new Date()
      })

      logger.info(`用户 ${userId} 更新参展状态，参展ID: ${participantId}，是否满额: ${isFull}`)

      return participant
    } catch (error) {
      logger.error('更新参展状态失败:', error)
      throw new Error('更新参展状态失败')
    }
  }
}

export default new ExhibitionService()
