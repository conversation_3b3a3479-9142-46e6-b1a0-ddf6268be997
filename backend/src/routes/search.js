import express from 'express'
import { success, error, paginate } from '../utils/response.js'
import { logger } from '../utils/logger.js'
import SearchService from '../services/SearchService.js'

const router = express.Router()

/**
 * 搜索工厂
 * GET /api/search/factories
 */
router.get('/factories', async (req, res) => {
  try {
    const searchParams = req.query
    const result = await SearchService.searchFactories(searchParams)

    res.json(paginate(
      result.list,
      result.total,
      result.page,
      result.pageSize,
      '搜索工厂成功'
    ))
  } catch (err) {
    logger.error('搜索工厂失败:', err)
    res.status(500).json(error(err.message || '搜索工厂失败', 500))
  }
})

/**
 * 搜索设计公司
 * GET /api/search/design-companies
 */
router.get('/design-companies', async (req, res) => {
  try {
    const searchParams = req.query
    const result = await SearchService.searchDesignCompanies(searchParams)

    res.json(paginate(
      result.list,
      result.total,
      result.page,
      result.pageSize,
      '搜索设计公司成功'
    ))
  } catch (err) {
    logger.error('搜索设计公司失败:', err)
    res.status(500).json(error(err.message || '搜索设计公司失败', 500))
  }
})

/**
 * 获取近期展会
 * GET /api/search/recent-exhibitions
 */
router.get('/recent-exhibitions', async (req, res) => {
  try {
    const { limit = 10, city } = req.query
    const exhibitions = await SearchService.getRecentExhibitions(parseInt(limit), city)

    res.json(success(exhibitions, '获取近期展会成功'))
  } catch (err) {
    logger.error('获取近期展会失败:', err)
    res.status(500).json(error(err.message || '获取近期展会失败', 500))
  }
})

/**
 * 获取工厂详情
 * GET /api/factory/:id
 */
router.get('/factory/:id', async (req, res) => {
  try {
    const { id } = req.params
    const factory = await SearchService.getFactoryDetail(parseInt(id))

    res.json(success(factory, '获取工厂详情成功'))
  } catch (err) {
    logger.error('获取工厂详情失败:', err)
    res.status(500).json(error(err.message || '获取工厂详情失败', 500))
  }
})

/**
 * 获取设计公司详情
 * GET /api/design-company/:id
 */
router.get('/design-company/:id', async (req, res) => {
  try {
    const { id } = req.params
    const company = await SearchService.getDesignCompanyDetail(parseInt(id))

    res.json(success(company, '获取设计公司详情成功'))
  } catch (err) {
    logger.error('获取设计公司详情失败:', err)
    res.status(500).json(error(err.message || '获取设计公司详情失败', 500))
  }
})

/**
 * 获取展会详情
 * GET /api/exhibition/:id
 */
router.get('/exhibition/:id', async (req, res) => {
  try {
    const { id } = req.params
    const exhibition = await SearchService.getExhibitionDetail(parseInt(id))

    res.json(success(exhibition, '获取展会详情成功'))
  } catch (err) {
    logger.error('获取展会详情失败:', err)
    res.status(500).json(error(err.message || '获取展会详情失败', 500))
  }
})

/**
 * 获取展馆列表
 * GET /api/search/venues
 */
router.get('/venues', async (req, res) => {
  try {
    const venues = await SearchService.getVenues()
    res.json(success(venues, '获取展馆列表成功'))
  } catch (err) {
    logger.error('获取展馆列表失败:', err)
    res.status(500).json(error(err.message || '获取展馆列表失败', 500))
  }
})

export default router
