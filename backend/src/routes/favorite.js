import express from 'express'
import { authenticate } from '../middleware/auth.js'
import { success, error, paginate } from '../utils/response.js'
import { logger } from '../utils/logger.js'
import FavoriteService from '../services/FavoriteService.js'

const router = express.Router()

/**
 * 获取收藏列表
 * GET /api/favorite
 */
router.get('/', authenticate, async (req, res) => {
  try {
    const params = req.query
    const result = await FavoriteService.getUserFavorites(req.user.id, params)

    res.json(paginate(
      result.list,
      result.total,
      result.page,
      result.pageSize,
      '获取收藏列表成功'
    ))
  } catch (err) {
    logger.error('获取收藏列表失败:', err)
    res.status(500).json(error(err.message || '获取收藏列表失败', 500))
  }
})

/**
 * 添加收藏
 * POST /api/favorite
 */
router.post('/', authenticate, async (req, res) => {
  try {
    const favoriteData = req.body

    if (!favoriteData.type || !favoriteData.id) {
      return res.status(400).json(error('收藏类型和ID不能为空', 400))
    }

    const favorite = await FavoriteService.addFavorite(req.user.id, favoriteData)

    res.json(success(favorite, '添加收藏成功'))
  } catch (err) {
    logger.error('添加收藏失败:', err)
    const statusCode = err.message.includes('已经收藏') ? 400 : 500
    res.status(statusCode).json(error(err.message || '添加收藏失败', statusCode))
  }
})

/**
 * 取消收藏
 * DELETE /api/favorite/:id
 */
router.delete('/:id', authenticate, async (req, res) => {
  try {
    const favoriteId = parseInt(req.params.id)

    if (!favoriteId) {
      return res.status(400).json(error('收藏ID无效', 400))
    }

    await FavoriteService.removeFavorite(req.user.id, favoriteId)

    res.json(success(null, '取消收藏成功'))
  } catch (err) {
    logger.error('取消收藏失败:', err)
    const statusCode = err.message.includes('不存在') ? 404 : 500
    res.status(statusCode).json(error(err.message || '取消收藏失败', statusCode))
  }
})

/**
 * 检查收藏状态
 * GET /api/favorite/check/:type/:id
 */
router.get('/check/:type/:id', authenticate, async (req, res) => {
  try {
    const type = parseInt(req.params.type)
    const id = parseInt(req.params.id)

    const isFavorited = await FavoriteService.checkIsFavorited(req.user.id, type, id)

    res.json(success({ isFavorited }, '检查收藏状态成功'))
  } catch (err) {
    logger.error('检查收藏状态失败:', err)
    res.status(500).json(error(err.message || '检查收藏状态失败', 500))
  }
})

export default router
