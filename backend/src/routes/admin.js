import express from 'express'
import { authenticateAdmin } from '../middleware/auth.js'
import { success, error, paginate } from '../utils/response.js'
import { logger } from '../utils/logger.js'
import AdminService from '../services/AdminService.js'

const router = express.Router()

/**
 * 获取统计数据
 * GET /api/admin/stats
 */
router.get('/stats', authenticateAdmin, async (req, res) => {
  try {
    const stats = await AdminService.getStats()
    res.json(success(stats, '获取统计数据成功'))
  } catch (err) {
    logger.error('获取统计数据失败:', err)
    res.status(500).json(error(err.message || '获取统计数据失败', 500))
  }
})

/**
 * 用户管理 - 获取用户列表
 * GET /api/admin/users
 */
router.get('/users', authenticateAdmin, async (req, res) => {
  try {
    const params = req.query
    const result = await AdminService.getUsers(params)

    res.json(paginate(
      result.list,
      result.total,
      result.page,
      result.pageSize,
      '获取用户列表成功'
    ))
  } catch (err) {
    logger.error('获取用户列表失败:', err)
    res.status(500).json(error(err.message || '获取用户列表失败', 500))
  }
})

/**
 * 展会管理 - 获取展会列表
 * GET /api/admin/exhibitions
 */
router.get('/exhibitions', authenticateAdmin, async (req, res) => {
  try {
    const params = req.query
    const result = await AdminService.getExhibitions(params)

    res.json(paginate(
      result.list,
      result.total,
      result.page,
      result.pageSize,
      '获取展会列表成功'
    ))
  } catch (err) {
    logger.error('获取展会列表失败:', err)
    res.status(500).json(error(err.message || '获取展会列表失败', 500))
  }
})

/**
 * 展馆管理 - 获取展馆列表
 * GET /api/admin/venues
 */
router.get('/venues', authenticateAdmin, async (req, res) => {
  try {
    const venues = await AdminService.getVenues()
    res.json(success(venues, '获取展馆列表成功'))
  } catch (err) {
    logger.error('获取展馆列表失败:', err)
    res.status(500).json(error(err.message || '获取展馆列表失败', 500))
  }
})

/**
 * 展会类别管理 - 获取类别列表
 * GET /api/admin/categories
 */
router.get('/categories', authenticateAdmin, async (req, res) => {
  try {
    const categories = await AdminService.getCategories()
    res.json(success(categories, '获取展会类别成功'))
  } catch (err) {
    logger.error('获取展会类别失败:', err)
    res.status(500).json(error(err.message || '获取展会类别失败', 500))
  }
})

/**
 * 公司管理 - 获取公司列表
 * GET /api/admin/companies
 */
router.get('/companies', authenticateAdmin, async (req, res) => {
  try {
    const params = req.query
    const result = await AdminService.getCompanies(params)

    res.json(paginate(
      result.list,
      result.total,
      result.page,
      result.pageSize,
      '获取公司列表成功'
    ))
  } catch (err) {
    logger.error('获取公司列表失败:', err)
    res.status(500).json(error(err.message || '获取公司列表失败', 500))
  }
})

/**
 * 参展管理 - 获取参展列表
 * GET /api/admin/participants
 */
router.get('/participants', authenticateAdmin, async (req, res) => {
  try {
    const params = req.query
    const result = await AdminService.getParticipants(params)

    res.json(paginate(
      result.list,
      result.total,
      result.page,
      result.pageSize,
      '获取参展列表成功'
    ))
  } catch (err) {
    logger.error('获取参展列表失败:', err)
    res.status(500).json(error(err.message || '获取参展列表失败', 500))
  }
})

/**
 * 参展管理 - 获取参展详情
 * GET /api/admin/participants/:id
 */
router.get('/participants/:id', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params
    const participant = await AdminService.getParticipantDetail(parseInt(id))
    res.json(success(participant, '获取参展详情成功'))
  } catch (err) {
    logger.error('获取参展详情失败:', err)
    res.status(500).json(error(err.message || '获取参展详情失败', 500))
  }
})

/**
 * 参展管理 - 创建参展信息
 * POST /api/admin/participants
 */
router.post('/participants', authenticateAdmin, async (req, res) => {
  try {
    const data = req.body
    const participant = await AdminService.createParticipant(data)
    res.json(success(participant, '创建参展信息成功'))
  } catch (err) {
    logger.error('创建参展信息失败:', err)
    res.status(500).json(error(err.message || '创建参展信息失败', 500))
  }
})

/**
 * 参展管理 - 更新参展信息
 * PUT /api/admin/participants/:id
 */
router.put('/participants/:id', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params
    const data = req.body
    const participant = await AdminService.updateParticipant(parseInt(id), data)
    res.json(success(participant, '更新参展信息成功'))
  } catch (err) {
    logger.error('更新参展信息失败:', err)
    res.status(500).json(error(err.message || '更新参展信息失败', 500))
  }
})

/**
 * 参展管理 - 删除参展信息
 * DELETE /api/admin/participants/:id
 */
router.delete('/participants/:id', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params
    await AdminService.deleteParticipant(parseInt(id))
    res.json(success(null, '删除参展信息成功'))
  } catch (err) {
    logger.error('删除参展信息失败:', err)
    res.status(500).json(error(err.message || '删除参展信息失败', 500))
  }
})

/**
 * 参展管理 - 获取可选参展方列表
 * GET /api/admin/participants/available/:type
 */
router.get('/participants/available/:type', authenticateAdmin, async (req, res) => {
  try {
    const { type } = req.params
    const participants = await AdminService.getAvailableParticipants(parseInt(type))
    res.json(success(participants, '获取可选参展方列表成功'))
  } catch (err) {
    logger.error('获取可选参展方列表失败:', err)
    res.status(500).json(error(err.message || '获取可选参展方列表失败', 500))
  }
})

// ==================== 用户管理增删改 ====================

/**
 * 用户管理 - 删除用户
 * DELETE /api/admin/users/:id
 */
router.delete('/users/:id', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params
    await AdminService.deleteUser(parseInt(id))
    res.json(success(null, '删除用户成功'))
  } catch (err) {
    logger.error('删除用户失败:', err)
    res.status(500).json(error(err.message || '删除用户失败', 500))
  }
})

/**
 * 用户管理 - 更新用户
 * PUT /api/admin/users/:id
 */
router.put('/users/:id', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params
    const data = req.body
    const user = await AdminService.updateUser(parseInt(id), data)
    res.json(success(user, '更新用户成功'))
  } catch (err) {
    logger.error('更新用户失败:', err)
    res.status(500).json(error(err.message || '更新用户失败', 500))
  }
})

// ==================== 展会管理增删改 ====================

/**
 * 展会管理 - 获取展会详情
 * GET /api/admin/exhibitions/:id
 */
router.get('/exhibitions/:id', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params
    const exhibition = await AdminService.getExhibitionDetail(parseInt(id))
    res.json(success(exhibition, '获取展会详情成功'))
  } catch (err) {
    logger.error('获取展会详情失败:', err)
    res.status(500).json(error(err.message || '获取展会详情失败', 500))
  }
})

/**
 * 展会管理 - 创建展会
 * POST /api/admin/exhibitions
 */
router.post('/exhibitions', authenticateAdmin, async (req, res) => {
  try {
    const data = req.body
    const exhibition = await AdminService.createExhibition(data)
    res.json(success(exhibition, '创建展会成功'))
  } catch (err) {
    logger.error('创建展会失败:', err)
    res.status(500).json(error(err.message || '创建展会失败', 500))
  }
})

/**
 * 展会管理 - 更新展会
 * PUT /api/admin/exhibitions/:id
 */
router.put('/exhibitions/:id', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params
    const data = req.body
    const exhibition = await AdminService.updateExhibition(parseInt(id), data)
    res.json(success(exhibition, '更新展会成功'))
  } catch (err) {
    logger.error('更新展会失败:', err)
    res.status(500).json(error(err.message || '更新展会失败', 500))
  }
})

/**
 * 展会管理 - 删除展会
 * DELETE /api/admin/exhibitions/:id
 */
router.delete('/exhibitions/:id', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params
    await AdminService.deleteExhibition(parseInt(id))
    res.json(success(null, '删除展会成功'))
  } catch (err) {
    logger.error('删除展会失败:', err)
    res.status(500).json(error(err.message || '删除展会失败', 500))
  }
})

// ==================== 展馆管理增删改 ====================

/**
 * 展馆管理 - 获取展馆详情
 * GET /api/admin/venues/:id
 */
router.get('/venues/:id', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params
    const venue = await AdminService.getVenueDetail(parseInt(id))
    res.json(success(venue, '获取展馆详情成功'))
  } catch (err) {
    logger.error('获取展馆详情失败:', err)
    res.status(500).json(error(err.message || '获取展馆详情失败', 500))
  }
})

/**
 * 展馆管理 - 创建展馆
 * POST /api/admin/venues
 */
router.post('/venues', authenticateAdmin, async (req, res) => {
  try {
    const data = req.body
    const venue = await AdminService.createVenue(data)
    res.json(success(venue, '创建展馆成功'))
  } catch (err) {
    logger.error('创建展馆失败:', err)
    res.status(500).json(error(err.message || '创建展馆失败', 500))
  }
})

/**
 * 展馆管理 - 更新展馆
 * PUT /api/admin/venues/:id
 */
router.put('/venues/:id', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params
    const data = req.body
    const venue = await AdminService.updateVenue(parseInt(id), data)
    res.json(success(venue, '更新展馆成功'))
  } catch (err) {
    logger.error('更新展馆失败:', err)
    res.status(500).json(error(err.message || '更新展馆失败', 500))
  }
})

/**
 * 展馆管理 - 删除展馆
 * DELETE /api/admin/venues/:id
 */
router.delete('/venues/:id', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params
    await AdminService.deleteVenue(parseInt(id))
    res.json(success(null, '删除展馆成功'))
  } catch (err) {
    logger.error('删除展馆失败:', err)
    res.status(500).json(error(err.message || '删除展馆失败', 500))
  }
})

// ==================== 展会类别管理增删改 ====================

/**
 * 展会类别管理 - 获取类别详情
 * GET /api/admin/categories/:id
 */
router.get('/categories/:id', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params
    const category = await AdminService.getCategoryDetail(parseInt(id))
    res.json(success(category, '获取展会类别详情成功'))
  } catch (err) {
    logger.error('获取展会类别详情失败:', err)
    res.status(500).json(error(err.message || '获取展会类别详情失败', 500))
  }
})

/**
 * 展会类别管理 - 创建类别
 * POST /api/admin/categories
 */
router.post('/categories', authenticateAdmin, async (req, res) => {
  try {
    const data = req.body
    const category = await AdminService.createCategory(data)
    res.json(success(category, '创建展会类别成功'))
  } catch (err) {
    logger.error('创建展会类别失败:', err)
    res.status(500).json(error(err.message || '创建展会类别失败', 500))
  }
})

/**
 * 展会类别管理 - 更新类别
 * PUT /api/admin/categories/:id
 */
router.put('/categories/:id', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params
    const data = req.body
    const category = await AdminService.updateCategory(parseInt(id), data)
    res.json(success(category, '更新展会类别成功'))
  } catch (err) {
    logger.error('更新展会类别失败:', err)
    res.status(500).json(error(err.message || '更新展会类别失败', 500))
  }
})

/**
 * 展会类别管理 - 删除类别
 * DELETE /api/admin/categories/:id
 */
router.delete('/categories/:id', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params
    await AdminService.deleteCategory(parseInt(id))
    res.json(success(null, '删除展会类别成功'))
  } catch (err) {
    logger.error('删除展会类别失败:', err)
    res.status(500).json(error(err.message || '删除展会类别失败', 500))
  }
})

// ==================== 公司管理增删改 ====================

/**
 * 公司管理 - 获取公司详情
 * GET /api/admin/companies/:type/:id
 */
router.get('/companies/:type/:id', authenticateAdmin, async (req, res) => {
  try {
    const { type, id } = req.params
    const company = await AdminService.getCompanyDetail(type, parseInt(id))
    res.json(success(company, '获取公司详情成功'))
  } catch (err) {
    logger.error('获取公司详情失败:', err)
    res.status(500).json(error(err.message || '获取公司详情失败', 500))
  }
})

/**
 * 公司管理 - 更新公司信息
 * PUT /api/admin/companies/:type/:id
 */
router.put('/companies/:type/:id', authenticateAdmin, async (req, res) => {
  try {
    const { type, id } = req.params
    const data = req.body
    const company = await AdminService.updateCompany(type, parseInt(id), data)
    res.json(success(company, '更新公司信息成功'))
  } catch (err) {
    logger.error('更新公司信息失败:', err)
    res.status(500).json(error(err.message || '更新公司信息失败', 500))
  }
})

/**
 * 公司管理 - 删除公司
 * DELETE /api/admin/companies/:type/:id
 */
router.delete('/companies/:type/:id', authenticateAdmin, async (req, res) => {
  try {
    const { type, id } = req.params
    await AdminService.deleteCompany(type, parseInt(id))
    res.json(success(null, '删除公司成功'))
  } catch (err) {
    logger.error('删除公司失败:', err)
    res.status(500).json(error(err.message || '删除公司失败', 500))
  }
})

export default router
