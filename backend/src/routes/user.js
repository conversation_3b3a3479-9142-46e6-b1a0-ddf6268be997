import express from 'express'
import { authenticate } from '../middleware/auth.js'
import { success, error } from '../utils/response.js'
import { logger } from '../utils/logger.js'
import UserService from '../services/UserService.js'

const router = express.Router()

/**
 * 获取用户信息
 * GET /api/user/profile
 */
router.get('/profile', authenticate, async (req, res) => {
  try {
    const userInfo = await UserService.getUserProfile(req.user.id)
    res.json(success(userInfo, '获取用户信息成功'))
  } catch (err) {
    logger.error('获取用户信息失败:', err)
    res.status(500).json(error(err.message || '获取用户信息失败', 500))
  }
})

/**
 * 更新用户信息
 * PUT /api/user/profile
 */
router.put('/profile', authenticate, async (req, res) => {
  try {
    const updateData = req.body
    const userInfo = await UserService.updateUserProfile(req.user.id, updateData)

    res.json(success(userInfo, '更新用户信息成功'))
  } catch (err) {
    logger.error('更新用户信息失败:', err)
    res.status(500).json(error(err.message || '更新用户信息失败', 500))
  }
})

/**
 * 完善用户信息（首次注册）
 * POST /api/user/complete-profile
 */
router.post('/complete-profile', authenticate, async (req, res) => {
  try {
    const profileData = req.body

    if (!profileData.userType || !profileData.companyInfo) {
      return res.status(400).json(error('用户类型和公司信息不能为空', 400))
    }

    const userInfo = await UserService.completeProfile(req.user.id, profileData)

    res.json(success(userInfo, '完善用户信息成功'))
  } catch (err) {
    logger.error('完善用户信息失败:', err)
    res.status(500).json(error(err.message || '完善用户信息失败', 500))
  }
})

export default router
