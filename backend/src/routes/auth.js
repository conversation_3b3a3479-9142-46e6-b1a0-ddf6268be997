import express from 'express'
import { success, error } from '../utils/response.js'
import { logger } from '../utils/logger.js'
import AuthService from '../services/AuthService.js'

const router = express.Router()

/**
 * 微信授权登录
 * POST /api/auth/wechat-login
 */
router.post('/wechat-login', async (req, res) => {
  try {
    const { code } = req.body

    if (!code) {
      return res.status(400).json(error('缺少微信授权码', 400))
    }

    const result = await AuthService.wechatLogin(code)

    res.json(success(result, '登录成功'))

  } catch (err) {
    logger.error('微信登录失败:', err)
    res.status(500).json(error(err.message || '登录失败', 500))
  }
})

/**
 * 微信云开发登录
 * POST /api/auth/cloud-login
 */
router.post('/cloud-login', async (req, res) => {
  try {
    // 从微信云开发请求头中获取用户信息
    const openid = req.headers['x-wx-openid']
    const unionid = req.headers['x-wx-unionid']

    if (!openid) {
      return res.status(400).json(error('无法获取用户信息', 400))
    }

    const result = await AuthService.cloudLogin(openid, unionid)

    res.json(success(result, '登录成功'))

  } catch (err) {
    logger.error('云开发登录失败:', err)
    res.status(500).json(error(err.message || '登录失败', 500))
  }
})

/**
 * 管理员登录
 * POST /api/auth/admin-login
 */
router.post('/admin-login', async (req, res) => {
  try {
    const { username, password } = req.body

    if (!username || !password) {
      return res.status(400).json(error('用户名和密码不能为空', 400))
    }

    const result = await AuthService.adminLogin(username, password)

    res.json(success(result, '登录成功'))

  } catch (err) {
    logger.error('管理员登录失败:', err)
    const statusCode = err.message.includes('用户名或密码错误') ? 401 : 500
    res.status(statusCode).json(error(err.message || '登录失败', statusCode))
  }
})

/**
 * 刷新Token
 * POST /api/auth/refresh-token
 */
router.post('/refresh-token', async (req, res) => {
  try {
    // TODO: 实现Token刷新逻辑
    res.json(success(null, 'Token刷新成功'))
  } catch (err) {
    logger.error('Token刷新失败:', err)
    res.status(500).json(error('Token刷新失败', 500))
  }
})

export default router
