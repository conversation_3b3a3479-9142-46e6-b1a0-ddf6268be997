import express from 'express'
import ExhibitionController from '../controllers/ExhibitionController.js'
import { authenticate } from '../middleware/auth.js'

const router = express.Router()

// 所有展会相关接口都需要登录
router.use(authenticate)

// 获取我的展会列表
router.get('/my', ExhibitionController.getMyExhibitions)

// 获取可参加的展会列表
router.get('/available', ExhibitionController.getAvailableExhibitions)

// 参加展会
router.post('/join', ExhibitionController.joinExhibition)

// 获取展会详情
router.get('/:id', ExhibitionController.getExhibitionDetail)

// 退出展会
router.post('/:id/leave', ExhibitionController.leaveExhibition)

// 更新参展状态
router.put('/participant/:participantId/status', ExhibitionController.updateParticipantStatus)

export default router
