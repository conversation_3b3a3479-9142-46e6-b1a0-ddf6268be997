import ExhibitionService from '../services/ExhibitionService.js'
import { logger } from '../utils/logger.js'

class ExhibitionController {
  /**
   * 获取我的展会列表
   */
  async getMyExhibitions(req, res) {
    try {
      const userId = req.user.id
      const { page = 1, pageSize = 10 } = req.query
      
      const result = await ExhibitionService.getMyExhibitions(userId, {
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      })
      
      res.json({
        code: 200,
        message: '获取成功',
        data: result
      })
    } catch (error) {
      logger.error('获取我的展会列表失败:', error)
      res.status(500).json({
        code: 500,
        message: error.message || '获取我的展会列表失败'
      })
    }
  }

  /**
   * 获取可参加的展会列表
   */
  async getAvailableExhibitions(req, res) {
    try {
      const userId = req.user.id
      const { page = 1, pageSize = 10, keyword } = req.query
      
      const result = await ExhibitionService.getAvailableExhibitions(userId, {
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        keyword
      })
      
      res.json({
        code: 200,
        message: '获取成功',
        data: result
      })
    } catch (error) {
      logger.error('获取可参加展会列表失败:', error)
      res.status(500).json({
        code: 500,
        message: error.message || '获取可参加展会列表失败'
      })
    }
  }

  /**
   * 参加展会
   */
  async joinExhibition(req, res) {
    try {
      const userId = req.user.id
      const { exhibitionId, participantType, isFull = 0 } = req.body

      if (!exhibitionId || !participantType) {
        return res.status(400).json({
          code: 400,
          message: '展会ID和参展类型不能为空'
        })
      }

      const result = await ExhibitionService.joinExhibition(userId, {
        exhibitionId,
        participantType,
        isFull
      })

      res.json({
        code: 200,
        message: '参展成功',
        data: result
      })
    } catch (error) {
      logger.error('参加展会失败:', error)
      res.status(500).json({
        code: 500,
        message: error.message || '参加展会失败'
      })
    }
  }

  /**
   * 获取展会详情
   */
  async getExhibitionDetail(req, res) {
    try {
      const { id } = req.params
      const userId = req.user?.id
      
      const result = await ExhibitionService.getExhibitionDetail(id, userId)
      
      res.json({
        code: 200,
        message: '获取成功',
        data: result
      })
    } catch (error) {
      logger.error('获取展会详情失败:', error)
      res.status(500).json({
        code: 500,
        message: error.message || '获取展会详情失败'
      })
    }
  }

  /**
   * 退出展会
   */
  async leaveExhibition(req, res) {
    try {
      const userId = req.user.id
      const { id } = req.params

      const result = await ExhibitionService.leaveExhibition(userId, id)

      res.json({
        code: 200,
        message: '退出成功',
        data: result
      })
    } catch (error) {
      logger.error('退出展会失败:', error)
      res.status(500).json({
        code: 500,
        message: error.message || '退出展会失败'
      })
    }
  }

  /**
   * 更新参展状态
   */
  async updateParticipantStatus(req, res) {
    try {
      const userId = req.user.id
      const { participantId } = req.params
      const { isFull } = req.body

      if (isFull === undefined) {
        return res.status(400).json({
          code: 400,
          message: '满额状态不能为空'
        })
      }

      const result = await ExhibitionService.updateParticipantStatus(userId, participantId, isFull)

      res.json({
        code: 200,
        message: '更新成功',
        data: result
      })
    } catch (error) {
      logger.error('更新参展状态失败:', error)
      res.status(500).json({
        code: 500,
        message: error.message || '更新参展状态失败'
      })
    }
  }
}

export default new ExhibitionController()
