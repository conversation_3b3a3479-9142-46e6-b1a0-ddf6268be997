import { logger } from '../utils/logger.js'
import { error } from '../utils/response.js'

/**
 * 全局错误处理中间件
 */
export const errorHandler = (err, req, res, next) => {
  logger.error('全局错误处理:', err)

  // Sequelize 验证错误
  if (err.name === 'SequelizeValidationError') {
    const messages = err.errors.map(e => e.message)
    return res.status(400).json(error(messages.join(', '), 400))
  }

  // Sequelize 唯一约束错误
  if (err.name === 'SequelizeUniqueConstraintError') {
    return res.status(400).json(error('数据已存在', 400))
  }

  // Sequelize 外键约束错误
  if (err.name === 'SequelizeForeignKeyConstraintError') {
    return res.status(400).json(error('关联数据不存在', 400))
  }

  // JWT 错误
  if (err.name === 'JsonWebTokenError') {
    return res.status(401).json(error('Token无效', 401))
  }

  if (err.name === 'TokenExpiredError') {
    return res.status(401).json(error('Token已过期', 401))
  }

  // 自定义业务错误
  if (err.status) {
    return res.status(err.status).json(error(err.message, err.status))
  }

  // 默认服务器错误
  res.status(500).json(error('服务器内部错误', 500))
}
