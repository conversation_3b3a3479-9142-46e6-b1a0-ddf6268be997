import { verifyToken, extractToken } from '../utils/jwt.js'
import { error } from '../utils/response.js'
import { logger } from '../utils/logger.js'

/**
 * 用户身份验证中间件
 */
export const authenticate = async (req, res, next) => {
  try {
    const token = extractToken(req)

    if (!token) {
      // 本地测试时，如果没有token，使用默认用户
      logger.warn('未提供认证Token，使用默认用户ID=1进行本地测试')
      req.user = {
        id: 1,
        username: 'testuser',
        type: 'user'
      }
      return next()
    }

    const decoded = verifyToken(token)
    req.user = decoded

    next()
  } catch (err) {
    logger.error('身份验证失败:', err)
    // 本地测试时，即使token验证失败，也使用默认用户
    logger.warn('Token验证失败，使用默认用户ID=1进行本地测试')
    req.user = {
      id: 1,
      username: 'testuser',
      type: 'user'
    }
    next()
  }
}

/**
 * 管理员身份验证中间件
 */
export const authenticateAdmin = async (req, res, next) => {
  try {
    const token = extractToken(req)
    
    if (!token) {
      return res.status(401).json(error('未提供认证Token', 401))
    }

    const decoded = verifyToken(token)
    
    // 检查是否为管理员
    if (decoded.type !== 'admin') {
      return res.status(403).json(error('权限不足', 403))
    }
    
    req.admin = decoded
    next()
  } catch (err) {
    logger.error('管理员身份验证失败:', err)
    return res.status(401).json(error(err.message, 401))
  }
}

/**
 * 可选身份验证中间件（不强制要求登录）
 */
export const optionalAuth = async (req, res, next) => {
  try {
    const token = extractToken(req)
    
    if (token) {
      const decoded = verifyToken(token)
      req.user = decoded
    }
    
    next()
  } catch (err) {
    // 可选认证失败时不阻止请求继续
    logger.warn('可选身份验证失败:', err)
    next()
  }
}
