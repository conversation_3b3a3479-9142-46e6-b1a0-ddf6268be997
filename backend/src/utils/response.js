/**
 * 统一响应格式工具
 */

/**
 * 成功响应
 * @param {*} data 响应数据
 * @param {string} message 响应消息
 * @param {number} code 响应码
 */
export const success = (data = null, message = 'success', code = 200) => {
  return {
    code,
    message,
    data,
    timestamp: new Date().toISOString()
  }
}

/**
 * 失败响应
 * @param {string} message 错误消息
 * @param {number} code 错误码
 * @param {*} data 错误数据
 */
export const error = (message = 'error', code = 500, data = null) => {
  return {
    code,
    message,
    data,
    timestamp: new Date().toISOString()
  }
}

/**
 * 分页响应
 * @param {Array} list 数据列表
 * @param {number} total 总数
 * @param {number} page 当前页
 * @param {number} pageSize 每页大小
 * @param {string} message 响应消息
 */
export const paginate = (list = [], total = 0, page = 1, pageSize = 10, message = 'success') => {
  return {
    code: 200,
    message,
    data: {
      list,
      pagination: {
        total,
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        totalPages: Math.ceil(total / pageSize)
      }
    },
    timestamp: new Date().toISOString()
  }
}

/**
 * 响应中间件
 */
export const responseMiddleware = (req, res, next) => {
  // 成功响应方法
  res.success = (data, message, code) => {
    res.json(success(data, message, code))
  }

  // 失败响应方法
  res.error = (message, code, data) => {
    res.status(code || 500).json(error(message, code, data))
  }

  // 分页响应方法
  res.paginate = (list, total, page, pageSize, message) => {
    res.json(paginate(list, total, page, pageSize, message))
  }

  next()
}
