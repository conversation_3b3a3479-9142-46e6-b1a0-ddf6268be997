import jwt from 'jsonwebtoken'
import { logger } from './logger.js'

const JWT_SECRET = process.env.JWT_SECRET || 'guanpinpin_secret_key'
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d'

/**
 * 生成JWT Token
 * @param {Object} payload 载荷数据
 * @param {string} expiresIn 过期时间
 * @returns {string} JWT Token
 */
export const generateToken = (payload, expiresIn = JWT_EXPIRES_IN) => {
  try {
    return jwt.sign(payload, JWT_SECRET, { expiresIn })
  } catch (error) {
    logger.error('生成JWT Token失败:', error)
    throw new Error('生成Token失败')
  }
}

/**
 * 验证JWT Token
 * @param {string} token JWT Token
 * @returns {Object} 解码后的载荷数据
 */
export const verifyToken = (token) => {
  try {
    return jwt.verify(token, JWT_SECRET)
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      throw new Error('Token已过期')
    } else if (error.name === 'JsonWebTokenError') {
      throw new Error('Token无效')
    } else {
      logger.error('验证JWT Token失败:', error)
      throw new Error('Token验证失败')
    }
  }
}

/**
 * 从请求头中提取Token
 * @param {Object} req Express请求对象
 * @returns {string|null} JWT Token
 */
export const extractToken = (req) => {
  const authHeader = req.headers.authorization
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7)
  }
  return null
}
