import { Sequelize } from 'sequelize'
import dotenv from 'dotenv'
import { logger } from '../utils/logger.js'

dotenv.config()

// 数据库配置
const config = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  database: process.env.DB_NAME || 'guanpinpin',
  username: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  dialect: 'mysql',
  timezone: '+08:00',
  pool: {
    max: 10, // 最大连接数（考虑服务器资源限制）
    min: 0,
    acquire: 30000,
    idle: 10000
  },
  logging: (sql) => {
    if (process.env.NODE_ENV === 'development') {
      logger.debug(`SQL: ${sql}`)
    }
  },
  define: {
    timestamps: true,
    underscored: true,
    freezeTableName: true
  }
}

// 创建Sequelize实例
const sequelize = new Sequelize(
  config.database,
  config.username,
  config.password,
  config
)

// 测试数据库连接
const testConnection = async () => {
  try {
    await sequelize.authenticate()
    logger.info('数据库连接成功')
  } catch (error) {
    if (error.message.includes('Unknown database')) {
      logger.warn('数据库不存在，尝试创建数据库...')
      try {
        // 创建一个不指定数据库的连接来创建数据库
        const { Sequelize } = await import('sequelize')
        const tempSequelize = new Sequelize('', config.username, config.password, {
          ...config,
          database: undefined
        })

        await tempSequelize.query(`CREATE DATABASE IF NOT EXISTS \`${config.database}\``)
        await tempSequelize.close()

        // 重新测试连接
        await sequelize.authenticate()
        logger.info('数据库创建成功并连接')
      } catch (createError) {
        logger.error('创建数据库失败:', createError)
        throw createError
      }
    } else {
      logger.error('数据库连接失败:', error)
      throw error
    }
  }
}

// 同步数据库模型
const syncDatabase = async (force = false) => {
  try {
    await sequelize.sync({ force })
    logger.info('数据库模型同步成功')
  } catch (error) {
    logger.error('数据库模型同步失败:', error)
    throw error
  }
}

export { sequelize, testConnection, syncDatabase }
