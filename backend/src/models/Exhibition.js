import { DataTypes } from 'sequelize'
import { sequelize } from '../config/database.js'

const Exhibition = sequelize.define('Exhibition', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '展会ID'
  },
  venue_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '展馆ID'
  },
  city: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '城市'
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '展会名称'
  },
  start_date: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    comment: '开始日期'
  },
  end_date: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    comment: '结束日期'
  },
  category_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '展会类别ID'
  },
  status: {
    type: DataTypes.TINYINT,
    allowNull: false,
    defaultValue: 0,
    comment: '状态：0未开始 1进行中 9已结束',
    validate: {
      isIn: [[0, 1, 9]]
    }
  },
  is_deleted: {
    type: DataTypes.TINYINT,
    allowNull: false,
    defaultValue: 0,
    comment: '是否删除：0否 1是'
  }
}, {
  tableName: 'exhibitions',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['venue_id']
    },
    {
      fields: ['category_id']
    },
    {
      fields: ['start_date']
    },
    {
      fields: ['end_date']
    },
    {
      fields: ['status']
    },
    {
      fields: ['is_deleted']
    },
    {
      fields: ['start_date', 'end_date']
    }
  ]
})

export default Exhibition
