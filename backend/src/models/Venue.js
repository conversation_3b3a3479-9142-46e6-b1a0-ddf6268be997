import { DataTypes } from 'sequelize'
import { sequelize } from '../config/database.js'

const Venue = sequelize.define('Venue', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '展馆ID'
  },
  city: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '所在城市'
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '展馆名称'
  },
  address: {
    type: DataTypes.STRING(200),
    allowNull: true,
    comment: '展馆地址'
  },
  is_deleted: {
    type: DataTypes.TINYINT,
    allowNull: false,
    defaultValue: 0,
    comment: '是否删除：0否 1是'
  }
}, {
  tableName: 'venues',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['city']
    },
    {
      fields: ['name']
    },
    {
      fields: ['is_deleted']
    }
  ]
})

export default Venue
