import { DataTypes } from 'sequelize'
import { sequelize } from '../config/database.js'

const UserFavorite = sequelize.define('UserFavorite', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '收藏ID'
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '用户ID'
  },
  favorite_type: {
    type: DataTypes.TINYINT,
    allowNull: false,
    comment: '收藏类型：2设计公司 3工厂 4综合商',
    validate: {
      isIn: [[2, 3, 4]]
    }
  },
  favorite_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '收藏对象ID'
  },
  is_deleted: {
    type: DataTypes.TINYINT,
    allowNull: false,
    defaultValue: 0,
    comment: '是否删除：0否 1是'
  }
}, {
  tableName: 'user_favorites',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: false, // 收藏表不需要更新时间
  indexes: [
    {
      fields: ['user_id']
    },
    {
      fields: ['favorite_type', 'favorite_id']
    },
    {
      fields: ['is_deleted']
    },
    {
      unique: true,
      fields: ['user_id', 'favorite_type', 'favorite_id'],
      name: 'uk_favorites_user_type_id'
    }
  ]
})

export default UserFavorite
