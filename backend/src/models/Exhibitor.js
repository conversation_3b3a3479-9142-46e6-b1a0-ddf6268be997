import { DataTypes } from 'sequelize'
import { sequelize } from '../config/database.js'

const Exhibitor = sequelize.define('Exhibitor', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '参展商ID'
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '用户ID'
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '公司名称'
  },
  contact_person: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '联系人'
  },
  contact_phone: {
    type: DataTypes.STRING(20),
    allowNull: true,
    comment: '联系电话'
  },
  exhibition_category_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '参展类别ID'
  },
  is_deleted: {
    type: DataTypes.TINYINT,
    allowNull: false,
    defaultValue: 0,
    comment: '是否删除：0否 1是'
  }
}, {
  tableName: 'exhibitors',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['user_id']
    },
    {
      fields: ['exhibition_category_id']
    },
    {
      fields: ['is_deleted']
    }
  ]
})

export default Exhibitor
