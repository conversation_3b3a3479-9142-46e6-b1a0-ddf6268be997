import { sequelize } from '../config/database.js'
import User from './User.js'
import ExhibitionCategory from './ExhibitionCategory.js'
import Venue from './Venue.js'
import Exhibition from './Exhibition.js'
import Factory from './Factory.js'
import DesignCompany from './DesignCompany.js'
import Exhibitor from './Exhibitor.js'
import ExhibitionParticipant from './ExhibitionParticipant.js'
import UserFavorite from './UserFavorite.js'
import Admin from './Admin.js'

// 定义模型关联关系
const defineAssociations = () => {
  // 用户与各业务表的关联
  User.hasOne(Factory, { foreignKey: 'user_id', as: 'factory' })
  User.hasOne(DesignCompany, { foreignKey: 'user_id', as: 'designCompany' })
  User.hasOne(Exhibitor, { foreignKey: 'user_id', as: 'exhibitor' })
  User.hasMany(UserFavorite, { foreignKey: 'user_id', as: 'favorites' })

  Factory.belongsTo(User, { foreignKey: 'user_id', as: 'user' })
  DesignCompany.belongsTo(User, { foreignKey: 'user_id', as: 'user' })
  Exhibitor.belongsTo(User, { foreignKey: 'user_id', as: 'user' })

  // 展会相关关联
  Exhibition.belongsTo(Venue, { foreignKey: 'venue_id', as: 'venue' })
  Exhibition.belongsTo(ExhibitionCategory, { foreignKey: 'category_id', as: 'category' })
  Exhibition.hasMany(ExhibitionParticipant, { foreignKey: 'exhibition_id', as: 'participants' })

  Venue.hasMany(Exhibition, { foreignKey: 'venue_id', as: 'exhibitions' })
  ExhibitionCategory.hasMany(Exhibition, { foreignKey: 'category_id', as: 'exhibitions' })
  ExhibitionCategory.hasMany(Exhibitor, { foreignKey: 'exhibition_category_id', as: 'exhibitors' })

  // 参展信息关联
  ExhibitionParticipant.belongsTo(Exhibition, { foreignKey: 'exhibition_id', as: 'exhibition' })
  ExhibitionParticipant.belongsTo(User, { foreignKey: 'user_id', as: 'user' })

  User.hasMany(ExhibitionParticipant, { foreignKey: 'user_id', as: 'participations' })

  // 收藏关联
  UserFavorite.belongsTo(User, { foreignKey: 'user_id', as: 'user' })

  // 参展商与展会类别关联
  Exhibitor.belongsTo(ExhibitionCategory, { foreignKey: 'exhibition_category_id', as: 'category' })
}

// 初始化关联关系
defineAssociations()

// 导出所有模型
export {
  sequelize,
  User,
  ExhibitionCategory,
  Venue,
  Exhibition,
  Factory,
  DesignCompany,
  Exhibitor,
  ExhibitionParticipant,
  UserFavorite,
  Admin
}
