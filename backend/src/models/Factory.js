import { DataTypes } from 'sequelize'
import { sequelize } from '../config/database.js'

const Factory = sequelize.define('Factory', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '工厂ID'
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '用户ID'
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '工厂名称'
  },
  contact_person: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '联系人'
  },
  contact_phone: {
    type: DataTypes.STRING(20),
    allowNull: true,
    comment: '联系电话'
  },
  city: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '所在城市'
  },
  specialties: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '特长：钢结构、木结构等'
  },
  level: {
    type: DataTypes.TINYINT,
    allowNull: false,
    defaultValue: 1,
    comment: '级别：1-5星',
    validate: {
      min: 1,
      max: 5
    }
  },
  is_deleted: {
    type: DataTypes.TINYINT,
    allowNull: false,
    defaultValue: 0,
    comment: '是否删除：0否 1是'
  }
}, {
  tableName: 'factories',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['user_id']
    },
    {
      fields: ['city']
    },
    {
      fields: ['level']
    },
    {
      fields: ['is_deleted']
    }
  ]
})

export default Factory
