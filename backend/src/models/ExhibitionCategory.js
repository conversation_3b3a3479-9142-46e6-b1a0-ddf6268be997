import { DataTypes } from 'sequelize'
import { sequelize } from '../config/database.js'

const ExhibitionCategory = sequelize.define('ExhibitionCategory', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '类别ID'
  },
  name: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '类别名称'
  },
  is_deleted: {
    type: DataTypes.TINYINT,
    allowNull: false,
    defaultValue: 0,
    comment: '是否删除：0否 1是'
  }
}, {
  tableName: 'exhibition_categories',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['name']
    },
    {
      fields: ['is_deleted']
    }
  ]
})

export default ExhibitionCategory
