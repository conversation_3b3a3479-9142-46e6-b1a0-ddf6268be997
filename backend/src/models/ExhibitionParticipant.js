import { DataTypes } from 'sequelize'
import { sequelize } from '../config/database.js'

const ExhibitionParticipant = sequelize.define('ExhibitionParticipant', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '参展信息ID'
  },
  exhibition_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '展会ID'
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '用户ID'
  },
  participant_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '参展方ID（对应工厂或设计公司ID）'
  },
  participant_type: {
    type: DataTypes.TINYINT,
    allowNull: false,
    comment: '参展方类型：2设计公司 3工厂 4综合商',
    validate: {
      isIn: [[2, 3, 4]]
    }
  },
  is_full: {
    type: DataTypes.TINYINT,
    allowNull: false,
    defaultValue: 0,
    comment: '是否满额：0否 1是'
  },
  is_deleted: {
    type: DataTypes.TINYINT,
    allowNull: false,
    defaultValue: 0,
    comment: '是否删除：0否 1是'
  }
}, {
  tableName: 'exhibition_participants',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['exhibition_id']
    },
    {
      fields: ['user_id']
    },
    {
      fields: ['participant_id']
    },
    {
      fields: ['participant_type']
    },
    {
      fields: ['is_full']
    },
    {
      fields: ['is_deleted']
    },
    {
      fields: ['exhibition_id', 'user_id'],
      unique: true,
      where: {
        is_deleted: 0
      }
    },
    {
      fields: ['exhibition_id', 'participant_type']
    }
  ]
})

export default ExhibitionParticipant
