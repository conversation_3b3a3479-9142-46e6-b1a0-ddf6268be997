{"name": "guanpinpin-backend", "version": "1.0.0", "description": "馆拼拼后端API服务", "main": "src/app.js", "type": "module", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "migrate": "node src/scripts/migrate.js", "seed": "node src/scripts/seed.js", "lint": "eslint src/ --fix", "format": "prettier --write src/"}, "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.5", "sequelize": "^6.35.2", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "winston": "^3.11.0", "dotenv": "^16.3.1", "joi": "^17.11.0", "axios": "^1.6.2"}, "devDependencies": {"nodemon": "^3.0.2", "eslint": "^8.55.0", "prettier": "^3.1.1"}}