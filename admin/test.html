<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>馆拼拼后台管理系统 - 测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .login-form {
            max-width: 400px;
            margin: 0 auto;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 12px;
            background-color: #409eff;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #337ecc;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            display: none;
        }
        .success {
            background-color: #f0f9ff;
            border: 1px solid #409eff;
            color: #409eff;
        }
        .error {
            background-color: #fef2f2;
            border: 1px solid #f56565;
            color: #f56565;
        }
        .api-test {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        .api-result {
            margin-top: 10px;
            padding: 10px;
            background-color: white;
            border-radius: 4px;
            border: 1px solid #ddd;
            font-family: monospace;
            font-size: 14px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>馆拼拼后台管理系统</h1>
            <p>测试页面 - 验证API连接</p>
        </div>

        <div class="login-form">
            <h3>管理员登录测试</h3>
            <form id="loginForm">
                <div class="form-group">
                    <label for="username">用户名:</label>
                    <input type="text" id="username" value="admin" required>
                </div>
                <div class="form-group">
                    <label for="password">密码:</label>
                    <input type="password" id="password" value="gpp13141234" required>
                </div>
                <button type="submit" id="loginBtn">登录测试</button>
            </form>
            <div id="loginResult" class="result"></div>
        </div>

        <div class="api-test">
            <h3>API连接测试</h3>
            <button onclick="testHealthAPI()">测试健康检查API</button>
            <div id="healthResult" class="api-result" style="display: none;"></div>
            
            <button onclick="testStatsAPI()" style="margin-top: 10px;">测试统计数据API</button>
            <div id="statsResult" class="api-result" style="display: none;"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001/api';
        let authToken = '';

        // 登录测试
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('loginBtn');
            const resultDiv = document.getElementById('loginResult');
            
            loginBtn.disabled = true;
            loginBtn.textContent = '登录中...';
            
            try {
                const response = await fetch(`${API_BASE}/auth/admin-login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (data.code === 200) {
                    authToken = data.data.token;
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `登录成功！管理员: ${data.data.adminInfo.username}`;
                    resultDiv.style.display = 'block';
                } else {
                    throw new Error(data.message || '登录失败');
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `登录失败: ${error.message}`;
                resultDiv.style.display = 'block';
            } finally {
                loginBtn.disabled = false;
                loginBtn.textContent = '登录测试';
            }
        });

        // 健康检查API测试
        async function testHealthAPI() {
            const resultDiv = document.getElementById('healthResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '正在测试健康检查API...';
            
            try {
                const response = await fetch('http://localhost:3001/health');
                const data = await response.json();
                resultDiv.textContent = `健康检查API响应:\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.textContent = `健康检查API错误: ${error.message}`;
            }
        }

        // 统计数据API测试
        async function testStatsAPI() {
            const resultDiv = document.getElementById('statsResult');
            resultDiv.style.display = 'block';
            
            if (!authToken) {
                resultDiv.textContent = '请先登录获取Token';
                return;
            }
            
            resultDiv.textContent = '正在测试统计数据API...';
            
            try {
                const response = await fetch(`${API_BASE}/admin/stats`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                const data = await response.json();
                resultDiv.textContent = `统计数据API响应:\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.textContent = `统计数据API错误: ${error.message}`;
            }
        }
    </script>
</body>
</html>
