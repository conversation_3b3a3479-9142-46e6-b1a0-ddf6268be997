import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { adminLogin } from '@/api/auth'

export const useAdminStore = defineStore('admin', () => {
  // 状态
  const token = ref(localStorage.getItem('admin_token') || '')
  const adminInfo = ref(null)
  const isInitialized = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)

  // 设置Token
  const setToken = (newToken) => {
    token.value = newToken
    if (newToken) {
      localStorage.setItem('admin_token', newToken)
    } else {
      localStorage.removeItem('admin_token')
    }
  }

  // 设置管理员信息
  const setAdminInfo = (info) => {
    adminInfo.value = info
  }

  // 管理员登录
  const login = async (loginData) => {
    try {
      const response = await adminLogin(loginData)
      const { token: newToken, adminInfo: info } = response.data
      
      setToken(newToken)
      setAdminInfo(info)
      
      ElMessage.success('登录成功')
      return true
    } catch (error) {
      ElMessage.error(error.message || '登录失败')
      throw error
    }
  }

  // 退出登录
  const logout = () => {
    setToken('')
    setAdminInfo(null)
    isInitialized.value = false
    ElMessage.success('已退出登录')
  }

  // 初始化应用
  const initApp = async () => {
    if (isInitialized.value) return
    
    if (isLoggedIn.value) {
      // 如果有token，可以在这里验证token有效性
      // 暂时跳过，直接设置为已初始化
    }
    
    isInitialized.value = true
  }

  return {
    // 状态
    token,
    adminInfo,
    isInitialized,
    
    // 计算属性
    isLoggedIn,
    
    // 方法
    setToken,
    setAdminInfo,
    login,
    logout,
    initApp
  }
})
