import axios from 'axios'
import { ElMessage } from 'element-plus'
import { useAdminStore } from '@/stores/admin'

// 创建axios实例
const request = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    const adminStore = useAdminStore()
    
    // 添加token到请求头
    if (adminStore.token) {
      config.headers.Authorization = `Bearer ${adminStore.token}`
    }
    
    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    const { code, message, data } = response.data
    
    // 请求成功
    if (code === 200) {
      return response.data
    }
    
    // 业务错误
    ElMessage.error(message || '请求失败')
    return Promise.reject(new Error(message || '请求失败'))
  },
  (error) => {
    console.error('响应拦截器错误:', error)
    
    if (error.response) {
      const { status, data } = error.response
      
      // 401 未授权，清除登录状态
      if (status === 401) {
        const adminStore = useAdminStore()
        adminStore.logout()
        ElMessage.error('登录已过期，请重新登录')
        return Promise.reject(new Error('登录已过期'))
      }
      
      // 其他HTTP错误
      const message = data?.message || `请求失败 (${status})`
      ElMessage.error(message)
      return Promise.reject(new Error(message))
    }
    
    // 网络错误
    if (error.code === 'NETWORK_ERROR' || error.message === 'Network Error') {
      ElMessage.error('网络连接失败，请检查网络')
      return Promise.reject(new Error('网络连接失败'))
    }
    
    // 超时错误
    if (error.code === 'ECONNABORTED') {
      ElMessage.error('请求超时，请重试')
      return Promise.reject(new Error('请求超时'))
    }
    
    // 其他错误
    ElMessage.error('请求失败，请重试')
    return Promise.reject(error)
  }
)

export default request
