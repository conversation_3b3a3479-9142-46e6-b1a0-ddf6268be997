import request from './request'

// ==================== 统计数据 ====================

/**
 * 获取统计数据
 */
export const getStats = () => {
  return request.get('/admin/stats')
}

// ==================== 用户管理 ====================

/**
 * 获取用户列表
 * @param {Object} params 查询参数
 */
export const getUsers = (params) => {
  return request.get('/admin/users', { params })
}

/**
 * 删除用户
 * @param {number} id 用户ID
 */
export const deleteUser = (id) => {
  return request.delete(`/admin/users/${id}`)
}

/**
 * 更新用户
 * @param {number} id 用户ID
 * @param {Object} data 更新数据
 */
export const updateUser = (id, data) => {
  return request.put(`/admin/users/${id}`, data)
}

// ==================== 展会管理 ====================

/**
 * 获取展会列表
 * @param {Object} params 查询参数
 */
export const getExhibitions = (params) => {
  return request.get('/admin/exhibitions', { params })
}

/**
 * 获取展会详情
 * @param {number} id 展会ID
 */
export const getExhibitionDetail = (id) => {
  return request.get(`/admin/exhibitions/${id}`)
}

/**
 * 创建展会
 * @param {Object} data 展会数据
 */
export const createExhibition = (data) => {
  return request.post('/admin/exhibitions', data)
}

/**
 * 更新展会
 * @param {number} id 展会ID
 * @param {Object} data 更新数据
 */
export const updateExhibition = (id, data) => {
  return request.put(`/admin/exhibitions/${id}`, data)
}

/**
 * 删除展会
 * @param {number} id 展会ID
 */
export const deleteExhibition = (id) => {
  return request.delete(`/admin/exhibitions/${id}`)
}

// ==================== 展馆管理 ====================

/**
 * 获取展馆列表
 * @param {Object} params 查询参数
 */
export const getVenues = (params) => {
  return request.get('/admin/venues', { params })
}

/**
 * 获取展馆详情
 * @param {number} id 展馆ID
 */
export const getVenueDetail = (id) => {
  return request.get(`/admin/venues/${id}`)
}

/**
 * 创建展馆
 * @param {Object} data 展馆数据
 */
export const createVenue = (data) => {
  return request.post('/admin/venues', data)
}

/**
 * 更新展馆
 * @param {number} id 展馆ID
 * @param {Object} data 更新数据
 */
export const updateVenue = (id, data) => {
  return request.put(`/admin/venues/${id}`, data)
}

/**
 * 删除展馆
 * @param {number} id 展馆ID
 */
export const deleteVenue = (id) => {
  return request.delete(`/admin/venues/${id}`)
}

// ==================== 展会类别管理 ====================

/**
 * 获取展会类别列表
 * @param {Object} params 查询参数
 */
export const getCategories = (params) => {
  return request.get('/admin/categories', { params })
}

/**
 * 获取展会类别详情
 * @param {number} id 类别ID
 */
export const getCategoryDetail = (id) => {
  return request.get(`/admin/categories/${id}`)
}

/**
 * 创建展会类别
 * @param {Object} data 类别数据
 */
export const createCategory = (data) => {
  return request.post('/admin/categories', data)
}

/**
 * 更新展会类别
 * @param {number} id 类别ID
 * @param {Object} data 更新数据
 */
export const updateCategory = (id, data) => {
  return request.put(`/admin/categories/${id}`, data)
}

/**
 * 删除展会类别
 * @param {number} id 类别ID
 */
export const deleteCategory = (id) => {
  return request.delete(`/admin/categories/${id}`)
}

// ==================== 公司管理 ====================

/**
 * 获取公司列表
 * @param {Object} params 查询参数
 */
export const getCompanies = (params) => {
  return request.get('/admin/companies', { params })
}

/**
 * 获取公司详情
 * @param {string} type 公司类型
 * @param {number} id 公司ID
 */
export const getCompanyDetail = (type, id) => {
  return request.get(`/admin/companies/${type}/${id}`)
}

/**
 * 更新公司信息
 * @param {string} type 公司类型
 * @param {number} id 公司ID
 * @param {Object} data 更新数据
 */
export const updateCompany = (type, id, data) => {
  return request.put(`/admin/companies/${type}/${id}`, data)
}

/**
 * 删除公司
 * @param {string} type 公司类型
 * @param {number} id 公司ID
 */
export const deleteCompany = (type, id) => {
  return request.delete(`/admin/companies/${type}/${id}`)
}

// ==================== 参展管理 ====================

/**
 * 获取参展列表
 * @param {Object} params 查询参数
 */
export const getParticipants = (params) => {
  return request.get('/admin/participants', { params })
}

/**
 * 获取参展详情
 * @param {number} id 参展ID
 */
export const getParticipantDetail = (id) => {
  return request.get(`/admin/participants/${id}`)
}

/**
 * 创建参展信息
 * @param {Object} data 参展数据
 */
export const createParticipant = (data) => {
  return request.post('/admin/participants', data)
}

/**
 * 更新参展信息
 * @param {number} id 参展ID
 * @param {Object} data 更新数据
 */
export const updateParticipant = (id, data) => {
  return request.put(`/admin/participants/${id}`, data)
}

/**
 * 删除参展信息
 * @param {number} id 参展ID
 */
export const deleteParticipant = (id) => {
  return request.delete(`/admin/participants/${id}`)
}

/**
 * 获取可选参展方列表
 * @param {number} type 参展方类型
 */
export const getAvailableParticipants = (type) => {
  return request.get(`/admin/participants/available/${type}`)
}
