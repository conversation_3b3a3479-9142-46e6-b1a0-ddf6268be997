<template>
  <div class="admin-layout">
    <!-- 顶部导航 -->
    <el-header class="admin-header">
      <div class="logo">
        <el-icon><Shop /></el-icon>
        馆拼拼后台管理
      </div>
      
      <div class="user-info">
        <span>欢迎，{{ adminStore.adminInfo?.username || '管理员' }}</span>
        <el-dropdown @command="handleCommand">
          <el-button type="text">
            <el-icon><User /></el-icon>
            <el-icon><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="logout">
                <el-icon><SwitchButton /></el-icon>
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-header>
    
    <el-container>
      <!-- 侧边栏 -->
      <el-aside width="200px" class="admin-sidebar">
        <el-menu
          :default-active="activeMenu"
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409eff"
          router
        >
          <el-menu-item
            v-for="item in menuItems"
            :key="item.path"
            :index="item.path"
          >
            <el-icon><component :is="item.icon" /></el-icon>
            <span>{{ item.title }}</span>
          </el-menu-item>
        </el-menu>
      </el-aside>
      
      <!-- 主内容区 -->
      <el-main class="admin-main">
        <router-view />
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import {
  Shop,
  User,
  ArrowDown,
  SwitchButton,
  House,
  Calendar,
  OfficeBuilding,
  Collection,
  Tickets,
  DataAnalysis
} from '@element-plus/icons-vue'
import { useAdminStore } from '@/stores/admin'

const route = useRoute()
const router = useRouter()
const adminStore = useAdminStore()

// 当前激活的菜单
const activeMenu = computed(() => route.path)

// 菜单项
const menuItems = [
  {
    path: '/dashboard/home',
    title: '首页',
    icon: 'House'
  },
  {
    path: '/dashboard/users',
    title: '用户管理',
    icon: 'User'
  },
  {
    path: '/dashboard/exhibitions',
    title: '展会管理',
    icon: 'Calendar'
  },
  {
    path: '/dashboard/venues',
    title: '展馆管理',
    icon: 'OfficeBuilding'
  },
  {
    path: '/dashboard/categories',
    title: '展会类别',
    icon: 'Collection'
  },
  {
    path: '/dashboard/companies',
    title: '公司管理',
    icon: 'Shop'
  },
  {
    path: '/dashboard/participants',
    title: '参展管理',
    icon: 'Tickets'
  },
  {
    path: '/dashboard/statistics',
    title: '数据统计',
    icon: 'DataAnalysis'
  }
]

// 处理下拉菜单命令
const handleCommand = async (command) => {
  if (command === 'logout') {
    try {
      await ElMessageBox.confirm(
        '确定要退出登录吗？',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
      
      adminStore.logout()
      router.push('/login')
    } catch (error) {
      // 用户取消
    }
  }
}
</script>
