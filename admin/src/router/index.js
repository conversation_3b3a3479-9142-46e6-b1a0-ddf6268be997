import { createRouter, createWebHistory } from 'vue-router'
import { useAdminStore } from '@/stores/admin'

// 路由配置
const routes = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { title: '管理员登录' }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/layout/index.vue'),
    redirect: '/dashboard/home',
    meta: { title: '控制台', requiresAuth: true },
    children: [
      {
        path: 'home',
        name: 'DashboardHome',
        component: () => import('@/views/Dashboard/Home.vue'),
        meta: { title: '首页', icon: 'House' }
      },
      {
        path: 'users',
        name: 'Users',
        component: () => import('@/views/Users/<USER>'),
        meta: { title: '用户管理', icon: 'User' }
      },
      {
        path: 'exhibitions',
        name: 'Exhibitions',
        component: () => import('@/views/Exhibitions/index.vue'),
        meta: { title: '展会管理', icon: 'Calendar' }
      },
      {
        path: 'venues',
        name: 'Venues',
        component: () => import('@/views/Venues/index.vue'),
        meta: { title: '展馆管理', icon: 'OfficeBuilding' }
      },
      {
        path: 'categories',
        name: 'Categories',
        component: () => import('@/views/Categories/index.vue'),
        meta: { title: '展会类别', icon: 'Collection' }
      },
      {
        path: 'companies',
        name: 'Companies',
        component: () => import('@/views/Companies/index.vue'),
        meta: { title: '公司管理', icon: 'Shop' }
      },
      {
        path: 'participants',
        name: 'Participants',
        component: () => import('@/views/Participants/index.vue'),
        meta: { title: '参展管理', icon: 'Tickets' }
      },
      {
        path: 'statistics',
        name: 'Statistics',
        component: () => import('@/views/Statistics/index.vue'),
        meta: { title: '数据统计', icon: 'DataAnalysis' }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const adminStore = useAdminStore()
  
  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - 馆拼拼后台管理` : '馆拼拼后台管理'
  
  // 检查是否需要登录
  if (to.meta.requiresAuth) {
    if (!adminStore.isLoggedIn) {
      // 未登录，跳转到登录页
      next('/login')
      return
    }
  }
  
  // 已登录管理员访问登录页，跳转到首页
  if (to.name === 'Login' && adminStore.isLoggedIn) {
    next('/dashboard')
    return
  }
  
  next()
})

export default router
