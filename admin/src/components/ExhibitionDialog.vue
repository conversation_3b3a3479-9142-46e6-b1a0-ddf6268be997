<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑展会' : '新增展会'"
    width="600px"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="展会名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入展会名称" />
      </el-form-item>
      
      <el-form-item label="展馆" prop="venueId">
        <el-select v-model="form.venueId" placeholder="请选择展馆" style="width: 100%">
          <el-option
            v-for="venue in venues"
            :key="venue.id"
            :label="venue.name"
            :value="venue.id"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="展会类别" prop="categoryId">
        <el-select v-model="form.categoryId" placeholder="请选择展会类别" style="width: 100%">
          <el-option
            v-for="category in categories"
            :key="category.id"
            :label="category.name"
            :value="category.id"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="开始日期" prop="startDate">
        <el-date-picker
          v-model="form.startDate"
          type="date"
          placeholder="请选择开始日期"
          style="width: 100%"
        />
      </el-form-item>
      
      <el-form-item label="结束日期" prop="endDate">
        <el-date-picker
          v-model="form.endDate"
          type="date"
          placeholder="请选择结束日期"
          style="width: 100%"
        />
      </el-form-item>
      
      <el-form-item label="状态" prop="status">
        <el-select v-model="form.status" placeholder="请选择状态" style="width: 100%">
          <el-option label="未开始" :value="0" />
          <el-option label="进行中" :value="1" />
          <el-option label="已结束" :value="9" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入展会描述"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { createExhibition, updateExhibition, getExhibitionDetail } from '@/api/admin'
import { getVenues, getCategories } from '@/api/admin'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  exhibitionId: {
    type: Number,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

const visible = ref(false)
const loading = ref(false)
const formRef = ref()
const venues = ref([])
const categories = ref([])

const isEdit = computed(() => !!props.exhibitionId)

// 表单数据
const form = reactive({
  name: '',
  venueId: '',
  categoryId: '',
  startDate: '',
  endDate: '',
  status: 0,
  description: ''
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入展会名称', trigger: 'blur' }
  ],
  venueId: [
    { required: true, message: '请选择展馆', trigger: 'change' }
  ],
  categoryId: [
    { required: true, message: '请选择展会类别', trigger: 'change' }
  ],
  startDate: [
    { required: true, message: '请选择开始日期', trigger: 'change' }
  ],
  endDate: [
    { required: true, message: '请选择结束日期', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 监听显示状态
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val) {
    fetchOptions()
    if (props.exhibitionId) {
      fetchExhibitionDetail()
    } else {
      resetForm()
    }
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 获取选项数据
const fetchOptions = async () => {
  try {
    const [venuesRes, categoriesRes] = await Promise.all([
      getVenues({ pageSize: 100 }),
      getCategories({ pageSize: 100 })
    ])

    // 处理展馆数据 - 展馆API返回的是直接数组
    venues.value = Array.isArray(venuesRes.data) ? venuesRes.data : []

    // 处理类别数据 - 类别API返回的是直接数组
    categories.value = Array.isArray(categoriesRes.data) ? categoriesRes.data : []

    console.log('展馆数据:', venues.value)
    console.log('类别数据:', categories.value)
  } catch (error) {
    console.error('获取选项数据失败:', error)
    ElMessage.error('获取选项数据失败')
  }
}

// 获取展会详情
const fetchExhibitionDetail = async () => {
  try {
    const response = await getExhibitionDetail(props.exhibitionId)
    const data = response.data

    // 映射数据库字段到表单字段
    form.name = data.name || ''
    form.venueId = data.venue_id || ''
    form.categoryId = data.category_id || ''
    form.status = data.status || 0
    form.description = data.description || ''

    // 处理日期格式
    if (data.start_date) {
      form.startDate = new Date(data.start_date)
    }
    if (data.end_date) {
      form.endDate = new Date(data.end_date)
    }
  } catch (error) {
    ElMessage.error('获取展会详情失败')
  }
}

// 重置表单
const resetForm = () => {
  Object.keys(form).forEach(key => {
    form[key] = key === 'status' ? 0 : ''
  })
  formRef.value?.clearValidate()
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    loading.value = true

    // 转换字段名以匹配数据库字段
    const submitData = {
      name: form.name,
      venue_id: form.venueId,
      category_id: form.categoryId,
      start_date: form.startDate ? new Date(form.startDate).toISOString().split('T')[0] : '',
      end_date: form.endDate ? new Date(form.endDate).toISOString().split('T')[0] : '',
      status: form.status,
      description: form.description
    }

    console.log('提交数据:', submitData)

    if (isEdit.value) {
      await updateExhibition(props.exhibitionId, submitData)
      ElMessage.success('更新成功')
    } else {
      await createExhibition(submitData)
      ElMessage.success('创建成功')
    }

    emit('success')
    handleClose()
  } catch (error) {
    if (error.message) {
      ElMessage.error(error.message)
    }
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  resetForm()
}
</script>
