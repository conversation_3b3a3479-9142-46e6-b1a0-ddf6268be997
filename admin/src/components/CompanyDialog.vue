<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? `编辑${getCompanyTypeName()}` : `查看${getCompanyTypeName()}`"
    width="600px"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      :disabled="!isEdit"
    >
      <el-form-item label="公司名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入公司名称" />
      </el-form-item>
      
      <el-form-item label="联系人" prop="contactPerson">
        <el-input v-model="form.contactPerson" placeholder="请输入联系人" />
      </el-form-item>
      
      <el-form-item label="联系电话" prop="contactPhone">
        <el-input v-model="form.contactPhone" placeholder="请输入联系电话" />
      </el-form-item>
      
      <!-- 设计公司和工厂有城市字段 -->
      <el-form-item v-if="companyType !== 'exhibitor'" label="所在城市" prop="city">
        <el-input v-model="form.city" placeholder="请输入所在城市" />
      </el-form-item>
      
      <!-- 设计公司和工厂有级别字段 -->
      <el-form-item v-if="companyType !== 'exhibitor'" label="级别" prop="level">
        <el-rate v-model="form.level" :max="5" show-text />
      </el-form-item>
      
      <!-- 工厂有特长字段 -->
      <el-form-item v-if="companyType === 'factory'" label="特长" prop="specialties">
        <el-input
          v-model="form.specialties"
          type="textarea"
          :rows="3"
          placeholder="请输入特长，如：钢结构、木结构、特装搭建等"
        />
      </el-form-item>
      
      <!-- 参展商有展会类别字段 -->
      <el-form-item v-if="companyType === 'exhibitor'" label="参展类别" prop="exhibitionCategoryId">
        <el-select v-model="form.exhibitionCategoryId" placeholder="请选择参展类别" style="width: 100%">
          <el-option
            v-for="category in categories"
            :key="category.id"
            :label="category.name"
            :value="category.id"
          />
        </el-select>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">{{ isEdit ? '取消' : '关闭' }}</el-button>
        <el-button v-if="isEdit" type="primary" :loading="loading" @click="handleSubmit">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { getCompanyDetail, updateCompany } from '@/api/admin'
import { getCategories } from '@/api/admin'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  companyType: {
    type: String,
    required: true,
    validator: (value) => ['design', 'factory', 'exhibitor'].includes(value)
  },
  companyId: {
    type: Number,
    default: null
  },
  mode: {
    type: String,
    default: 'view', // 'view' 或 'edit'
    validator: (value) => ['view', 'edit'].includes(value)
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

const visible = ref(false)
const loading = ref(false)
const formRef = ref()
const categories = ref([])

const isEdit = computed(() => props.mode === 'edit')

// 表单数据
const form = reactive({
  name: '',
  contactPerson: '',
  contactPhone: '',
  city: '',
  level: 1,
  specialties: '',
  exhibitionCategoryId: ''
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入公司名称', trigger: 'blur' }
  ]
}

// 获取公司类型名称
const getCompanyTypeName = () => {
  const typeMap = {
    design: '设计公司',
    factory: '工厂',
    exhibitor: '参展商'
  }
  return typeMap[props.companyType] || '公司'
}

// 监听显示状态
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val) {
    if (props.companyId) {
      fetchCompanyDetail()
    }
    if (props.companyType === 'exhibitor') {
      fetchCategories()
    }
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 获取展会类别（仅参展商需要）
const fetchCategories = async () => {
  try {
    const response = await getCategories({ pageSize: 100 })
    categories.value = Array.isArray(response.data) ? response.data : []
  } catch (error) {
    console.error('获取展会类别失败:', error)
  }
}

// 获取公司详情
const fetchCompanyDetail = async () => {
  try {
    const response = await getCompanyDetail(props.companyType, props.companyId)
    const data = response.data
    
    // 映射数据到表单
    form.name = data.name || ''
    form.contactPerson = data.contact_person || ''
    form.contactPhone = data.contact_phone || ''
    
    if (props.companyType !== 'exhibitor') {
      form.city = data.city || ''
      form.level = data.level || 1
    }
    
    if (props.companyType === 'factory') {
      form.specialties = data.specialties || ''
    }
    
    if (props.companyType === 'exhibitor') {
      form.exhibitionCategoryId = data.exhibition_category_id || ''
    }
  } catch (error) {
    ElMessage.error('获取公司详情失败')
  }
}

// 重置表单
const resetForm = () => {
  Object.keys(form).forEach(key => {
    if (key === 'level') {
      form[key] = 1
    } else {
      form[key] = ''
    }
  })
  formRef.value?.clearValidate()
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    loading.value = true
    
    // 转换字段名以匹配数据库字段
    const submitData = {
      name: form.name,
      contact_person: form.contactPerson,
      contact_phone: form.contactPhone
    }
    
    if (props.companyType !== 'exhibitor') {
      submitData.city = form.city
      submitData.level = form.level
    }
    
    if (props.companyType === 'factory') {
      submitData.specialties = form.specialties
    }
    
    if (props.companyType === 'exhibitor') {
      submitData.exhibition_category_id = form.exhibitionCategoryId
    }
    
    await updateCompany(props.companyType, props.companyId, submitData)
    ElMessage.success('更新成功')
    
    emit('success')
    handleClose()
  } catch (error) {
    if (error.message) {
      ElMessage.error(error.message)
    }
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  resetForm()
}
</script>
