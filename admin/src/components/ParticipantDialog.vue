<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑参展信息' : '新增参展信息'"
    width="600px"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="展会" prop="exhibitionId">
        <el-select v-model="form.exhibitionId" placeholder="请选择展会" style="width: 100%">
          <el-option
            v-for="exhibition in exhibitions"
            :key="exhibition.id"
            :label="exhibition.name"
            :value="exhibition.id"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="参展方类型" prop="participantType">
        <el-select 
          v-model="form.participantType" 
          placeholder="请选择参展方类型" 
          style="width: 100%"
          @change="handleTypeChange"
        >
          <el-option label="设计公司" :value="2" />
          <el-option label="工厂" :value="3" />
          <el-option label="综合商" :value="4" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="参展方" prop="participantId">
        <el-select 
          v-model="form.participantId" 
          placeholder="请先选择参展方类型" 
          style="width: 100%"
          :disabled="!form.participantType"
          filterable
        >
          <el-option
            v-for="participant in availableParticipants"
            :key="participant.id"
            :label="`${participant.name} (${participant.contactPerson})`"
            :value="participant.id"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="状态" prop="isFull">
        <el-radio-group v-model="form.isFull">
          <el-radio :label="0">可预约</el-radio>
          <el-radio :label="1">已满额</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  createParticipant, 
  updateParticipant, 
  getParticipantDetail,
  getAvailableParticipants
} from '@/api/admin'
import { getExhibitions } from '@/api/admin'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  participantId: {
    type: Number,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

const visible = ref(false)
const loading = ref(false)
const formRef = ref()
const exhibitions = ref([])
const availableParticipants = ref([])

const isEdit = computed(() => !!props.participantId)

// 表单数据
const form = reactive({
  exhibitionId: '',
  participantType: '',
  participantId: '',
  isFull: 0
})

// 表单验证规则
const rules = {
  exhibitionId: [
    { required: true, message: '请选择展会', trigger: 'change' }
  ],
  participantType: [
    { required: true, message: '请选择参展方类型', trigger: 'change' }
  ],
  participantId: [
    { required: true, message: '请选择参展方', trigger: 'change' }
  ]
}

// 监听显示状态
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val) {
    fetchExhibitions()
    if (props.participantId) {
      fetchParticipantDetail()
    } else {
      resetForm()
    }
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 获取展会列表
const fetchExhibitions = async () => {
  try {
    const response = await getExhibitions({ pageSize: 100 })
    exhibitions.value = response.data.list || []
  } catch (error) {
    console.error('获取展会列表失败:', error)
  }
}

// 获取参展详情
const fetchParticipantDetail = async () => {
  try {
    const response = await getParticipantDetail(props.participantId)
    const data = response.data
    
    form.exhibitionId = data.exhibitionId
    form.participantType = data.participantType
    form.participantId = data.participantId
    form.isFull = data.isFull
    
    // 获取对应类型的参展方列表
    if (data.participantType) {
      await fetchAvailableParticipants(data.participantType)
    }
  } catch (error) {
    ElMessage.error('获取参展详情失败')
  }
}

// 参展方类型改变时
const handleTypeChange = async (type) => {
  form.participantId = '' // 清空已选择的参展方
  if (type) {
    await fetchAvailableParticipants(type)
  } else {
    availableParticipants.value = []
  }
}

// 获取可选参展方列表
const fetchAvailableParticipants = async (type) => {
  try {
    const response = await getAvailableParticipants(type)
    availableParticipants.value = response.data || []
  } catch (error) {
    console.error('获取可选参展方列表失败:', error)
    availableParticipants.value = []
  }
}

// 重置表单
const resetForm = () => {
  Object.keys(form).forEach(key => {
    if (key === 'isFull') {
      form[key] = 0
    } else {
      form[key] = ''
    }
  })
  availableParticipants.value = []
  formRef.value?.clearValidate()
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    loading.value = true
    
    // 转换字段名以匹配数据库字段
    const submitData = {
      exhibition_id: form.exhibitionId,
      participant_type: form.participantType,
      participant_id: form.participantId,
      is_full: form.isFull
    }
    
    if (isEdit.value) {
      await updateParticipant(props.participantId, submitData)
      ElMessage.success('更新成功')
    } else {
      await createParticipant(submitData)
      ElMessage.success('创建成功')
    }
    
    emit('success')
    handleClose()
  } catch (error) {
    if (error.message) {
      ElMessage.error(error.message)
    }
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  resetForm()
}
</script>
