// 全局样式文件

// 重置样式
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 通用类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

// 布局样式
.admin-layout {
  height: 100vh;
  
  .admin-header {
    background: #fff;
    border-bottom: 1px solid #e6e6e6;
    padding: 0 20px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .logo {
      font-size: 20px;
      font-weight: bold;
      color: #409eff;
    }
    
    .user-info {
      display: flex;
      align-items: center;
      gap: 10px;
    }
  }
  
  .admin-sidebar {
    background: #304156;
    height: calc(100vh - 60px);
    overflow-y: auto;
    
    .el-menu {
      border-right: none;
    }
  }
  
  .admin-main {
    background: #f0f2f5;
    height: calc(100vh - 60px);
    overflow-y: auto;
    padding: 20px;
  }
}

// 登录页面样式
.login-container {
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  
  .login-form {
    background: white;
    padding: 40px;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    width: 400px;
    
    .login-title {
      text-align: center;
      margin-bottom: 30px;
      font-size: 24px;
      font-weight: bold;
      color: #333;
    }
    
    .el-form-item {
      margin-bottom: 20px;
    }
    
    .login-button {
      width: 100%;
      margin-top: 10px;
    }
  }
}

// 页面内容样式
.page-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .page-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e6e6e6;
    
    .page-title {
      font-size: 18px;
      font-weight: bold;
      color: #333;
      margin: 0;
    }
  }
  
  .page-content {
    .search-form {
      margin-bottom: 20px;
      padding: 15px;
      background: #f8f9fa;
      border-radius: 6px;
    }
    
    .table-toolbar {
      margin-bottom: 15px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}

// 统计卡片样式
.stats-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
  
  .stats-icon {
    font-size: 40px;
    margin-bottom: 10px;
  }
  
  .stats-title {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
  }
  
  .stats-value {
    font-size: 24px;
    font-weight: bold;
    color: #333;
  }
  
  &.primary {
    .stats-icon {
      color: #409eff;
    }
  }
  
  &.success {
    .stats-icon {
      color: #67c23a;
    }
  }
  
  &.warning {
    .stats-icon {
      color: #e6a23c;
    }
  }
  
  &.danger {
    .stats-icon {
      color: #f56c6c;
    }
  }
}

// 响应式
@media (max-width: 768px) {
  .admin-layout {
    .admin-main {
      padding: 10px;
    }
  }
  
  .login-container {
    padding: 20px;
    
    .login-form {
      width: 100%;
      max-width: 400px;
      padding: 30px 20px;
    }
  }
}
