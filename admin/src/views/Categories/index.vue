<template>
  <div class="page-container">
    <div class="page-header">
      <h3 class="page-title">展会类别管理</h3>
    </div>
    
    <div class="page-content">
      <div class="table-toolbar">
        <div>
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增类别
          </el-button>
        </div>
        
        <div>
          <el-button @click="handleRefresh">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
      
      <el-table v-loading="loading" :data="tableData">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="类别名称" min-width="200" />
        <el-table-column prop="createdAt" label="创建时间" width="160" />
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="warning" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 类别编辑对话框 -->
    <CategoryDialog
      v-model="dialogVisible"
      :category-id="currentCategoryId"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh } from '@element-plus/icons-vue'
import { getCategories, deleteCategory } from '@/api/admin'
import CategoryDialog from '@/components/CategoryDialog.vue'

const tableData = ref([])
const loading = ref(false)

// 对话框状态
const dialogVisible = ref(false)
const currentCategoryId = ref(null)

const fetchCategories = async () => {
  loading.value = true
  try {
    const response = await getCategories({ pageSize: 100 })
    tableData.value = response.data
  } catch (error) {
    ElMessage.error('获取展会类别失败')
    console.error('获取展会类别失败:', error)
  } finally {
    loading.value = false
  }
}

// 新增类别
const handleAdd = () => {
  currentCategoryId.value = null
  dialogVisible.value = true
}

// 编辑类别
const handleEdit = (row) => {
  currentCategoryId.value = row.id
  dialogVisible.value = true
}

// 删除类别
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除类别"${row.name}"吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteCategory(row.id)
    ElMessage.success('删除成功')
    fetchCategories()
  } catch (error) {
    if (error.message) {
      ElMessage.error(error.message)
    }
  }
}

// 对话框成功回调
const handleDialogSuccess = () => {
  fetchCategories()
}

const handleRefresh = () => fetchCategories()

onMounted(() => fetchCategories())
</script>
