<template>
  <div class="dashboard-home">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <div class="stats-card primary">
          <div class="stats-icon">
            <el-icon><User /></el-icon>
          </div>
          <div class="stats-title">用户总数</div>
          <div class="stats-value">{{ stats.userCount }}</div>
        </div>
      </el-col>
      
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <div class="stats-card success">
          <div class="stats-icon">
            <el-icon><Calendar /></el-icon>
          </div>
          <div class="stats-title">展会总数</div>
          <div class="stats-value">{{ stats.exhibitionCount }}</div>
        </div>
      </el-col>
      
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <div class="stats-card warning">
          <div class="stats-icon">
            <el-icon><Shop /></el-icon>
          </div>
          <div class="stats-title">公司总数</div>
          <div class="stats-value">{{ stats.companyCount }}</div>
        </div>
      </el-col>
      
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <div class="stats-card danger">
          <div class="stats-icon">
            <el-icon><OfficeBuilding /></el-icon>
          </div>
          <div class="stats-title">展馆总数</div>
          <div class="stats-value">{{ stats.venueCount }}</div>
        </div>
      </el-col>
    </el-row>
    
    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
        <div class="page-container">
          <div class="page-header">
            <h3 class="page-title">用户类型分布</h3>
          </div>
          <div class="chart-container" id="userTypeChart"></div>
        </div>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
        <div class="page-container">
          <div class="page-header">
            <h3 class="page-title">展会状态分布</h3>
          </div>
          <div class="chart-container" id="exhibitionStatusChart"></div>
        </div>
      </el-col>
    </el-row>
    
    <!-- 最近活动 -->
    <div class="page-container">
      <div class="page-header">
        <h3 class="page-title">最近活动</h3>
      </div>
      <div class="page-content">
        <el-timeline>
          <el-timeline-item
            v-for="activity in recentActivities"
            :key="activity.id"
            :timestamp="activity.time"
            :type="activity.type"
          >
            {{ activity.content }}
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { User, Calendar, Shop, OfficeBuilding } from '@element-plus/icons-vue'

// 统计数据
const stats = ref({
  userCount: 0,
  exhibitionCount: 0,
  companyCount: 0,
  venueCount: 0
})

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    content: '新用户注册：测试用户001',
    time: '2024-01-15 10:30:00',
    type: 'primary'
  },
  {
    id: 2,
    content: '新增展会：2024春季家具展',
    time: '2024-01-15 09:15:00',
    type: 'success'
  },
  {
    id: 3,
    content: '工厂信息更新：深圳XX搭建工厂',
    time: '2024-01-15 08:45:00',
    type: 'warning'
  },
  {
    id: 4,
    content: '设计公司认证通过：上海YY设计',
    time: '2024-01-14 16:20:00',
    type: 'info'
  }
])

// 获取统计数据
const fetchStats = async () => {
  try {
    // TODO: 调用API获取真实统计数据
    // 这里使用模拟数据
    stats.value = {
      userCount: 156,
      exhibitionCount: 23,
      companyCount: 89,
      venueCount: 12
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 初始化图表
const initCharts = () => {
  // TODO: 使用ECharts或其他图表库初始化图表
  // 这里暂时跳过，后续可以添加
}

onMounted(() => {
  fetchStats()
  initCharts()
})
</script>

<style lang="scss" scoped>
.dashboard-home {
  .stats-row {
    margin-bottom: 20px;
  }
  
  .charts-row {
    margin-bottom: 20px;
  }
  
  .chart-container {
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 6px;
    color: #666;
    font-size: 14px;
    
    &::before {
      content: '图表区域（待实现）';
    }
  }
}
</style>
