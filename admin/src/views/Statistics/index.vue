<template>
  <div class="page-container">
    <div class="page-header">
      <h3 class="page-title">数据统计</h3>
    </div>
    
    <div class="page-content">
      <!-- 统计卡片 -->
      <el-row :gutter="20" class="stats-row">
        <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
          <div class="stats-card primary">
            <div class="stats-icon">
              <el-icon><User /></el-icon>
            </div>
            <div class="stats-title">总用户数</div>
            <div class="stats-value">{{ stats.totalUsers }}</div>
          </div>
        </el-col>
        
        <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
          <div class="stats-card success">
            <div class="stats-icon">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="stats-title">总展会数</div>
            <div class="stats-value">{{ stats.totalExhibitions }}</div>
          </div>
        </el-col>
        
        <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
          <div class="stats-card warning">
            <div class="stats-icon">
              <el-icon><Shop /></el-icon>
            </div>
            <div class="stats-title">总公司数</div>
            <div class="stats-value">{{ stats.totalCompanies }}</div>
          </div>
        </el-col>
        
        <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
          <div class="stats-card danger">
            <div class="stats-icon">
              <el-icon><Star /></el-icon>
            </div>
            <div class="stats-title">总收藏数</div>
            <div class="stats-value">{{ stats.totalFavorites }}</div>
          </div>
        </el-col>
      </el-row>
      
      <!-- 图表区域 -->
      <el-row :gutter="20" class="charts-row">
        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <div class="chart-container">
            <h4>用户注册趋势</h4>
            <div class="chart-placeholder">
              用户注册趋势图表（待实现）
            </div>
          </div>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <div class="chart-container">
            <h4>展会参与情况</h4>
            <div class="chart-placeholder">
              展会参与情况图表（待实现）
            </div>
          </div>
        </el-col>
      </el-row>
      
      <!-- 数据表格 -->
      <div class="data-tables">
        <h4>热门展会排行</h4>
        <el-table :data="hotExhibitions" size="small">
          <el-table-column prop="rank" label="排名" width="80" />
          <el-table-column prop="name" label="展会名称" min-width="200" />
          <el-table-column prop="participantCount" label="参展方数量" width="120" />
          <el-table-column prop="viewCount" label="浏览次数" width="120" />
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { User, Calendar, Shop, Star } from '@element-plus/icons-vue'

// 统计数据
const stats = ref({
  totalUsers: 0,
  totalExhibitions: 0,
  totalCompanies: 0,
  totalFavorites: 0
})

// 热门展会
const hotExhibitions = ref([])

// 获取统计数据
const fetchStats = async () => {
  try {
    // TODO: 调用API获取统计数据
    await new Promise(resolve => setTimeout(resolve, 500))
    
    stats.value = {
      totalUsers: 156,
      totalExhibitions: 23,
      totalCompanies: 89,
      totalFavorites: 234
    }
    
    hotExhibitions.value = [
      { rank: 1, name: '第4届上海网络安全博览会', participantCount: 45, viewCount: 1234 },
      { rank: 2, name: 'GPOWER2025动力展', participantCount: 38, viewCount: 987 },
      { rank: 3, name: '2025设计上海展', participantCount: 32, viewCount: 876 }
    ]
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

onMounted(() => {
  fetchStats()
})
</script>

<style lang="scss" scoped>
.stats-row {
  margin-bottom: 20px;
}

.charts-row {
  margin-bottom: 20px;
}

.chart-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  h4 {
    margin: 0 0 15px 0;
    font-size: 16px;
    color: #333;
  }
  
  .chart-placeholder {
    height: 250px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 6px;
    color: #666;
    font-size: 14px;
  }
}

.data-tables {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  h4 {
    margin: 0 0 15px 0;
    font-size: 16px;
    color: #333;
  }
}
</style>
