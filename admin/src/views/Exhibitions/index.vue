<template>
  <div class="page-container">
    <div class="page-header">
      <h3 class="page-title">展会管理</h3>
    </div>
    
    <div class="page-content">
      <!-- 搜索表单 -->
      <div class="search-form">
        <el-form :model="searchForm" inline>
          <el-form-item label="展会名称">
            <el-input
              v-model="searchForm.name"
              placeholder="请输入展会名称"
              clearable
            />
          </el-form-item>
          
          <el-form-item label="展馆">
            <el-select
              v-model="searchForm.venueId"
              placeholder="请选择展馆"
              clearable
            >
              <el-option label="新国际博览中心" :value="1" />
              <el-option label="国家会展中心" :value="2" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="状态">
            <el-select
              v-model="searchForm.status"
              placeholder="请选择状态"
              clearable
            >
              <el-option label="未开始" :value="0" />
              <el-option label="进行中" :value="1" />
              <el-option label="已结束" :value="9" />
            </el-select>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      
      <!-- 表格工具栏 -->
      <div class="table-toolbar">
        <div>
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增展会
          </el-button>
        </div>
        
        <div>
          <el-button @click="handleRefresh">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
      
      <!-- 展会表格 -->
      <el-table v-loading="loading" :data="tableData">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="展会名称" min-width="200" />
        <el-table-column prop="venue" label="展馆" width="150" />
        <el-table-column prop="startDate" label="开始日期" width="120" />
        <el-table-column prop="endDate" label="结束日期" width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusName(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)">
              查看
            </el-button>
            <el-button type="warning" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 展会编辑对话框 -->
    <ExhibitionDialog
      v-model="dialogVisible"
      :exhibition-id="currentExhibitionId"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Plus } from '@element-plus/icons-vue'
import { getExhibitions, deleteExhibition } from '@/api/admin'
import ExhibitionDialog from '@/components/ExhibitionDialog.vue'

// 搜索表单
const searchForm = reactive({
  name: '',
  venueId: '',
  status: ''
})

// 表格数据
const tableData = ref([])
const loading = ref(false)

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 对话框状态
const dialogVisible = ref(false)
const currentExhibitionId = ref(null)

// 获取状态名称
const getStatusName = (status) => {
  const statusMap = {
    0: '未开始',
    1: '进行中',
    9: '已结束'
  }
  return statusMap[status] || '未知'
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  const typeMap = {
    0: 'info',
    1: 'success',
    9: 'danger'
  }
  return typeMap[status] || ''
}

// 获取展会列表
const fetchExhibitions = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      page: pagination.page,
      pageSize: pagination.pageSize
    }

    const response = await getExhibitions(params)
    const { list, total } = response.data

    tableData.value = list
    pagination.total = total
  } catch (error) {
    ElMessage.error('获取展会列表失败')
    console.error('获取展会列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchExhibitions()
}

// 重置
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  handleSearch()
}

// 刷新
const handleRefresh = () => {
  fetchExhibitions()
}

// 新增展会
const handleAdd = () => {
  currentExhibitionId.value = null
  dialogVisible.value = true
}

// 查看展会
const handleView = (row) => {
  ElMessage.info(`查看展会：${row.name}`)
}

// 编辑展会
const handleEdit = (row) => {
  currentExhibitionId.value = row.id
  dialogVisible.value = true
}

// 对话框成功回调
const handleDialogSuccess = () => {
  fetchExhibitions()
}

// 删除展会
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除展会"${row.name}"吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteExhibition(row.id)
    ElMessage.success('删除成功')
    fetchExhibitions()
  } catch (error) {
    if (error.message) {
      ElMessage.error(error.message)
    }
  }
}

// 分页大小变化
const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.page = 1
  fetchExhibitions()
}

// 当前页变化
const handleCurrentChange = (page) => {
  pagination.page = page
  fetchExhibitions()
}

onMounted(() => {
  fetchExhibitions()
})
</script>

<style lang="scss" scoped>
.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>
