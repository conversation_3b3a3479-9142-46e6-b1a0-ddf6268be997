<template>
  <div class="page-container">
    <div class="page-header">
      <h3 class="page-title">展馆管理</h3>
    </div>
    
    <div class="page-content">
      <div class="table-toolbar">
        <div>
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增展馆
          </el-button>
        </div>
        
        <div>
          <el-button @click="handleRefresh">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
      
      <el-table v-loading="loading" :data="tableData">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="city" label="城市" width="100" />
        <el-table-column prop="name" label="展馆名称" min-width="200" />
        <el-table-column prop="address" label="地址" min-width="300" />
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="warning" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 展馆编辑对话框 -->
    <VenueDialog
      v-model="dialogVisible"
      :venue-id="currentVenueId"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh } from '@element-plus/icons-vue'
import { getVenues, deleteVenue } from '@/api/admin'
import VenueDialog from '@/components/VenueDialog.vue'

const tableData = ref([])
const loading = ref(false)

// 对话框状态
const dialogVisible = ref(false)
const currentVenueId = ref(null)

const fetchVenues = async () => {
  loading.value = true
  try {
    const response = await getVenues({ pageSize: 100 })
    tableData.value = response.data
  } catch (error) {
    ElMessage.error('获取展馆列表失败')
    console.error('获取展馆列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 新增展馆
const handleAdd = () => {
  currentVenueId.value = null
  dialogVisible.value = true
}

// 编辑展馆
const handleEdit = (row) => {
  currentVenueId.value = row.id
  dialogVisible.value = true
}

// 删除展馆
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除展馆"${row.name}"吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteVenue(row.id)
    ElMessage.success('删除成功')
    fetchVenues()
  } catch (error) {
    if (error.message) {
      ElMessage.error(error.message)
    }
  }
}

// 对话框成功回调
const handleDialogSuccess = () => {
  fetchVenues()
}

const handleRefresh = () => fetchVenues()

onMounted(() => fetchVenues())
</script>
