<template>
  <div class="page-container">
    <div class="page-header">
      <h3 class="page-title">参展管理</h3>
    </div>
    
    <div class="page-content">
      <div class="search-form">
        <el-form :model="searchForm" inline>
          <el-form-item label="展会">
            <el-select v-model="searchForm.exhibitionId" placeholder="请选择展会" clearable>
              <el-option label="第4届上海网络安全博览会" :value="1" />
              <el-option label="GPOWER2025动力展" :value="2" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="参展方类型">
            <el-select v-model="searchForm.participantType" placeholder="请选择类型" clearable>
              <el-option label="设计公司" :value="2" />
              <el-option label="工厂" :value="3" />
              <el-option label="综合商" :value="4" />
            </el-select>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      
      <div class="table-toolbar">
        <div>
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            添加参展
          </el-button>
        </div>
      </div>
      
      <el-table v-loading="loading" :data="tableData">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="exhibitionName" label="展会名称" min-width="200" />
        <el-table-column prop="participantName" label="参展方" min-width="150" />
        <el-table-column prop="participantType" label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.participantType)">
              {{ getTypeName(row.participantType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="isFull" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.isFull ? 'danger' : 'success'">
              {{ row.isFull ? '已满额' : '可预约' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)">
              查看
            </el-button>
            <el-button type="warning" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 参展编辑对话框 -->
    <ParticipantDialog
      v-model="dialogVisible"
      :participant-id="currentParticipantId"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Plus } from '@element-plus/icons-vue'
import { getParticipants, deleteParticipant } from '@/api/admin'
import ParticipantDialog from '@/components/ParticipantDialog.vue'

const searchForm = reactive({
  exhibitionId: '',
  participantType: ''
})

const tableData = ref([])
const loading = ref(false)

// 对话框状态
const dialogVisible = ref(false)
const currentParticipantId = ref(null)

const getTypeName = (type) => {
  const typeMap = {
    2: '设计公司',
    3: '工厂',
    4: '综合商'
  }
  return typeMap[type] || '未知'
}

const getTypeTagType = (type) => {
  const typeMap = {
    2: 'success',
    3: 'warning',
    4: 'danger'
  }
  return typeMap[type] || ''
}

const fetchParticipants = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      page: 1,
      pageSize: 100
    }

    const response = await getParticipants(params)
    const { list } = response.data

    tableData.value = list
  } catch (error) {
    ElMessage.error('获取参展列表失败')
    console.error('获取参展列表失败:', error)
  } finally {
    loading.value = false
  }
}

const handleSearch = () => fetchParticipants()
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  fetchParticipants()
}
// 新增参展
const handleAdd = () => {
  currentParticipantId.value = null
  dialogVisible.value = true
}

// 查看参展
const handleView = (row) => {
  ElMessage.info(`查看参展：${row.participantName}`)
}

// 编辑参展
const handleEdit = (row) => {
  currentParticipantId.value = row.id
  dialogVisible.value = true
}

// 删除参展
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除参展信息"${row.participantName}"吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteParticipant(row.id)
    ElMessage.success('删除成功')
    fetchParticipants()
  } catch (error) {
    if (error.message) {
      ElMessage.error(error.message)
    }
  }
}

// 对话框成功回调
const handleDialogSuccess = () => {
  fetchParticipants()
}

onMounted(() => fetchParticipants())
</script>
