<template>
  <div class="page-container">
    <div class="page-header">
      <h3 class="page-title">公司管理</h3>
    </div>
    
    <div class="page-content">
      <div class="search-form">
        <el-form :model="searchForm" inline>
          <el-form-item label="公司类型">
            <el-select v-model="searchForm.type" placeholder="请选择公司类型" clearable>
              <el-option label="设计公司" value="design" />
              <el-option label="工厂" value="factory" />
              <el-option label="参展商" value="exhibitor" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="公司名称">
            <el-input v-model="searchForm.name" placeholder="请输入公司名称" clearable />
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      
      <el-table v-loading="loading" :data="tableData">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="公司名称" min-width="200" />
        <el-table-column prop="type" label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.type)">
              {{ getTypeName(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="contactPerson" label="联系人" width="100" />
        <el-table-column prop="contactPhone" label="联系电话" width="130" />
        <el-table-column prop="city" label="城市" width="100" />
        
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)">
              查看
            </el-button>
            <el-button type="warning" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 公司详情/编辑对话框 -->
    <CompanyDialog
      v-model="dialogVisible"
      :company-type="currentCompanyType"
      :company-id="currentCompanyId"
      :mode="dialogMode"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'
import { getCompanies, deleteCompany } from '@/api/admin'
import CompanyDialog from '@/components/CompanyDialog.vue'

const searchForm = reactive({
  type: '',
  name: ''
})

const tableData = ref([])
const loading = ref(false)

// 对话框状态
const dialogVisible = ref(false)
const currentCompanyType = ref('')
const currentCompanyId = ref(null)
const dialogMode = ref('view') // 'view' 或 'edit'

const getTypeName = (type) => {
  const typeMap = {
    design: '设计公司',
    factory: '工厂',
    exhibitor: '参展商'
  }
  return typeMap[type] || '未知'
}

const getTypeTagType = (type) => {
  const typeMap = {
    design: 'success',
    factory: 'warning',
    exhibitor: ''
  }
  return typeMap[type] || ''
}

const fetchCompanies = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      page: 1,
      pageSize: 100
    }

    const response = await getCompanies(params)
    const { list } = response.data

    tableData.value = list
  } catch (error) {
    ElMessage.error('获取公司列表失败')
    console.error('获取公司列表失败:', error)
  } finally {
    loading.value = false
  }
}

const handleSearch = () => fetchCompanies()

const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  fetchCompanies()
}

// 查看公司
const handleView = (row) => {
  currentCompanyType.value = row.type
  currentCompanyId.value = row.id
  dialogMode.value = 'view'
  dialogVisible.value = true
}

// 编辑公司
const handleEdit = (row) => {
  currentCompanyType.value = row.type
  currentCompanyId.value = row.id
  dialogMode.value = 'edit'
  dialogVisible.value = true
}

// 删除公司
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除公司"${row.name}"吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteCompany(row.type, row.id)
    ElMessage.success('删除成功')
    fetchCompanies()
  } catch (error) {
    if (error.message) {
      ElMessage.error(error.message)
    }
  }
}

// 对话框成功回调
const handleDialogSuccess = () => {
  fetchCompanies()
}

onMounted(() => fetchCompanies())
</script>
