#!/usr/bin/env node

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 启动vite开发服务器
const vitePath = join(__dirname, 'node_modules', 'vite', 'bin', 'vite.js');

console.log('🚀 启动Vue后台管理系统...');
console.log('📁 当前目录:', __dirname);
console.log('🔧 Vite路径:', vitePath);

// 确保在正确的目录中运行
process.chdir(__dirname);

const viteProcess = spawn('node', [vitePath, '--port', '5174'], {
  cwd: __dirname,
  stdio: 'inherit',
  env: { ...process.env, PWD: __dirname }
});

viteProcess.on('error', (error) => {
  console.error('❌ 启动失败:', error);
});

viteProcess.on('close', (code) => {
  console.log(`🔚 进程退出，代码: ${code}`);
});

// 处理进程退出
process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭服务器...');
  viteProcess.kill('SIGINT');
  process.exit(0);
});

process.on('SIGTERM', () => {
  viteProcess.kill('SIGTERM');
  process.exit(0);
});
