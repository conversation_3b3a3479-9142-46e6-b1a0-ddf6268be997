#!/usr/bin/env node

// 简单的API测试脚本
const axios = require('axios').default || require('axios');

const BASE_URL = 'http://localhost:3001';

async function testAPI() {
  console.log('🧪 开始测试API...\n');

  try {
    // 1. 测试健康检查
    console.log('1. 测试健康检查...');
    const healthResponse = await axios.get(`${BASE_URL}/health`);
    console.log('✅ 健康检查通过:', healthResponse.data.message);
    console.log('   服务器运行时间:', Math.floor(healthResponse.data.data.uptime), '秒\n');

    // 2. 测试管理员登录
    console.log('2. 测试管理员登录...');
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/admin-login`, {
      username: 'admin',
      password: 'gpp13141234'
    });
    console.log('✅ 管理员登录成功');
    const token = loginResponse.data.data.token;
    console.log('   获取到Token:', token.substring(0, 20) + '...\n');

    // 3. 测试获取统计数据
    console.log('3. 测试获取统计数据...');
    const statsResponse = await axios.get(`${BASE_URL}/api/admin/stats`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    console.log('✅ 统计数据获取成功:');
    console.log('   用户总数:', statsResponse.data.data.userCount);
    console.log('   展会总数:', statsResponse.data.data.exhibitionCount);
    console.log('   公司总数:', statsResponse.data.data.companyCount);
    console.log('   展馆总数:', statsResponse.data.data.venueCount, '\n');

    // 4. 测试获取用户列表
    console.log('4. 测试获取用户列表...');
    const usersResponse = await axios.get(`${BASE_URL}/api/admin/users?page=1&pageSize=5`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    console.log('✅ 用户列表获取成功:');
    console.log('   总用户数:', usersResponse.data.data.pagination.total);
    console.log('   当前页用户数:', usersResponse.data.data.list.length);
    if (usersResponse.data.data.list.length > 0) {
      console.log('   第一个用户:', usersResponse.data.data.list[0].nickname, '\n');
    }

    // 5. 测试获取展会列表
    console.log('5. 测试获取展会列表...');
    const exhibitionsResponse = await axios.get(`${BASE_URL}/api/admin/exhibitions?page=1&pageSize=5`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    console.log('✅ 展会列表获取成功:');
    console.log('   总展会数:', exhibitionsResponse.data.data.pagination.total);
    console.log('   当前页展会数:', exhibitionsResponse.data.data.list.length);
    if (exhibitionsResponse.data.data.list.length > 0) {
      console.log('   第一个展会:', exhibitionsResponse.data.data.list[0].name, '\n');
    }

    // 6. 测试获取公司列表
    console.log('6. 测试获取公司列表...');
    const companiesResponse = await axios.get(`${BASE_URL}/api/admin/companies?page=1&pageSize=5`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    console.log('✅ 公司列表获取成功:');
    console.log('   总公司数:', companiesResponse.data.data.pagination.total);
    console.log('   当前页公司数:', companiesResponse.data.data.list.length);
    if (companiesResponse.data.data.list.length > 0) {
      console.log('   第一个公司:', companiesResponse.data.data.list[0].name, '\n');
    }

    console.log('🎉 所有API测试通过！');
    console.log('\n📋 测试总结:');
    console.log('✅ 健康检查 - 通过');
    console.log('✅ 管理员登录 - 通过');
    console.log('✅ 统计数据API - 通过');
    console.log('✅ 用户管理API - 通过');
    console.log('✅ 展会管理API - 通过');
    console.log('✅ 公司管理API - 通过');
    console.log('\n🚀 现在可以访问后台管理系统: http://localhost:5174');

  } catch (error) {
    console.error('❌ API测试失败:');
    if (error.response) {
      console.error('   状态码:', error.response.status);
      console.error('   错误信息:', error.response.data?.message || error.response.statusText);
    } else if (error.request) {
      console.error('   网络错误: 无法连接到服务器');
      console.error('   请确保后端服务已启动 (npm run dev)');
    } else {
      console.error('   错误:', error.message);
    }
    process.exit(1);
  }
}

// 检查是否安装了axios
try {
  require.resolve('axios');
} catch (e) {
  console.log('📦 正在安装axios...');
  require('child_process').execSync('npm install axios', { stdio: 'inherit' });
}

testAPI();
